package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionDetailQueryDTO extends BaseDto {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2494")
    private Long id;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String prescriptionPref;

    @Schema(description = "门店id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productPref;

    @Schema(description = "标准库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8400")
    private String standardId;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String productName;

    @Schema(description = "通用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String commonName;

    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String approvalNumber;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prescriptionType;

    @Schema(description = "处方编号集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> prescriptionPrefList;

}
