package com.xyy.saas.inquiry.hospital.server.service.reception;

import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_NO_DOCTOR_RECEPTION;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorFilterChainEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorOperatFloorStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.GrabModelEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.exception.MqConsumerLaterException;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorOperatFloorResultVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.convert.inquiry.InquiryConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.dal.redis.hospital.HospitalRedisDao;
import com.xyy.saas.inquiry.hospital.server.dal.redis.inquiry.HospitalInquiryRedisDao;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionAutoIssueEvent;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionAutoIssueProducer;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.chain.DoctorFilterChain;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.strategy.distribute.DistributeInquiryStrategy;
import com.xyy.saas.inquiry.hospital.server.service.strategy.distribute.ManualInquiryDistributeStrategy;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.MathUtil;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Desc 医院接诊大厅
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/5 下午2:23
 */
@Service
@Slf4j
public class HospitalReceptionAreaServiceImpl implements HospitalReceptionAreaService {

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private HospitalRedisDao hospitalRedisDao;

    @Resource
    private HospitalInquiryRedisDao hospitalInquiryRedisDao;

    @Resource
    private PrescriptionService prescriptionService;

    @Autowired
    private Map<String, DoctorFilterChain> doctorFilterMap;

    @Resource
    private PrescriptionAutoIssueProducer prescriptionAutoIssueProducer;

    @Resource
    private DoctorImService doctorImService;

    private HospitalReceptionAreaServiceImpl getSelf() {
        return SpringUtil.getBean(this.getClass());
    }


    /**
     * 自动开方判定策略
     */
    private static final Map<Integer, DistributeInquiryStrategy> distributeStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<DistributeInquiryStrategy> strategies) {
        strategies.forEach(strategy -> distributeStrategyMap.put(strategy.getInquiryType().getCode(), strategy));
    }

    @PostConstruct
    public void afterPostConstruct() {
        for (DoctorFilterChainEnum process : DoctorFilterChainEnum.values()) {
            DoctorFilterChain dispatch = doctorFilterMap.get(process.getNode());
            if (null == dispatch) {
                continue;
            }
            if (StringUtils.isBlank(process.getNext())) {
                continue;
            }
            dispatch.setNextNode(doctorFilterMap.get(process.getNext()));
        }
    }


    /**
     * 给问诊分配医生
     *
     * @param inquiryRecordDto 问诊单信息
     */
    @Override
    @TraceNode(node = TraceNodeEnum.MATCH_DOCTOR , prefLocation = "inquiryRecordDto.pref")
    public List<String> distributeDoctorForInquiry(InquiryRecordDto inquiryRecordDto) {
        log.info("接诊大厅开始调度问诊单，单号：{}", inquiryRecordDto.getPref());
        // 判断问诊单状态
        if (InquiryStatusEnum.QUEUING.getStatusCode() != inquiryRecordDto.getInquiryStatus()) {
            return List.of();
        }
        // 查询租户信息
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 给问诊单设置租户信息-后面会用
        InquiryConvert.INSTANCE.convertTenantParam(inquiryRecordDto, tenantDto);
        // 获取可调度医生列表 以及 调度模式
        List<String> doctorList = getCanReceptionDoctorList(inquiryRecordDto);
        log.info("问诊单号：{},接诊大厅分配的医生：{}", inquiryRecordDto.getPref(), JSON.toJSONString(doctorList));
        // 未匹配到医生直接返回
        if (CollectionUtils.isEmpty(doctorList)) {
            throw new MqConsumerLaterException(INQUIRY_NO_DOCTOR_RECEPTION);
        }
        // 执行派单
        distributeStrategyMap.get(inquiryRecordDto.getAutoInquiry()).distributeInquiry(inquiryRecordDto, doctorList);
        return doctorList;
    }


    /**
     * 根据可接诊医院、科室、查询可接诊医生
     * @param inquiryDto
     * @return
     */
    private List<String> getCanReceptionDoctorList(InquiryRecordDto inquiryDto) {
        // 指定自动抢单模式为-派单优先
        inquiryDto.setGrabModelEnum(GrabModelEnum.DISPATCH_FIRST);
        List<String> doctorList = new ArrayList<>();
        HospitalDeptDto hospitalDeptDto = inquiryDto.getHospitalDeptDto();
        // 当前调度的科室列表
        List<Dept> deptList = hospitalDeptDto.getSortInquiryDeptList();

        for(int i = 0 ; i < deptList.size(); i ++){
            // 跳过已经派单的科室
            if(i< hospitalDeptDto.getCurrentDispatchDeptIndex()){
                continue;
            }
            Dept dept = deptList.get(i);
            // 将问诊单号写入当前调度的接诊大厅
            hospitalInquiryRedisDao.pushInquiryToReactionArea(inquiryDto,dept.getDeptPref());
            // 获取当前科室对应医生队列的key
            List<String> doctors = doctorRedisDao.getWaitDoctorList(inquiryDto.getHospitalPref(), dept.getDeptPref(), inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType(), inquiryDto.getEnv());
            log.info("问诊单号：{},接诊大厅可接诊医生：{}", inquiryDto.getPref(), JSON.toJSONString(doctorList));
            doctorFilterMap.get(DoctorFilterChainEnum.MEDICARE_DOCTOR_FILTERCHAIN.getNode()).doFilter(inquiryDto, doctors);
            // 正常获取到医生列表直接返回
            if(!CollectionUtils.isEmpty(doctors)){
                doctorList = doctors;
                break;
            }
            // 递增调度科室索引
            hospitalDeptDto.incrementCurrentDispatchDeptIndex();
            // 最后一次循环时重置当前调度科室索引
            if(i == deptList.size() - 1){
                hospitalDeptDto.restDispatchDeptIndex();
            }
        }
        return doctorList;
    }


    /**
     * 查询接诊大厅医生工作台问诊单列表
     *
     * @param status 状态
     * @return 接诊大厅医生工作台问诊单列表
     */
    @Override
    public DoctorOperatFloorResultVO getDoctorOperatFloorList(Integer status) {
        // 根据当前用户查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(getLoginUserId());
        // 查询当前医生可接诊队列
        List<String> canReceptionList = doctorRedisDao.getDoctorCanReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        // 查询当前医生接诊中队列
        List<String> receptionList = doctorRedisDao.getDoctorReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        // 检查状态并返回
        return checkStatusForDoctorOperatFloor(status,canReceptionList,receptionList,doctorDO);
    }

    /**
     *  检查状态并返回
     * @param status 当前查询的状态
     * @param canReceptionList 可接诊列表
     * @param receptionList 接诊中列表
     * @param doctorDO 医生信息
     * @return
     */
    private DoctorOperatFloorResultVO checkStatusForDoctorOperatFloor(Integer status, List<String> canReceptionList, List<String> receptionList,InquiryDoctorDO doctorDO) {
        // 根据条件查询问诊列表
        List<String> resultList = DoctorOperatFloorStatusEnum.WAIT_RECEIPT.getStatus().equals(status) ? canReceptionList : receptionList;
        if (CollectionUtils.isEmpty(resultList)) {
            return DoctorOperatFloorResultVO.builder().acceptNum(receptionList.size()).waitNum(canReceptionList.size()).inquiryList(List.of()).build();
        }
        // 查询问诊列表
        List<InquiryRecordDto> recordDtos = inquiryApi.getInquiryRecordList(InquiryQueryDto.builder().prefs(resultList.stream().distinct().toList()).build());
        // 将问诊单转为map结构
        Map<String,  InquiryRecordDto> recordDtoMap = recordDtos.stream().collect(Collectors.toMap(InquiryRecordDto::getPref, recordDto -> recordDto,(k1,k2) ->k2));
        // 根据当前查询的类型来定义本次检查的单据状态
        Integer checkStatus = DoctorOperatFloorStatusEnum.RECEIPTING.getStatus().equals(status) ? InquiryStatusEnum.INQUIRING.getStatusCode() : InquiryStatusEnum.QUEUING.getStatusCode();
        resultList.removeIf(inquiryPref -> {
            InquiryRecordDto recordDto = recordDtoMap.get(inquiryPref);
            if (ObjectUtil.isEmpty(recordDto) || ObjectUtil.notEqual(recordDto.getInquiryStatus(), checkStatus)) {
                doctorRedisDao.removeInquiryFromDoctorReception(checkStatus,doctorDO.getPref(), doctorDO.getEnvTag(), inquiryPref);
                recordDtoMap.remove(inquiryPref);
                return true;  // 返回 true 表示需要移除该元素
            }
            return false;
        });
        return DoctorOperatFloorResultVO.builder().acceptNum(receptionList.size()).waitNum(canReceptionList.size()).inquiryList(InquiryConvert.INSTANCE.convertDTO2VO(recordDtos.stream()
            .filter(recordDto -> recordDtoMap.containsKey(recordDto.getPref()))
            .toList())).build();
    }

    /**
     * 医生从接诊大厅拉取问诊单
     *
     * @param doctor 医生信息
     */
    @Override
    public void doctorPullInquiryFromReceptionArea(InquiryDoctorDO doctor) {
        // 查询当前医生真人接诊大厅列表
        List<String> receptionAreaKeyList = doctorRedisDao.getDoctorReceptionAreaKeyList(doctor.getPref(), DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode());
        if (CollectionUtils.isEmpty(receptionAreaKeyList)) {
            return;
        }
        // 获取队列长度最长的
        String maxKey = getMaxKey(receptionAreaKeyList);
        // 从队列中获取问诊单
        List<String> inquiryPrefs = hospitalRedisDao.getInquiryListByRecetionArea(maxKey);
        if (CollectionUtils.isEmpty(inquiryPrefs)) {
            return;
        }
        // 因为是leftPush，所以需要反转，从尾部开始遍历
        Collections.reverse(inquiryPrefs);
        // 循环问诊大厅的队列，进行派单，成功即停
        for (String inquiryPref : inquiryPrefs) {
            if (distributeInquiry(inquiryPref, doctor,maxKey)) {
                // 派单成功直接return
                return;
            }
        }
    }

    /**
     * 视频问诊完成后自动开方调度
     *
     * @param inquiryPref 问诊单号
     * @return 调度结果
     */
    @Override
    public void autoVideoDispatch(String inquiryPref) {
        // 查询问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(inquiryPref);
        // 判断问诊单状态
        if (InquiryStatusEnum.INQUIRING.getStatusCode() != inquiryRecordDto.getInquiryStatus()) {
            return;
        }
        prescriptionAutoIssueProducer.sendMessage(PrescriptionAutoIssueEvent.builder().msg(inquiryRecordDto.getPref()).build(), LocalDateTime.now().plusSeconds(MathUtil.getRandomNumber(3, 6)));
    }


    /**
     * 把问诊派单给抢单医生
     *
     * @param inquiryPref 问诊单号
     * @param doctor  医生信息
     * @param recetionAreaKey 接诊大厅key
     * @return 是否派单成功
     */
    private boolean distributeInquiry(String inquiryPref, InquiryDoctorDO doctor, String recetionAreaKey) {
        // 查询问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryDtoByPref(inquiryPref);
        if (ObjectUtils.isEmpty(inquiryRecordDto) || ObjectUtil.notEqual(inquiryRecordDto.getInquiryStatus(), InquiryStatusEnum.QUEUING.getStatusCode())) {
            // 同步将问诊单从接诊大厅移除
            hospitalRedisDao.removeInquiryFromRecetionArea(recetionAreaKey, inquiryPref);
            return Boolean.FALSE;
        }
        // redis 查询当前问诊单有没有被其他医生接诊
        if (StringUtils.isNotBlank(hospitalInquiryRedisDao.getInquiryCurrentDoctor(inquiryPref))) {
            return Boolean.FALSE;
        }
        return getSelf().autoGrabInquiryForInquiryEnd(inquiryPref, doctor, inquiryRecordDto);
    }


    /**
     * 自动抢单医生派单
     * @param inquiryPref
     * @param doctor
     * @param inquiryRecordDto
     * @return
     */
    @TraceNode(node = TraceNodeEnum.DOCTOR_AUTOAUTOGRAB_FOR_INQUIRY_END , prefLocation = "inquiryPref")
    public Boolean autoGrabInquiryForInquiryEnd(String inquiryPref,InquiryDoctorDO doctor, InquiryRecordDto inquiryRecordDto) {
        // 查询租户信息
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 给问诊单设置租户信息-后面会用
        InquiryConvert.INSTANCE.convertTenantParam(inquiryRecordDto, tenantDto);
        // 写入调度派单缓存
        doctorRedisDao.onSendInquiry(inquiryPref, Collections.singletonList(doctor.getPref()), inquiryRecordDto.getEnv());
        // 抢单
        log.info("问诊单pref:{}，开始自动抢单", inquiryRecordDto.getPref());
        CommonResult<?> result = prescriptionService.grabbingPrescriptionByDoctor(PrescriptionGrabbingVO.builder().inquiryPref(inquiryPref).doctorPref(doctor.getPref()).autoGrabStatus(AutoGrabStatusEnum.OPEN.getCode()).build());
        if (result.isSuccess()) {
            // 写入医生自动抢单缓存
            doctorRedisDao.pushDoctorCurrentAutoGrabList(doctor.getPref(), doctor.getEnvTag(), inquiryRecordDto.getPref());
            log.info("问诊单pref:{}，自动抢单成功", inquiryRecordDto.getPref());
            doctorImService.batchPushNotifyMessage(Collections.singletonList(doctor.getPref()), PushContentEnum.GRAB_ORDER , inquiryRecordDto);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 从接诊大厅中选取队列最长的
     *
     * @param receptionAreaKeyList 接诊大厅列表
     * @return 接诊大厅列表中队列最长的key
     */
    private String getMaxKey(List<String> receptionAreaKeyList) {
        String maxKey = "";
        int max = -1;
        for (int i = 0; i < receptionAreaKeyList.size(); i++) {
            if (RedisUtils.lSize(receptionAreaKeyList.get(i)) > max) {
                max = (int) RedisUtils.lSize(receptionAreaKeyList.get(i));
                maxKey = receptionAreaKeyList.get(i);
            }
        }
        return maxKey;
    }
}
