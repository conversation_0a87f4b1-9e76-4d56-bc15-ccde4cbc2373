package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 处方记录新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionSaveReqVO {

    @Schema(description = "处方ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27739")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    @NotNull(message = "互联网医院编码不能为空")
    private String hospitalPref;

    @Schema(description = "处方笺模版id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21304")
    @NotNull(message = "处方笺模版id")
    private Long preTempId;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "科室编码不能为空")
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "科室名称不能为空")
    private String deptName;

    @Schema(description = "问诊编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "32287")
    private String inquiryPref;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30337")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "患者姓名不能为空")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者性别：1 男 2 女不能为空")
    private Integer patientSex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者年龄不能为空")
    private String patientAge;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "患者手机号不能为空")
    private String patientMobile;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    @NotNull(message = "医生编码不能为空")
    private String doctorPref;

    @Schema(description = "药师编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String pharmacistPref;

    @Schema(description = "药师名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String pharmacistName;

    @Schema(description = "是否自动开方：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer autoInquiry;

    @Schema(description = "处方审核级数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer auditLevel;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "处方状态不能为空")
    private Integer status;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;

    @Schema(description = "处方分发状态 0-未分配,1-已分配", example = "2")
    private Integer distributeStatus;

    @Schema(description = "处方分配的用户id", example = "2")
    private Long distributeUserId;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "用药类型不能为空")
    private Integer medicineType;

    @Schema(description = "使用状态：0 初始 1可用 2已用 3过期  4失效", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer useStatus;

    @Schema(description = "失效原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    private String invalidReason;

    @Schema(description = "失效时间")
    private LocalDateTime invalidTime;

    @Schema(description = "费别", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String feeType;

    @Schema(description = "问诊开始时间")
    @NotNull(message = "问诊开始时间不能为空")
    private LocalDateTime inquiryStartTime;

    @Schema(description = "问诊结束时间")
    private LocalDateTime inquiryEndTime;

    @Schema(description = "医师出方时间")
    @NotNull(message = "医师出方时间不能为空")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "药师审方时间")
    private LocalDateTime auditPrescriptionTime;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "主诉", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> mainSuit;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> diagnosisName;

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionPdfUrl;

    @Schema(description = "病历img图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String caseImgUrl;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问诊方式不能为空")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问诊业务类型不能为空")
    private Integer inquiryBizType;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prescriptionType;


    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer clientChannelType;

    @Schema(description = "医生客户端类型 0：未知，10：微信小程序，11：微信公众号，20：H5 网页，31：手机 App", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer doctorChannelType;

    @Schema(description = "医生操作系统类型 0：未知，1：Android，2：iOS，3：Windows，4：Mac，5：Linux", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String doctorOsType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "问诊渠道不能为空")
    private Integer bizChannelType;

    @Schema(description = "处方打印状态（0-未打印、1-已打印、NULL -未知）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer printStatus;

    @Schema(description = "签章平台 0-自绘 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer signPlatform;

    @Schema(description = "处方拓展字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private PrescriptionExtDto ext;

    @Schema(description = "处方详情集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InquiryPrescriptionDetailSaveReqVO> inquiryPrescriptionDetailList;

}