package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 处方记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPrescriptionExcelRespVO {

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "患者性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer patientSex;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者手机号")
    private String patientMobile;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    @ExcelProperty("处方编号")
    private String pref;

    @Schema(description = "三方处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方处方编号")
    private String thirdPrescriptionNo;


    @Schema(description = "医师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("医师姓名")
    private String doctorName;

    @Schema(description = "医师开方时间")
    @ExcelProperty("医师开方时间")
    private String outPrescriptionTimeStr;

    @Schema(description = "药师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    @ExcelProperty("药师姓名")
    private String pharmacistName;

    @Schema(description = "药师审方时间")
    @ExcelProperty("药师审方时间")
    private String auditPrescriptionTimeStr;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "用药类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRESCRIPTION_MEDICINE_TYPE)
    private Integer medicineType;

    @Schema(description = "处方划价总价")
    @ExcelProperty("金额/元")
    private String pricingPriceStr;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "处方状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRESCRIPTION_STATUS)
    private Integer status;

    @Schema(description = "处方打印状态（0-未打印、1-已打印、NULL -未知）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "打印状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRESCRIPTION_PRINT_STATUS)
    private Integer printStatus;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "问诊方式", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_WAY_TYPE)
    private Integer inquiryWayType;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "处方类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRESCRIPTION_TYPE)
    private Integer prescriptionType;

    @Schema(description = "处方来源 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "处方来源", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_BIZ_TYPE)
    private Integer inquiryBizType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "客户端渠类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.CLIENT_CHANNEL_TYPE)
    private Integer clientChannelType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "问诊渠道", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.BIZ_CHANNEL_TYPE)
    private Integer bizChannelType;

    @Schema(description = "通用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("药品名称")
    private String commonName;

    @Schema(description = "商品规格", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规格")
    private String attributeSpecification;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @Schema(description = "用药方法eg:口服", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用法")
    private String directions;

    @Schema(description = "单次剂量 eg:1", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单次用量")
    private String singleDose;

    @Schema(description = "单次剂量单位 eg:片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("剂量单位")
    private String singleUnit;

    @Schema(description = "使用频率 eg:一日三次", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("频次")
    private String useFrequency;

    // ----------- 以下为中药信息 ----------

    @Schema(description = "使用频率 eg:一日三次", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("副数(中药)")
    private String tcmDosageCombinationDesc;

    /**
     * 用法 eg: 温服
     */
    @Schema(description = "中药用法 eg: 温服")
    @ExcelProperty("用法(中药)")
    private String tcmDirections;

    /**
     * 加工方法 eg: 打粉冲服
     */
    @Schema(description = "中药加工方法 eg: 打粉冲服")
    @ExcelProperty("加工方式(中药)")
    private String tcmProcessingMethod;

    // ----------- 以上为中药信息 ----------

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String tenantName;


    @Schema(description = "处方废弃操作人")
    private String abandonUser;

    @Schema(description = "处方废弃时间")
    private String abandonTime;

    @Schema(description = "处方废弃原因")
    private String abandonReason;


}
