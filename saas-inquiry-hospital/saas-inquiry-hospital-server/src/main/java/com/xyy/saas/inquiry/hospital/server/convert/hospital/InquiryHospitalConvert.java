package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import java.util.List;
import java.util.stream.Stream;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:53
 */
@Mapper
public interface InquiryHospitalConvert {

    InquiryHospitalConvert INSTANCE = Mappers.getMapper(InquiryHospitalConvert.class);

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getHospitalPref())")
    InquiryHospitalDO initConvertVO2DO(InquiryHospitalSaveReqVO reqVO);

    InquiryHospitalDO convertVO2DO(InquiryHospitalSaveReqVO reqVO);

    InquiryHospitalRespDto convertDo2Dto(InquiryHospitalDO inquiryHospital);

    List<InquiryHospitalRespDto> convertDo2Dtos(List<InquiryHospitalDO> inquiryHospitalDOS);

    InquiryHospitalRespVO convertDto2RespVO(InquiryHospitalRespDto dto);

    List<InquiryHospitalRespVO> convertDtos2VOs(List<InquiryHospitalRespDto> dtoList);

    default ConditionParamDto convertConditionParam(TenantDto tenantDto, InquiryRecordDto inquiryRecordDto, InquiryRecordDetailDto inquiryRecordDetailDto) {
        return ConditionParamDto.builder()
            .age(inquiryRecordDto.getPatientAge())
            .dept(inquiryRecordDto.getDeptPref())
            .medicineType(inquiryRecordDto.getMedicineType())
            .slowDisease(inquiryRecordDetailDto.getSlowDisease())
            .areaCodes(Stream.of(tenantDto.getProvinceCode(), tenantDto.getCityCode(), tenantDto.getAreaCode()).toList())
            .prescriptionType(inquiryRecordDetailDto.getPrescriptionType())
            .build();

    }
}
