package com.xyy.saas.inquiry.hospital.server.dal.redis.doctor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorOperatFloorStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.hospital.server.constant.DoctorConstant;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.redis.HospitalBaseRedisDao;
import com.xyy.saas.inquiry.hospital.server.util.DateUtil;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * @Author: xucao
 * @Date: 2024/12/18 10:59
 * @Description: 医生相关redis 操作类
 */
@Repository
@Slf4j
public class DoctorRedisDao extends HospitalBaseRedisDao {

    /**
     * 将医生推入待调度池
     *
     * @param hospitalPref   医院编码
     * @param deptPref       科室编码
     * @param autoInquiry    自动开方标识
     * @param inquiryWayType 问诊方式
     * @param env            环境
     * @param doctorPref     医生编码
     */
    public void doctorToWaitPool(String hospitalPref, String deptPref, Integer autoInquiry, Integer inquiryWayType, String env, String doctorPref) {
        RedisUtils.lPush(RedisKeyConstants.getDoctorWaitPoolKey(hospitalPref, deptPref, autoInquiry, inquiryWayType, env), doctorPref);
    }

    /**
     * 查询指定调度池的所有医生
     *
     * @param hospitalPref   医院编码
     * @param deptPref       科室编码
     * @param autoInquiry    自动开方标识
     * @param inquiryWayType 问诊方式
     * @param env            环境
     */
    public List<String> getWaitDoctorList(String hospitalPref, String deptPref, Integer autoInquiry, Integer inquiryWayType, String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorWaitPoolKey(hospitalPref, deptPref, autoInquiry, inquiryWayType, env)).stream().map(Object::toString).collect(Collectors.toList()));
    }


    public Integer getNextAtomicIntNumber(InquiryRecordDto inquiryDto, Long max) {
        String key = RedisKeyConstants.getDoctorLoadbalancKey(inquiryDto.getHospitalPref(), inquiryDto.getDeptPref(), inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType(), inquiryDto.getEnv());
        Long increment = RedisUtils.incrementKey(key);
        if (increment > max) {
            RedisUtils.set(key, 1);
            return 0;
        }
        return increment.intValue();
    }


    /**
     * 获取医生可接诊问诊列表
     *
     * @param doctorPref 医生编码
     * @param env        环境
     * @return 医生可接诊问诊列表
     */
    public List<String> getDoctorCanReceptionList(String doctorPref, String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorCanReceptionKey(doctorPref, env)).stream().map(Object::toString).toList());
    }


    /**
     * 将问诊单号从医生可接诊队列中移除
     *
     * @param doctorPref
     * @param env
     * @param inquiryPref
     */
    public void removeInquiryFromDoctorReception(Integer checkStatus , String doctorPref, String env, String inquiryPref) {
        String key = ObjectUtil.equals(InquiryStatusEnum.QUEUING.getStatusCode(),checkStatus) ? RedisKeyConstants.getDoctorCanReceptionKey(doctorPref, env) : RedisKeyConstants.getDoctorReceptionKey(doctorPref, env);
        RedisUtils.lRem(key, inquiryPref);
    }


    /**
     * 获取医生接诊中的列表
     *
     * @param doctorPref 医生编码
     * @param env        环境
     * @return 医生接诊中问诊单列表
     */
    public List<String> getDoctorReceptionList(String doctorPref, String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorReceptionKey(doctorPref, env)).stream().map(Object::toString).toList());
    }


    /**
     * 从一批医生中获取哪些处于开方间隔冷却期
     *
     * @param keys 医生开方间隔key集合
     * @return 处于冷却期医生编码集合
     */
    public List<String> getIntervalDoctorList(List<String> keys) {
        return new ArrayList<>(RedisUtils.mGet(keys).stream().filter(Objects::nonNull).map(Object::toString).toList());
    }

    /**
     * 获取医生省份编码
     *
     * @param doctorPref 医生编码
     * @return 省份编码
     */
    public String getDoctorProvinceCode(String doctorPref) {
        return RedisUtils.get(RedisKeyConstants.getDoctorProvinceCodeKey(doctorPref)).toString();
    }


    /**
     * 问诊派单 真人问诊派单时，需要记录派单医生列表
     *
     * @param inquiryPref 问诊单号
     * @param doctorList  医生列表
     */
    public void onSendInquiry(String inquiryPref, List<String> doctorList, String doctorEnv) {
        String key = RedisKeyConstants.getSendInquiryKey(inquiryPref);
        executeRedisTransaction(operations -> {
            // 1、写入-问诊派单医生记录
            operations.opsForList().leftPushAll(key, doctorList);
            // 给key设置超市时间，2个小时
            operations.expire(key, 2, TimeUnit.HOURS);
            // 2、写入-将当前问诊单加入医生可接诊队列
            doctorList.forEach(doctorPref -> operations.opsForList().leftPush(RedisKeyConstants.getDoctorCanReceptionKey(doctorPref, doctorEnv), inquiryPref));
        });
    }

    /**
     * 医生接诊(抢单)事件
     *
     * @param inquiryDto    问诊单信息
     * @param inquiryDoctor 医生信息
     */
    public List<String> doctorReception(InquiryRecordDto inquiryDto, TenantDto tenantDto, InquiryDoctorDO inquiryDoctor) {
        log.info("医生接诊redis操作开始,问诊单号：{}，医生编码：{}", inquiryDto.getPref(), inquiryDoctor.getPref());
        List<String> doctorWaitKeyList = getDoctorManualWaitKeyList(inquiryDto, inquiryDoctor);
        List<String> sendList = RedisUtils.lGetAll(RedisKeyConstants.getSendInquiryKey(inquiryDto.getPref())).stream().map(Object::toString).toList();
        log.info("当前问诊单号：{},已派单医生列表：{}", inquiryDto.getPref(), JSON.toJSONString(sendList));
        Long doctorReceptionSize = RedisUtils.lSize(RedisKeyConstants.getDoctorReceptionKey(inquiryDoctor.getPref(), inquiryDoctor.getEnvTag()));
        log.info("当前医生编码：{},当前接诊中的问诊个数：{}", inquiryDoctor.getPref(), doctorReceptionSize);
        // 获取当前问诊调度的科室 <科室编码>
        List<String> dispatchDoctorList = RedisUtils.lGetAll(RedisKeyConstants.getInquiryDispatchDeptKey(inquiryDto.getPref())).stream().map(Object::toString).toList();
        Integer doctorMaxInquiry = getDoctorMaxReceptionNum();
        executeRedisTransaction(operations -> {
            // 1、移除-将问诊单从门店(待接诊)
            operations.opsForList().remove(RedisKeyConstants.getDrugstoreInquiryKey(inquiryDto.getTenantId(), tenantDto.getEnvTag()), 0, inquiryDto.getPref());
            // 2、移除-将问诊单从医院接诊大厅移除
            dispatchDoctorList.forEach(deptPref -> {
                operations.opsForList().remove(RedisKeyConstants.getRecetionAreaKey(inquiryDto.getHospitalPref(), deptPref, inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType()), 0, inquiryDto.getPref());
            });
            // 同步删除科室调度记录
            operations.delete(RedisKeyConstants.getInquiryDispatchDeptKey(inquiryDto.getPref()));
            // 3、移除-遍历问诊派单记录队列，将问诊单每个派单医生可接诊队列中移除，然后清空派单记录队列(真人接诊情况下)
            if (inquiryDto.isManualInquiry()) {
                sendList.forEach(doctorPref -> {
                    operations.opsForList().remove(RedisKeyConstants.getDoctorCanReceptionKey(doctorPref.toString(), inquiryDoctor.getEnvTag()), 0, inquiryDto.getPref());
                    log.info("当前问诊单号：{},从：{}医生的可接诊队列移除", inquiryDto.getPref(), doctorPref);
                    operations.opsForList().remove(RedisKeyConstants.getSendInquiryKey(inquiryDto.getPref()), 0, doctorPref);
                    log.info("当前问诊单号：{},将：{}医生的派单记录清空", inquiryDto.getPref(), doctorPref);
                });
            }
            // 4、写入-将问诊单加入门店（接诊中）队列
            operations.opsForList().leftPush(RedisKeyConstants.getDrugstoreCurrentInquiryKey(inquiryDto.getTenantId(), tenantDto.getEnvTag()), inquiryDto.getPref());
            // 5、写入-写入问诊单对应接诊医生的缓存key（2小时过期）
            operations.opsForValue().set(RedisKeyConstants.getInquiryCurrentDoctorKey(inquiryDto.getPref()), inquiryDoctor.getPref(), 2, TimeUnit.HOURS);
            // 6、写入-将问诊单加入医生接诊中队列（真人问诊情况下）
            if (inquiryDto.isManualInquiry()) {
                operations.opsForList().leftPush(RedisKeyConstants.getDoctorReceptionKey(inquiryDoctor.getPref(), inquiryDoctor.getEnvTag()), inquiryDto.getPref());
            }
            // 7、判断当前医生接诊中队列的长度，如果大于5则遍历当前医生所有的调度池将此医生从待调度池中移除
            if (doctorReceptionSize != null && doctorReceptionSize >= doctorMaxInquiry - 1) {
                doctorWaitKeyList.forEach(waitPoolKey -> {
                    operations.opsForList().remove(waitPoolKey, 0, inquiryDoctor.getPref());
                });
            }
        });
        return sendList;
    }

    /**
     * 真人问诊时，涉及到医生接诊队列的移除操作
     *
     * @param inquiryDto    问诊单信息
     * @param inquiryDoctor 医生信息
     * @return 当前医生真人接诊等待队列的key集合
     */
    private List<String> getDoctorManualWaitKeyList(InquiryRecordDto inquiryDto, InquiryDoctorDO inquiryDoctor) {
        // 当前问诊为自动开方时，无需操作医生池的队列，所以直接返回空
        if (inquiryDto.isAutoInquiry()) {
            return List.of();
        }
        return super.getDoctorWaitKeyList(inquiryDoctor.getPref(), DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
    }

    /**
     * 医生开具处方、取消开方（医生完结问诊）
     *
     * @param inquiryDto    问诊单信息
     * @param tenantDto     租户信息
     * @param inquiryDoctor 医生信息
     */
    public void doctorOverInquiry(InquiryRecordDto inquiryDto, TenantDto tenantDto, InquiryDoctorDO inquiryDoctor) {
        List<String> doctorWaitKeyList = getDoctorManualWaitKeyList(inquiryDto, inquiryDoctor);
        Long doctorReceptionSize = RedisUtils.lSize(RedisKeyConstants.getDoctorReceptionKey(inquiryDoctor.getPref(), inquiryDoctor.getEnvTag()));
        Integer doctorMaxInquiry = getDoctorMaxReceptionNum();
        Integer prescriptionIntervalTime = getDoctorPrescriptionIntervalTime();
        executeRedisTransaction(operations -> {
            // 1、移除-将问诊单从门店（接诊中）队列移除
            operations.opsForList().remove(RedisKeyConstants.getDrugstoreCurrentInquiryKey(tenantDto.getId(), tenantDto.getEnvTag()), 0, inquiryDto.getPref());
            // 2、移除-问诊单对应接诊医生的缓存移除
            operations.delete(RedisKeyConstants.getInquiryCurrentDoctorKey(inquiryDto.getPref()));
            // 3、移除-将问诊单从医生接诊中队列移除（真人问诊情况下）
            if (inquiryDto.isManualInquiry()) {
                operations.opsForList().remove(RedisKeyConstants.getDoctorReceptionKey(inquiryDoctor.getPref(), inquiryDoctor.getEnvTag()), 0, inquiryDto.getPref());
            }
            // 4、判断当前医生接诊中队列的长度，如果小于5则遍历当前医生所有的调度池将此医生重新移入待调度池
            if (doctorReceptionSize != null && doctorReceptionSize == doctorMaxInquiry.longValue()) {
                doctorWaitKeyList.forEach(waitPoolKey -> {
                    operations.opsForList().leftPush(waitPoolKey, inquiryDoctor.getPref());
                });
            }
            // 5、移除-如果是真人问诊，则从当前医生自动抢单问诊中队列移除当前问诊单（理论上这里需要判断当前问诊单是否在在医生自动抢单队列中，但是考虑判断+移除需要2次redis操作，所以这里直接移除，key为空不影响）
            if (inquiryDto.isManualInquiry()) {
                operations.opsForList().remove(RedisKeyConstants.getDoctorCurrentAutoGrabKey(inquiryDoctor.getPref(), inquiryDoctor.getEnvTag()), 0, inquiryDto.getPref());
            }
            // 6、写入-如果是自动开方，且系统开启了医生签名使用间隔，则需要将当前医生写入间隔时间缓存（间隔期内无法再次开方）
            if (inquiryDto.isAutoInquiry() && prescriptionIntervalTime != null) {
                operations.opsForValue().set(RedisKeyConstants.getDoctorIntervalKey(inquiryDoctor.getPref()), 0, prescriptionIntervalTime, TimeUnit.SECONDS);
            }
        });
    }

    /**
     * 根据条件将医生移入待调度医生池
     *
     * @param inquiryDoctor          医生信息
     * @param inquiryType            开方类型  自动 or  手动
     * @param hospitalDeptRelationId 指定科室医院关联id
     */
    public void doctorStartReception(InquiryDoctorDO inquiryDoctor, Integer inquiryType, Long hospitalDeptRelationId) {
        List<String> doctorWaitKeyList = this.getDoctorWaitKeyList(inquiryDoctor.getPref(), inquiryType, hospitalDeptRelationId);
        executeRedisTransaction(operations -> {
            // 1、写入-将医生加入待调度医生池
            doctorWaitKeyList.forEach(waitPoolKey -> {
                operations.opsForList().leftPush(waitPoolKey, inquiryDoctor.getPref());
            });
            // 2、判断当前科室id不为空，或者出诊类型为自动开方时，直接return
            if (hospitalDeptRelationId != null || ObjectUtil.equals(inquiryType, DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode())) {
                return;
            }
            // 3、真人出诊情况下，判断当前医师是否开启了自动抢单(关闭不处理)
            if (Objects.equals(inquiryDoctor.getAutoGrabStatus(), AutoGrabStatusEnum.CLOSE.getCode())) {
                return;
            }
            // 4、将当前医生添加到自动抢单医生队列(先移除，再写入)
            operations.opsForList().remove(RedisKeyConstants.getDoctorAutoGrabKey(inquiryDoctor.getEnvTag()), 0, inquiryDoctor.getPref());
            operations.opsForList().leftPush(RedisKeyConstants.getDoctorAutoGrabKey(inquiryDoctor.getEnvTag()), inquiryDoctor.getPref());
        });
    }

    /**
     * 根据条件将医生移出待调度医生池
     *
     * @param inquiryDoctor 医生信息
     * @param inquiryType   人工 or  自动
     */
    public void doctorStopReception(InquiryDoctorDO inquiryDoctor, Integer inquiryType, Long hospitalDeptRelationId) {
        List<String> doctorWaitKeyList = this.getDoctorWaitKeyList(inquiryDoctor.getPref(), inquiryType, hospitalDeptRelationId);
        executeRedisTransaction(operations -> {
            // 1、移除-从待调度医生池将当前医生移出
            doctorWaitKeyList.forEach(waitPoolKey -> {
                operations.opsForList().remove(waitPoolKey, 0, inquiryDoctor.getPref());
            });
            // 判断是否需要将当前医生从自动抢单医生队列移除
            if (ObjectUtil.equals(DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode(), inquiryType) || hospitalDeptRelationId != null || Objects.equals(inquiryDoctor.getAutoGrabStatus(), AutoGrabStatusEnum.CLOSE.getCode())) {
                return;
            }
            // 2、将当前医生从自动抢单医生队列移除（不存在则是空）
            operations.opsForList().remove(RedisKeyConstants.getDoctorAutoGrabKey(inquiryDoctor.getEnvTag()), 0, inquiryDoctor.getPref());
        });
    }

    /**
     * 医生接诊范围更新事件，需要移除不可接问诊
     *
     * @param removePrefs 需要从医生可接诊列表移除的问诊单号
     * @param doctorPref  医生编号
     * @param envTag      医生环境
     */
    public void onDoctorReceiptScopeUpdate(List<String> removePrefs, String doctorPref, String envTag) {
        executeRedisTransaction(operations -> {
            removePrefs.forEach(inquiryPref -> {
                // 将当前问诊单号从医生可接问诊列表移出
                operations.opsForList().remove(RedisKeyConstants.getDoctorCanReceptionKey(doctorPref, envTag), 0, inquiryPref);
                // 将当前医生编码从当前问诊派单医生列表中移除
                operations.opsForList().remove(RedisKeyConstants.getSendInquiryKey(inquiryPref), 0, doctorPref);
            });
        });
    }

    /**
     * 移除医生省份编码 缓存
     *
     * @param doctorPref 医生编码
     */
    public void removeDoctorProvinceCode(String doctorPref) {
        RedisUtils.del(RedisKeyConstants.getDoctorProvinceCodeKey(doctorPref));
    }

    /**
     * 设置医生省份编码缓存
     *
     * @param doctorPref      医生编码
     * @param orgProvinceCode 省份编码
     */
    public void setDoctorProvinceCode(String doctorPref, String orgProvinceCode) {
        if (StringUtils.isAnyBlank(doctorPref, orgProvinceCode)) {
            return;
        }
        executeRedisTransaction(operations -> {
            // 1、删除旧缓存
            operations.delete(RedisKeyConstants.getDoctorProvinceCodeKey(doctorPref));
            // 2、设置新缓存
            operations.opsForValue().set(RedisKeyConstants.getDoctorProvinceCodeKey(doctorPref), orgProvinceCode);
        });
    }

    /**
     * 获取自动抢单医生列表
     *
     * @param envTag 环境表示
     * @return 自动抢单医生列表
     */
    public List<String> getGrabDoctorList(String envTag) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorAutoGrabKey(envTag)).stream().filter(Objects::nonNull).map(Object::toString).toList());
    }

    /**
     * 获取医生当前自动抢单接诊中的列表
     *
     * @param doctorPref 医生编码
     * @param envTag     环境标识
     * @return 医生当前自动抢单接诊中的问诊列表
     */
    public List<String> getDoctorCurrentAutoGrabList(String doctorPref, String envTag) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorCurrentAutoGrabKey(doctorPref, envTag)).stream().filter(Objects::nonNull).map(Object::toString).toList());
    }

    /**
     * 将问诊单加入到医生当前自动抢单接诊队列
     *
     * @param doctorPref  医生编码
     * @param env         环境
     * @param inquiryPref 问诊单号
     */
    public void pushDoctorCurrentAutoGrabList(String doctorPref, String env, String inquiryPref) {
        RedisUtils.lPush(RedisKeyConstants.getDoctorCurrentAutoGrabKey(doctorPref, env), inquiryPref);
    }


    /**
     * 处理自动开方医生时间轮
     *
     * @param oldWheels 旧时间
     * @param newWheels 新时间
     */
    public void handleAutoInquiryDoctorTimerWheel(List<DoctorAutoInquiryTimerWheelDto> oldWheels, List<DoctorAutoInquiryTimerWheelDto> newWheels, String orgProvinceCode) {
        if (CollUtil.isEmpty(oldWheels) && CollUtil.isEmpty(newWheels)) {
            return;
        }
        executeRedisTransaction(operations -> {
            // 删旧时间
            if (CollUtil.isNotEmpty(oldWheels)) {
                oldWheels.forEach(wheel -> {
                    if (StringUtils.isNotBlank(wheel.getWheelKey())) {
                        operations.opsForList().remove(wheel.getWheelKey(), 0, wheel.getWheelValue());
                    }
                    // 停诊移除旧时间下当前医生
                    operations.opsForList().remove(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(wheel.getWheelValue()), 0, wheel.getDoctorPref());
                    // 移除省份位置
                    operations.delete(RedisKeyConstants.getDoctorProvinceCodeKey(wheel.getDoctorPref()));
                });
            }
            // 加新时间
            if (CollUtil.isNotEmpty(newWheels)) {
                newWheels.forEach(wheel -> {
                    if (StringUtils.isNotBlank(wheel.getWheelKey())) {
                        operations.opsForList().leftPush(wheel.getWheelKey(), wheel.getWheelValue()); // 放时间轮
                    }
                    // 判断出诊 先移除-再移入 ,没有时间 或者 在时间范围内
                    if (StringUtils.isBlank(wheel.getWheelKey()) || wheel.isAutoAllInquiry()
                        || (LocalDateTime.now().toLocalTime().isAfter(LocalTime.parse(wheel.getStartTime())) && LocalDateTime.now().toLocalTime().isBefore(DateUtil.getEndLocalTime(wheel.getEndTime())))) {
                        operations.opsForList().remove(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(wheel.getWheelValue()), 0, wheel.getDoctorPref());
                        operations.opsForList().leftPush(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(wheel.getWheelValue()), wheel.getDoctorPref());
                        // 加入省份位置
                        if (StringUtils.isNotBlank(orgProvinceCode)) {
                            operations.opsForValue().set(RedisKeyConstants.getDoctorProvinceCodeKey(wheel.getDoctorPref()), orgProvinceCode);
                        }
                    }
                });
            }
        });
    }


    /**
     * 初始化自动医生时间轮
     *
     * @param wheels                   所有时间轮
     * @param doctorOrgProvinceCodeMap
     */
    public void initAutoInquiryDoctorTimerWheel(List<DoctorAutoInquiryTimerWheelDto> wheels, Map<String, String> doctorOrgProvinceCodeMap) {
        if (CollUtil.isEmpty(wheels)) {
            return;
        }
        // 获取所有自动开方时间轮keys
        List<String> allAutoTimerWheelKeys = new ArrayList<>() {{
            for (int i = 0; i < DoctorConstant.DOCTOR_AUTO_INQUIRY_TIME_UNIT; i++) {
                add(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelKey(i));
            }
        }};

        executeRedisTransaction(operations -> {
            // 删除时间轮所有槽位
            operations.delete(allAutoTimerWheelKeys);
            // 自动开方医生加入时间轮
            wheels.forEach(wheel -> {
                if (StringUtils.isNotBlank(wheel.getWheelKey())) {
                    operations.opsForList().leftPush(wheel.getWheelKey(), wheel.getWheelValue());
                    // 删除省份位置
                    operations.delete(RedisKeyConstants.getDoctorProvinceCodeKey(wheel.getDoctorPref()));
                    // 判断出诊 先移除- 在移入
                    if (LocalDateTime.now().toLocalTime().isAfter(LocalTime.parse(wheel.getStartTime())) && LocalDateTime.now().toLocalTime().isBefore(DateUtil.getEndLocalTime(wheel.getEndTime()))) {
                        operations.opsForList().remove(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(wheel.getWheelValue()), 0, wheel.getDoctorPref());
                        operations.opsForList().leftPush(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(wheel.getWheelValue()), wheel.getDoctorPref());
                        // 加入省份位置
                        if (doctorOrgProvinceCodeMap.containsKey(wheel.getDoctorPref())) {
                            operations.opsForValue().set(RedisKeyConstants.getDoctorProvinceCodeKey(wheel.getDoctorPref()), doctorOrgProvinceCodeMap.get(wheel.getDoctorPref()));
                        }
                    }
                }
            });
        });
    }


    /**
     * 获取当前时间时间轮节点 value
     *
     * @param time 时间点
     * @return
     */
    public List<String> getCurrentAutoInquiryDoctorTimerWheel(LocalTime time) {
        int t = time.getHour() * 60 + time.getMinute();
        return new ArrayList<>(RedisUtils.lGetAll(com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelKey(t)).stream().filter(Objects::nonNull).map(Object::toString).toList());
    }

    /**
     * 操作自动开方医生出诊
     *
     * @param doctorPref 医生编码
     * @param realKey    出诊key
     */
    public void doctorStartReceptionByAuto(String doctorPref, String realKey, String orgProvinceCode) {
        if (StringUtils.isAnyBlank(doctorPref, realKey)) {
            return;
        }
        executeRedisTransaction(operations -> {
            operations.opsForList().remove(realKey, 0, doctorPref);
            operations.opsForList().leftPush(realKey, doctorPref);
            // 加入省份位置
            if (StringUtils.isNotBlank(orgProvinceCode)) {
                operations.opsForValue().set(RedisKeyConstants.getDoctorProvinceCodeKey(doctorPref), orgProvinceCode);
            }
        });
    }

    /**
     * 操作自动开方医生停诊
     *
     * @param doctorPref 医生编码
     * @param realKey    停诊key
     */
    public void doctorStopReceptionByAuto(String doctorPref, String realKey) {
        if (StringUtils.isAnyBlank(doctorPref, realKey)) {
            return;
        }
        executeRedisTransaction(operations -> {
            operations.opsForList().remove(realKey, 0, doctorPref);

        });
    }

    /**
     * 将指定医生移除自动抢单医生队列
     * @param doctor
     */
    public void removeDoctorFromAutoGrabQueue(InquiryDoctorDO doctor) {
         RedisUtils.lRem(RedisKeyConstants.getDoctorAutoGrabKey(doctor.getEnvTag()), doctor.getPref());
    }

    /**
     * 将指定医生从每个环境的医保队列移除
     * @param doctorPref
     */
    public void removeDoctorFromMedicareQueue(String doctorPref) {
        executeRedisTransaction(operations -> {
            for (EnvTagEnum envTag : EnvTagEnum.values()){
                operations.opsForList().remove(RedisKeyConstants.getDoctorMedicareQueueKey(envTag.getEnv()), 0, doctorPref);
            }
        });
    }

    /**
     * 添加指定医生到每个环境的医保队列
     * @param env 环境
     * @param doctorPref 医生编码
     */
    public void pushDoctorFromMedicareQueue(String env , String doctorPref) {
        RedisUtils.lPush(RedisKeyConstants.getDoctorMedicareQueueKey(env), doctorPref);
    }

    /**
     * 获取医保编码医生队列
     * @param env
     * @return
     */
    public List<String> getMedicareDoctorQueue(String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDoctorMedicareQueueKey(env)).stream().map(Object::toString).toList());
    }
}
