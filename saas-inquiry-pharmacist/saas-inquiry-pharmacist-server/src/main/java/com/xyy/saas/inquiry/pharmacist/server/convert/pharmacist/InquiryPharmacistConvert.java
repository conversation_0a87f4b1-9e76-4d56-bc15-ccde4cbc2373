package com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistUpdateStatusReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist.vo.PharmacistUpdateInfoVo;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardCaInfo;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPharmacistConvert {

    InquiryPharmacistConvert INSTANCE = Mappers.getMapper(InquiryPharmacistConvert.class);

    @Mapping(target = "nickname", source = "name")
    @Mapping(target = "id", source = "userId")
    AdminUserSaveDTO convertUser(InquiryPharmacistSaveReqVO createReqVO);

    InquiryPharmacistRespVO convert(InquiryPharmacistDO inquiryPharmacistDO);

    default InquiryPharmacistDO initConvertVO2DO(InquiryPharmacistSaveReqVO createReqVO) {
        InquiryPharmacistDO pharmacistDO = convert(createReqVO);
        pharmacistDO.setPref(PrefUtil.getPharmacistPref());
        return pharmacistDO;
    }

    InquiryPharmacistDO convert(InquiryPharmacistSaveReqVO createReqVO);

    InquiryPharmacistDO convertStatus(InquiryPharmacistUpdateStatusReqVO updateReqVO);

    InquiryPharmacistDto convertDO2DTO(InquiryPharmacistDO pharmacistDO);

    InquiryPharmacistDto convert2VO(InquiryPharmacistRespVO pharmacist);

    List<InquiryPharmacistRespVO> convert2VOList(List<InquiryPharmacistDO> pharmacistDOS);

    default InquiryPharmacistRespVO convertSync(PharmacistForwardDetailDto dto) {
        InquiryPharmacistRespVO vo = new InquiryPharmacistRespVO();
        vo.setName(dto.getDocName());
        vo.setSex(dto.getSex());
        vo.setIdCard(dto.getIdCard());
        vo.setMobile(dto.getDocTel());

        vo.setPhoto(dto.getHeadPortrait());
        vo.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        vo.setEnvTag(EnvTagEnum.PROD.getEnv());
        // 合作状态 1合作 0取消
        vo.setBiography(dto.getDocComment()); // 简介
        vo.setNationCode(NumberUtils.toInt(dto.getNationCode(), 1)); // 民族
        vo.setQualification(dto.getQualification()); // 资格
        vo.setPharmacistType(
            Objects.equals((byte) 1, dto.getPharmacistType()) ? PharmacistTypeEnum.DRUGSTORE.getCode() : Objects.equals((byte) 2, dto.getPharmacistType()) ? PharmacistTypeEnum.HOSPITAL.getCode() : PharmacistTypeEnum.PLATFORM.getCode());

        if (Objects.equals(vo.getPharmacistType(), PharmacistTypeEnum.PLATFORM.getCode())) {
            vo.setJobType(Objects.equals((byte) 1, dto.getPlatformPharmacistType()) ? DoctorJobTypeEnum.FULL_TIME.getCode() : DoctorJobTypeEnum.PART_TIME.getCode());
        }

        vo.setSchool(dto.getDocDegree()); // 学校
        vo.setProvinceCode(dto.getWorkingAdress()); // 省份
        vo.setDrawnSign(dto.getDownFlag());
        // 资质证件
        vo.setProfessionIdentifications(new ArrayList<>() {{
            Optional.ofNullable(dto.getIdCardList()).ifPresent(u -> {
                add(InquiryProfessionIdentificationDto.builder().certificateType(CertificateTypeEnum.ID_CARD_FRONT.getType())
                    .certificateImgUrl(u).build());
            });

            add(InquiryProfessionIdentificationDto.builder().certificateType(CertificateTypeEnum.ZCZ.getType())
                .certificateImgUrl(dto.getDocCertList())
                .certificateNo(dto.getPracNo())
                .certificateName(dto.getWorkInstName())
                .registerTime(dto.getDocMultiSitedDateStart() == null ? null : dto.getDocMultiSitedDateStart().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .validTime(dto.getDocMultiSitedDateEnd() == null ? null : dto.getDocMultiSitedDateEnd().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()).build());

            add(InquiryProfessionIdentificationDto.builder().certificateType(CertificateTypeEnum.QUALIFICATION_IMG.getType())
                .certificateImgUrl(dto.getCertDocPracList())
                .certificateNo(dto.getCertNo())
                .registerTime(dto.getCertRecDate() == null ? null : dto.getCertRecDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()).build());
        }});
        return vo;
    }

    default SyncCreateCaDto convertPharmacistCa(InquiryPharmacistDO inquiryPharmacistDO, ForwardCaInfo caInfo, ForwardPersonInfo personInfo) {
        return SyncCreateCaDto.builder()
            .userId(inquiryPharmacistDO.getUserId())
            .name(inquiryPharmacistDO.getName())
            .mobile(inquiryPharmacistDO.getMobile())
            .idCard(inquiryPharmacistDO.getIdCard())
            .personInfo(personInfo)
            .caInfo(caInfo)
            .build();
    }

    @Mapping(target = "username", source = "mobile")
    @Mapping(target = "nickname", source = "name")
    AdminUserSaveDTO convertPharmacist2User(InquiryPharmacistDO pharmacistDO);

    InquiryPharmacistDO convertUpdate(PharmacistUpdateInfoVo updateInfoVo);

    @Mapping(target = "password", expression = "java(org.apache.commons.lang3.StringUtils.substring(pharmacistRespVO.getMobile(),5,12))")
    InquiryPharmacistSaveReqVO convertSyncVo(InquiryPharmacistRespVO pharmacistRespVO);
}
