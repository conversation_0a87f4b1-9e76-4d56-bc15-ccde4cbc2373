package com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistQualificationEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.signature.InquiryPharmacistSignatureConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.HosPlatPhaFreeAuditProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistQueryDto;
import com.xyy.saas.inquiry.signature.api.ca.dto.InquirySignatureCaAuthRespDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.dto.InquiryPrescriptionTemplateDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 线下(自动)审方策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 17:33
 */
@Component
@Slf4j
public class OfflineAuditStrategy extends PrescriptionAuditStrategy {


    @Resource
    private HosPlatPhaFreeAuditProducer hosPlatPhaFreeAuditProducer;

    @Override
    public PrescriptionAuditTypeEnum getPrescriptionAuditType() {
        return PrescriptionAuditTypeEnum.OFFLINE;
    }

    @Override
    public PharmacistTypeEnum getPharmacistType() {
        return null;
    }


    public OfflineAuditStrategy getSelf() {
        return SpringUtil.getBean(getClass());
    }

    /**
     * 自动审方
     *
     * @param ppDto        处方
     * @param roleCodeEnum 角色
     * @param auditorType  审方人类型
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoAuditPrescription(InquiryPharmacistPrescriptionDTO ppDto, RoleCodeEnum roleCodeEnum, AuditorTypeEnum auditorType) {
        // 获取门店roleCodeEnum角色用户 且在上班的
        List<AdminUserRespDTO> userRespDTOS = adminUserApi.getUserListByRoleCodes(Collections.singletonList(roleCodeEnum.getCode()))
            .stream().filter(u -> CommonStatusEnum.isEnable(u.getClockInStatus())).toList();
        // 创建审核记录 并 线下审方
        createRecordAndOfflineAudit(ppDto, auditorType, CollUtil.getFirst(userRespDTOS));
    }


    /**
     * 自动平台药师免签审方 - 查全职平台药师 + CA免签完成的
     *
     * @param auditMsgDto 医院平台药师审方
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoPlatformAuditPrescription(HosPlatPhaFreeAuditMsgDto auditMsgDto) {
        InquiryPharmacistPrescriptionDTO prescription = auditMsgDto.getPrescription();
        // 获取全职平台药师 + 筛选免签用户
        List<Long> phaUserIds = getPlatFreeSignPharmacists(prescription, auditMsgDto.getPlatformConfigId());
        log.info("医院药师走平台药师免签审方,查询到符合的全职药师符合CA的userId:{}", phaUserIds);

        if (CollUtil.isEmpty(phaUserIds)) {
            int consumerCount = auditMsgDto.getConsumerCount();
            // 业务通知  没有全职平台药师,放入mq等下次轮询调度 每次调度找不到+1分钟，最大60分钟
            if (consumerCount % 30 == 0) {
                InquiryPrescriptionTemplateDto template = prescriptionTemplateApi.getInquiryPrescriptionTemplate(prescription.getPreTempId());
                dingService.send(Markdown.title("医院平台药师免签审方")
                    .add("处方号", prescription.getPref())
                    .add("医院编码", prescription.getHospitalPref())
                    .add("模板名称", template == null ? prescription.getPreTempId() : template.getName())
                    .add("重试次数", "当前第" + (consumerCount + 1) + "次")
                    .add("详情", "没有符合的全职免签平台药师,请及时补充(下次重试时间为" + Math.min(60, consumerCount + 1) + "分钟后)")
                );
            }
            hosPlatPhaFreeAuditProducer.sendMessage(HosPlatPhaFreeAuditEvent.builder()
                .msg(auditMsgDto.setConsumerCount(consumerCount + 1)).build(), LocalDateTime.now().plusMinutes(Math.min(60, consumerCount + 1)));
            return;
        }
        // 创建审核记录 并 线下审方
        Long userId = phaUserIds.get(MathUtil.randomNextInt(phaUserIds.size()));
        createRecordAndOfflineAudit(prescription, auditMsgDto.getAuditorType(), adminUserApi.getUser(userId));
    }

    private List<Long> getPlatFreeSignPharmacists(InquiryPharmacistPrescriptionDTO prescription, Integer platformConfigId) {
        // 获取全职平台药师
        List<InquiryPharmacistDO> platformPharmacists = inquiryPharmacistService.getPharmacistList(PharmacistQueryDto.builder()
            .pharmacistType(PharmacistTypeEnum.PLATFORM.getCode())
            .auditStatus(AuditStatusEnum.APPROVED.getCode())
            .jobType(DoctorJobTypeEnum.FULL_TIME.getCode())
            .qualifications(PharmacistQualificationEnum.convertFromMedicineTypeCode(prescription.getMedicineType())).build());

        if (CollUtil.isEmpty(platformPharmacists)) {
            return List.of();
        }
        //  筛选正常用户
        List<Long> userIds = adminUserApi.getUserList(CollectionUtils.convertList(platformPharmacists, InquiryPharmacistDO::getUserId)).stream()
            .filter(e -> Objects.equals(e.getStatus(), UserAccountStatusEnum.ENABLE.getCode())).map(AdminUserRespDTO::getId).toList();

        //  筛选CA
        return inquirySignatureCaAuthApi.getCaAuthInfo(userIds, SignaturePlatformEnum.fromCode(prescription.getSignPlatform()))
            .stream()
            .filter(c -> isFreeSignValid(c, platformConfigId))
            .map(InquirySignatureCaAuthRespDto::getUserId)
            .toList();
    }

    private boolean isFreeSignValid(InquirySignatureCaAuthRespDto caAuth, Integer platformConfigId) {
        if (!FddCaConstantEnum.isFreeSignAndValid(caAuth.getAuthorizeFreeSignStatus(), caAuth.getAuthorizeFreeSignDdl())) {
            return false;
        }

        return SignatureAppConfigIdEnum.isDefault(platformConfigId)
            || (CollUtil.isNotEmpty(caAuth.getExt())
            && caAuth.getExt().stream()
            .filter(e -> Objects.equals(platformConfigId, e.getSignaturePlatformConfigId()))
            .allMatch(e -> FddCaConstantEnum.isFreeSignAndValid(e.getAuthorizeFreeSignStatus(), e.getAuthorizeFreeSignDdl())));
    }


    /**
     * 创建审核记录 并 线下审方
     *
     * @param ppDto
     * @param auditorType
     * @param userRespDTO
     */
    private void createRecordAndOfflineAudit(InquiryPharmacistPrescriptionDTO ppDto, AuditorTypeEnum auditorType, AdminUserRespDTO userRespDTO) {
        PrescriptionSignatureAuditDto auditDto = InquiryPharmacistSignatureConvert.INSTANCE.convertSignAuditDto(ppDto, InquiryPrescriptionAuditVO.builder().auditorTypeEnum(auditorType).build(), userRespDTO);

        if (userRespDTO != null) {
            InquiryPrescriptionAuditSaveReqVO createReqVO = InquiryPharmacistAuditConvert.INSTANCE.convertCreateAuditRecord(ppDto.setAuditPrescriptionTime(LocalDateTime.now())
                , userRespDTO.getId(), userRespDTO.getNickname(), SignatureStatusEnum.SENDING);
            createReqVO.setAuditorSignatureTime(LocalDateTime.now());
            createReqVO.setAuditorType(auditorType.getCode());
            Long auditRecordId = inquiryPrescriptionAuditService.createPrescriptionAudit(createReqVO);
            auditDto.setAuditRecordId(auditRecordId);
        }

        // 审核追加当前审方人
        auditDto.setAutoAudit(true);
        inquirySignaturePrescriptionApi.auditPrescription(auditDto);
    }


    /**
     * 线下审方
     */
    @Override
    public void pushPrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {
        // 更新处方审核人类型
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(ppDto.getId()).pref(ppDto.getPref()).auditorType(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode()).build());

        // 直接查询当前门店药师信息 自动加盖签名
        getSelf().autoAuditPrescription(ppDto, RoleCodeEnum.PHARMACIST, AuditorTypeEnum.DRUGSTORE_PHARMACIST);
    }

    /**
     * 获取当前药师待审方数量
     */
    @Override
    public CommonResult<Long> waitReceiveCountSelf(InquiryPharmacistDO pharmacist) {
        return CommonResult.success(0L);
    }

    /**
     * 领取一个最早的处方
     */
    @Override
    public CommonResult<InquiryPharmacistPrescriptionDTO> receivePrescriptionSelf(InquiryPharmacistDO pharmacist) {
        return null;
    }


}
