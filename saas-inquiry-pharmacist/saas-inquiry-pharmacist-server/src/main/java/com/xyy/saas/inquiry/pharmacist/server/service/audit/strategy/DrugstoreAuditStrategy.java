package com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy;

import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.PRESCRIPTION_AUDIT_DISTRIBUTE_FAIL;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistNatureEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistQualificationEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.DistributeStatusEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.PrescriptionPushRemoteAuditProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * 药店药师审方策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 17:33
 */
@Component
@Slf4j
public class DrugstoreAuditStrategy extends PrescriptionAuditStrategy {

    @Override
    public PrescriptionAuditTypeEnum getPrescriptionAuditType() {
        return PrescriptionAuditTypeEnum.DRUGSTORE;
    }

    @Override
    public PharmacistTypeEnum getPharmacistType() {
        return PharmacistTypeEnum.DRUGSTORE;
    }

    public DrugstoreAuditStrategy getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Resource
    private PrescriptionPushRemoteAuditProducer prescriptionPushRemoteAuditProducer;

    /**
     * 门店审方池,发送消息通知药师
     */
    @Override
    public void pushPrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {

        // 更新处方审核人类型
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(ppDto.getId()).pref(ppDto.getPref()).auditorType(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode()).build());

        TenantDto tenantDto = tenantApi.getTenant(ppDto.getTenantId());

        if (Objects.equals(ppDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            // 远程带方处方 推入连锁总部审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewChainPoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), ppDto.getExt().getHeadTenantId(), ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
        } else {
            // 推入门店药师审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewDrugstorePoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), tenantDto.getId(), ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
            // 判断推送远程审方
            pushRemoteAudit(ppDto, tenantDto);
        }

        // TODO 推送消息通知审方
        // 获取门店药师角色用户
        // List<AdminUserRespDTO> userRespDTOS = TenantUtils.execute(ppDto.getTenantId(), () -> adminUserApi.getUserListByRoleCodes(Collections.singletonList(RoleCodeEnum.PHARMACIST.getCode())));

    }

    private void pushRemoteAudit(InquiryPharmacistPrescriptionDTO ppDto, TenantDto tenantDto) {
        // 判断如果是连锁门店切总部开启了推送远程审方的配置
        if (Objects.equals(tenantDto.getWzTenantType(), TenantTypeEnum.CHAIN_HEADQUARTERS) && tenantDto.getHeadTenantId() != null) {
            TenantParamConfigDTO paramConfigDto = tenantParamConfigApi.queryTenantParamConfig(tenantDto.getHeadTenantId(), TenantParamConfigTypeEnum.PRESCRIPTION_REMOTE_TO_HEAD_SWITCH);
            boolean pushRemoteAudit = paramConfigDto != null && CommonStatusEnum.isEnable(NumberUtils.toInt(paramConfigDto.getParamValue(), -1));
            log.info("连锁门店处方推审方远程配置,pref:{},pushRemoteAudit:{}", ppDto.getPref(), pushRemoteAudit);
            if (pushRemoteAudit) {
                // prescriptionPushRemoteAuditProducer.sendMessage(PrescriptionPushRemoteAuditEvent.builder().build()
                //     , LocalDateTime.now().plusSeconds(Optional.ofNullable(paramConfigDto.getExt().getPrescriptionRemoteToHeadTime()).orElse(60)));
            }
        }
    }


    @Override
    public void removePrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {
        // 移除门店(连锁)审方池 - 目前仅支持远程审方处方
        TenantDto tenantDto = tenantApi.getTenant(ppDto.getTenantId());

        if (Objects.equals(ppDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            pharmacistAuditRedisService.prescriptionWaitingReviewChainPoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), ppDto.getExt().getHeadTenantId(), ppDto.getPref());
        } else {
            pharmacistAuditRedisService.prescriptionWaitingReviewDrugstorePoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), ppDto.getTenantId(), ppDto.getPref());
        }
    }

    /**
     * 获取当前门店药师锁绑定审方池Key - 缓存5s
     */
    @Cacheable(cacheNames = RedisKeyConstants.PHARMACIST_PRESCRIPTION_WAITING_REVIEW_DRUGSTORE_POOL_KEYS + "#5s", key = "#pharmacist.id")
    public List<String> getDrugstorePoolKeys(InquiryPharmacistDO pharmacist) {
        // 获取门店药师锁绑定门店信息
        List<TenantUserRelationDto> pharmacistBindStoreList = inquiryPharmacistService.getPharmacistBindStoreList();

        Set<Long> tenantIds = CollectionUtils.convertSet(pharmacistBindStoreList, TenantUserRelationDto::getTenantId);

        List<Integer> medicineTypes = PharmacistQualificationEnum.convertMedicineTypeFromCode(PharmacistQualificationEnum.fromCode(pharmacist.getQualification()).getCode());
        // 门店药师 组装门店审方池Key
        List<String> drugstorePoolKeys = Optional.ofNullable(tenantIds).orElse(new HashSet<>()).stream()
            .flatMap(tenantId -> medicineTypes.stream()
                .map(medicineType -> RedisKeyConstants.getPrescriptionWaitingReviewDrugstorePoolKey(
                    EnvTagEnum.getByEnv(pharmacist.getEnvTag()), MedicineTypeEnum.fromCode(medicineType), tenantId))
            ).collect(Collectors.toList());

        // 连锁药师 组装连锁总部审方池Key
        if (Objects.equals(pharmacist.getPharmacistNature(), PharmacistNatureEnum.HEAD_PHARMACIST.getCode())) {

            drugstorePoolKeys.addAll(Optional.of(pharmacistBindStoreList.stream()
                    .filter(t -> Objects.equals(t.getTenantType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode()))
                    .map(TenantUserRelationDto::getTenantId).collect(Collectors.toSet())).orElse(new HashSet<>())
                .stream().flatMap(tenantId -> medicineTypes.stream()
                    .map(medicineType -> RedisKeyConstants.getPrescriptionWaitingReviewChainPoolKey(
                        EnvTagEnum.getByEnv(pharmacist.getEnvTag()), MedicineTypeEnum.fromCode(medicineType), tenantId))
                ).toList());
        }

        return drugstorePoolKeys;
    }


    /**
     * 获取当前药师待审方数量
     */
    @Override
    public CommonResult<Long> waitReceiveCountSelf(InquiryPharmacistDO pharmacist) {
        // 1.获取当前药师 处方池
        List<String> drugstorePoolKeys = getSelf().getDrugstorePoolKeys(pharmacist);
        // 2. 获取所在门店待审数量
        Long prescriptionCount = pharmacistAuditRedisService.prescriptionWaitingReviewPoolCount(drugstorePoolKeys);

        return CommonResult.success(Optional.ofNullable(prescriptionCount).orElse(0L));
    }

    /**
     * 领取一个最早的待审处方
     */
    @Override
    public CommonResult<InquiryPharmacistPrescriptionDTO> receivePrescriptionSelf(InquiryPharmacistDO pharmacist) {
        // 1.获取当前药师 处方池
        List<String> drugstorePoolKeys = getSelf().getDrugstorePoolKeys(pharmacist);
        // 2.弹出一个最早待审的处方
        String pref = pharmacistAuditRedisService.prescriptionWaitingReviewPoolPopMin(drugstorePoolKeys);
        if (StringUtils.isBlank(pref)) {
            return CommonResult.error(PRESCRIPTION_AUDIT_DISTRIBUTE_FAIL);
        }

        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder()
            .pref(pref)
            .auditorType(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode())
            .status(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode())
            .distributeStatus(DistributeStatusEnum.WAITING.getCode()).build());

        if (prescription == null) {
            return CommonResult.error(PRESCRIPTION_AUDIT_DISTRIBUTE_FAIL);
        }

        // 3.分配处方给当前药师
        boolean distributed = inquiryPrescriptionApi.distributePharmacist(prescription.getId(), pharmacist.getUserId());
        if (!distributed) {
            return receivePrescriptionSelf(pharmacist); // 抢不到继续领取,直到抢到或者为空
            // return CommonResult.error(PRESCRIPTION_AUDIT_DISTRIBUTE_FAIL);
        }
        return CommonResult.success(InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription));
    }


    @Override
    public boolean auditOutTime(InquiryPharmacistDO pharmacist, InquiryPharmacistPrescriptionDTO prescription) {
        final boolean auditOutTime = super.auditOutTime(pharmacist, prescription);
        if (auditOutTime) {
            // 放回门店药师审方池
            pushPrescriptionAuditPool(prescription);
            log.info("药师审方超时,放回门店审方池,处方pref:{}", prescription.getPref());
        }
        return auditOutTime;
    }
}
