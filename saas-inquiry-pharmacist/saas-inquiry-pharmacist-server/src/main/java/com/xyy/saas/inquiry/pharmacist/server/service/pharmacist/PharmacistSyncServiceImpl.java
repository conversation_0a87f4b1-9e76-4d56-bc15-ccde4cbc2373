package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.pharmacist.server.config.InquiryPharmacistForwardClient;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailRespDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistSyncQueryDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 13:40
 */
@Service
public class PharmacistSyncServiceImpl implements PharmacistSyncService {

    @Resource
    private InquiryPharmacistForwardClient inquiryPharmacistForwardClient;

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Resource
    private DictDataApi dictDataApi;

    @Autowired
    private FileApi fileApi;

    @Autowired
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Override
    public CommonResult<PageResult<InquiryPharmacistForwardRespVO>> queryUserDoctorPharmacistLists(PharmacistSyncQueryDto dto) {
        dto.setPageNum(dto.getPageNo());
        ForwardResult<ForwardPageResultDto<InquiryPharmacistForwardRespVO>> forwardResult = inquiryPharmacistForwardClient.queryUserDoctorPharmacistLists(dto);
        if (forwardResult.isSuccess()) {
            return CommonResult.success(new PageResult(forwardResult.getResult().getList(), forwardResult.getResult().getTotal()));
        }
        return CommonResult.error(forwardResult.getMsg());
    }


    @Override
    public CommonResult<InquiryPharmacistRespVO> queryUserDoctorPharmacist(String guid) {
        ForwardResult<PharmacistForwardDetailRespDto> forwardResult = inquiryPharmacistForwardClient.syncUserDoctorPharmacist(guid);
        if (!forwardResult.isSuccess() || forwardResult.getResult() == null) {
            return CommonResult.error(forwardResult.getMsg());
        }
        PharmacistForwardDetailDto dto = forwardResult.getResult().getDto();
        InquiryPharmacistRespVO pharmacistRespVO = InquiryPharmacistConvert.INSTANCE.convertSync(dto);
        Optional.ofNullable(forwardResult.getResult().getCaInfo()).ifPresent(l -> pharmacistRespVO.setSignatureUrl(l.getSignUrl()));
        // 处理字典
        Optional.ofNullable(dictDataApi.parseDictData(null, "formal_level", dto.getDocEdu()))
            .ifPresent(d -> pharmacistRespVO.setFormalLevel(NumberUtils.toInt(d.getValue(), 1))); // 学历

        return CommonResult.success(pharmacistRespVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Long> createInquiryPharmacist(InquiryPharmacistSaveReqVO createReqVO) {
        ForwardResult<PharmacistForwardDetailRespDto> forwardResult = inquiryPharmacistForwardClient.syncUserDoctorPharmacist(createReqVO.getGuid());
        if (!forwardResult.isSuccess() || forwardResult.getResult() == null) {
            return CommonResult.error(forwardResult.getMsg());
        }
        AdminUserRespDTO user = adminUserApi.getUserByMobile(createReqVO.getMobile());
        // 存在任意三要素不一致的情况
        if (user != null && (!StringUtils.equals(user.getNickname(), forwardResult.getResult().getDto().getDocName())
            || !Objects.equals(user.getSex(), forwardResult.getResult().getDto().getSex())
            || !StringUtils.equals(user.getIdCard(), forwardResult.getResult().getDto().getIdCard()))) {
            return CommonResult.error("当前用户基础信息已存在，且三要素不匹配(姓名、性别、身份证号),请检查处理一致后再操作同步");
        }
        // 确认同步时 处理图片上传
        handlePharmacistImgUpload(createReqVO);

        // 判断医生新增或者修改
        InquiryPharmacistDO inquiryPharmacistDO = user != null ? inquiryPharmacistService.getPharmacistByUserId(user.getId()) : null;
        if (inquiryPharmacistDO == null) {
            inquiryPharmacistDO = inquiryPharmacistService.createInquiryPharmacistSystem(createReqVO.setAuditStatus(AuditStatusEnum.APPROVED.getCode()));
        } else {
            inquiryPharmacistService.updateInquiryPharmacistSystem(createReqVO.setUserId(inquiryPharmacistDO.getUserId()).setId(inquiryPharmacistDO.getId()));
        }

        // 创建CA信息
        SyncCreateCaDto syncCreateCaDto = InquiryPharmacistConvert.INSTANCE.convertPharmacistCa(inquiryPharmacistDO, forwardResult.getResult().getCaInfo(), forwardResult.getResult().getPersonInfo());
        inquirySignatureCaAuthApi.createSyncSignatureCaAuth(syncCreateCaDto);

        return CommonResult.success(inquiryPharmacistDO.getId());
    }


    private static final String[] SIGN_PREFIX = new String[]{"?sign=", "?token="};

    /**
     * 异步处理图像上传
     *
     * @param detailRespVO
     */
    public void handlePharmacistImgUpload(InquiryPharmacistSaveReqVO detailRespVO) {
        CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getPhoto(), SIGN_PREFIX)) {
                    detailRespVO.setPhoto(FileApiUtil.createFile(detailRespVO.getPhoto())); // 头像
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                for (InquiryProfessionIdentificationDto identification : detailRespVO.getProfessionIdentifications()) {
                    identification.setCertificateImgUrl(Arrays.stream(StringUtils.split(identification.getCertificateImgUrl(), ",")) // 资质
                        .map(u -> StringUtils.containsAny(u, SIGN_PREFIX) ? FileApiUtil.createFile(u) : u).collect(Collectors.joining(",")));
                }
            }).exceptionally(e -> null)
        ).join();
    }
}
