package com.xyy.saas.inquiry.pharmacist.server.service.audit;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeMessageDTO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 处方审核记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionAuditService {

    /**
     * 获取当前药师待审方数量
     */
    CommonResult<Long> waitReceiveCount();

    /**
     * 领取一个最早的处方
     */
    CommonResult<InquiryPrescriptionReceiveVO> receivePrescription();

    /**
     * 获取处方审核超时时间
     *
     * @return
     */
    CommonResult<Integer> getAuditTimeout();

    /**
     * 处方审核通过
     *
     * @param auditVO 审核参数
     * @return 成功与否
     */
    CommonResult<Boolean> auditPass(InquiryPrescriptionAuditVO auditVO);

    /**
     * 处方审核驳回
     *
     * @param auditVO 审核参数
     * @return 成功与否
     */
    CommonResult<Boolean> auditReject(InquiryPrescriptionAuditVO auditVO);

    /**
     * 处方审核超时处理
     *
     * @param msg 审核消息
     */
    CommonResult<Boolean> auditOutTime(PrescriptionAuditOutTimeMessageDTO msg);


    /**
     * 远程处方，操作审核池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionOperateAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 远程处方，批量移除审核池
     *
     * @param auditDto
     */
    void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 审方是否可以视频
     *
     * @param pref 处方编号
     * @return
     */
    boolean auditCanVideoCall(String pref);

    /**
     * 远程处方，开始视频
     *
     * @param pref 处方编号
     * @return
     */
    CommonResult<Boolean> auditStartVideoCall(String pref);

    /**
     * 创建处方审核记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPrescriptionAudit(@Valid InquiryPrescriptionAuditSaveReqVO createReqVO);

    /**
     * 更新处方审核记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePrescriptionAudit(@Valid InquiryPrescriptionAuditSaveReqVO updateReqVO);

    /**
     * 删除处方审核记录
     *
     * @param id 编号
     */
    void deletePrescriptionAudit(Long id);

    /**
     * 获得处方审核记录
     *
     * @param id 编号
     * @return 处方审核记录
     */
    InquiryPrescriptionAuditDO getPrescriptionAudit(Long id);

    /**
     * 获得处方审核记录分页
     *
     * @param pageReqVO 分页查询
     * @return 处方审核记录分页
     */
    PageResult<InquiryPrescriptionAuditDO> getPrescriptionAuditPage(InquiryPrescriptionAuditPageReqVO pageReqVO);

    /**
     * 条件查询处方审核记录
     *
     * @param pageReqVO
     * @return
     */
    List<InquiryPrescriptionAuditDO> selectListByCondition(InquiryPrescriptionAuditPageReqVO pageReqVO);

}