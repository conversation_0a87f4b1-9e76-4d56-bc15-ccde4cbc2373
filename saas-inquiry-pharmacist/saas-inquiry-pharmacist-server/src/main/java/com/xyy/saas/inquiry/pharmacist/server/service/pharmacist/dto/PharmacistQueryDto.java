package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto;

import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 13:15
 */
@Data
@Builder
public class PharmacistQueryDto {

    /**
     * 审核状态 0、待审核  1、审核通过  2、审核驳回 {@link AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 药师执业资格,中药或西药
     */
    private Integer qualification;

    private List<Integer> qualifications;

    /**
     * 药师类型 平台药师 / 门店药师 / 医院药师 {@link PharmacistTypeEnum}
     */
    private Integer pharmacistType;

    /**
     * 药师工作类型 1全职/ 2兼职
     * <p>
     * {@link DoctorJobTypeEnum}
     */
    private Integer jobType;


}
