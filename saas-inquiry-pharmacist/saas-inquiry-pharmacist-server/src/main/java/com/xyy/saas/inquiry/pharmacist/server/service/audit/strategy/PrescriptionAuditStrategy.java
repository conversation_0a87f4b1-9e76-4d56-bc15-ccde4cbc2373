package com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_AUDIT_PARAM_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_CHAIN_AUDIT_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.PRESCRIPTION_AUDITED_OR_TIMEOUT;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantUserRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistNatureEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryRemoteAuditApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.constant.PrescriptionAuditConstant;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.signature.InquiryPharmacistSignatureConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.audit.PharmacistAuditRedisService;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeMessageDTO;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.PrescriptionAuditOutTimeProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistImService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.prescription.InquirySignaturePrescriptionApi;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.PrescriptionTemplateApi;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;
import com.xyy.saas.inquiry.util.HttpHeaderUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 处方审方策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 17:33
 */
@Component
@Slf4j
public abstract class PrescriptionAuditStrategy {

    @Resource
    @Lazy
    protected InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    @Resource
    protected PharmacistAuditRedisService pharmacistAuditRedisService;

    @DubboReference
    protected InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    protected InquiryApi inquiryApi;

    @Resource
    protected AdminUserApi adminUserApi;

    @Resource
    protected TenantApi tenantApi;

    @DubboReference
    private TenantUserRelationApi tenantUserRelationApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    protected InquiryPharmacistService inquiryPharmacistService;

    @DubboReference
    protected InquirySignaturePrescriptionApi inquirySignaturePrescriptionApi;

    @DubboReference
    protected InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Resource
    private PrescriptionAuditOutTimeProducer prescriptionAuditOutTimeProducer;

    @Resource
    private InquiryPharmacistImService inquiryPharmacistImService;

    @Resource
    protected DingService dingService;

    @DubboReference
    protected PrescriptionTemplateApi prescriptionTemplateApi;

    @Resource
    private InquiryRemoteAuditApi inquiryRemoteAuditApi;

    @DubboReference
    protected InquiryOptionConfigApi inquiryOptionConfigApi;

    @DubboReference
    protected TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private InquirySignatureImageApi inquirySignatureImageApi;


    public PrescriptionAuditStrategy getSelf() {
        return SpringUtil.getBean(this.getClass());
    }

    /**
     * 映射门店审核类型 - 医生开方完成后推审方池使用
     */
    public abstract PrescriptionAuditTypeEnum getPrescriptionAuditType();

    /**
     * 映射药师资质类型 - 药师前端审核接口使用
     */
    public abstract PharmacistTypeEnum getPharmacistType();

    /**
     * 处方推送审方池
     */
    public abstract void pushPrescriptionAuditPool(InquiryPharmacistPrescriptionDTO pharmacistPrescriptionDTO);

    /**
     * 移除审方池
     *
     * @param pharmacistPrescriptionDTO
     */
    public void removePrescriptionAuditPool(InquiryPharmacistPrescriptionDTO pharmacistPrescriptionDTO) {

    }


    /**
     * 获取当前药师待审方数量
     */
    public CommonResult<Long> waitReceiveCount(InquiryPharmacistDO pharmacist) {
        CommonResult<Long> result = getSelf().waitReceiveCountSelf(pharmacist);
        // 如果自己预占了一个，待审数量+1
        Pair<String, Long> longPair = pharmacistAuditRedisService.prescriptionAuditReceiveGet(pharmacist.getId());
        if (longPair != null) {
            result.setData(Optional.ofNullable(result.getData()).orElse(0L) + 1);
        }
        return result;
    }


    public abstract CommonResult<Long> waitReceiveCountSelf(InquiryPharmacistDO pharmacist);


    /**
     * 领取一个最早的待审核处方
     *
     * @param pharmacist 当前审核药师
     * @return 处方
     */
    public CommonResult<InquiryPrescriptionReceiveVO> receivePrescription(InquiryPharmacistDO pharmacist) {

        // 1.判断当前药师是否已领取一个待审核的处方 Redis<药师id:处方pref:审核id>
        Pair<String, Long> receiveGet = pharmacistAuditRedisService.prescriptionAuditReceiveGet(pharmacist.getId());
        if (receiveGet != null) {
            InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(receiveGet.getFirst()).build());
            if (prescription != null && Objects.equals(prescription.getStatus(), PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode())
                && Objects.equals(pharmacist.getUserId(), prescription.getDistributeUserId())) {

                InquiryPharmacistPrescriptionDTO prescriptionDTO = InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription);
                fillPrescriptionInfo(prescriptionDTO);

                return CommonResult.success(InquiryPharmacistPrescriptionConvert.INSTANCE.convertReceiveVO(prescriptionDTO,
                    inquiryApi.getInquiryRecordDetail(prescription.getInquiryPref()), receiveGet.getSecond(), getAuditTimeOut(prescriptionDTO)));
            }
            pharmacistAuditRedisService.prescriptionAuditReceiveDel(pharmacist.getId());
        }

        // 2. 不同类型药师领取处方
        CommonResult<InquiryPharmacistPrescriptionDTO> commonResult = getSelf().receivePrescriptionSelf(pharmacist);
        if (commonResult.isError() || commonResult.getData() == null) {
            return CommonResult.error(commonResult.getCode(), commonResult.getMsg());
        }

        // 3.创建审核记录
        InquiryPharmacistPrescriptionDTO prescriptionDTO = commonResult.getData();
        fillPrescriptionInfo(prescriptionDTO);

        Long auditRecordId = inquiryPrescriptionAuditService.createPrescriptionAudit(
            InquiryPharmacistAuditConvert.INSTANCE.convertCreateAuditRecord(prescriptionDTO, pharmacist.getUserId(), pharmacist.getName(), SignatureStatusEnum.WAITING));
        //  存储领方redis
        pharmacistAuditRedisService.prescriptionAuditReceiveSet(pharmacist.getId(), prescriptionDTO.getPref(), auditRecordId);

        // 4.发超时审方确认MQ - 视频审方30min 其他默认60s
        prescriptionAuditOutTimeProducer.sendMessage(PrescriptionAuditOutTimeEvent.builder()
                .msg(PrescriptionAuditOutTimeMessageDTO.builder().pharmacistId(pharmacist.getId()).pref(prescriptionDTO.getPref()).auditRecordId(auditRecordId).build()).build()
            , LocalDateTime.now().plusSeconds(getAuditTimeOut(prescriptionDTO)));

        return CommonResult.success(InquiryPharmacistPrescriptionConvert.INSTANCE.convertReceiveVO(prescriptionDTO, inquiryApi.getInquiryRecordDetail(prescriptionDTO.getInquiryPref()), auditRecordId, getAuditTimeOut(prescriptionDTO)));
    }

    // 填充其他信息远程审方医院信息
    private void fillPrescriptionInfo(InquiryPharmacistPrescriptionDTO prescriptionDTO) {
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(prescriptionDTO.getInquiryPref());
        prescriptionDTO.setHospitalName(StringUtils.defaultIfBlank(prescriptionDTO.getHospitalName(), inquiryRecord.getHospitalName()));
    }

    // 获取审方超时时间
    public Integer getAuditTimeOut(InquiryPharmacistPrescriptionDTO prescriptionDTO) {

        boolean prescriptionAuditVideo = ClientChannelTypeEnum.isPc(HttpHeaderUtil.getClientChannelType()) && prescriptionDTO.getExt() != null && prescriptionDTO.getExt().isPrescriptionAuditVideo();

        return BooleanUtil.isTrue(prescriptionAuditVideo)
            ? NumberUtils.toInt(configApi.getConfigValueByKey(PrescriptionAuditConstant.PHARMACIST_VIDEO_AUDIT_TIMEOUT), 30 * 60)
            : NumberUtils.toInt(configApi.getConfigValueByKey(PrescriptionAuditConstant.PHARMACIST_AUDIT_TIMEOUT), 60);
    }

    public abstract CommonResult<InquiryPharmacistPrescriptionDTO> receivePrescriptionSelf(InquiryPharmacistDO pharmacist);

    /**
     * 审核通过处方
     *
     * @param pharmacist 当前审方药师
     * @param auditVO    审方参数
     * @return 通过与否
     */
    @Transactional(rollbackFor = Exception.class)
    public InquiryPrescriptionRespDTO auditPass(InquiryPharmacistDO pharmacist, InquiryPrescriptionAuditVO auditVO) {
        // 1.校验处方状态是否通过 或 非当前药师
        InquiryPrescriptionRespDTO prescription = checkPrescriptionAuditStatus(auditVO, pharmacist);

        // 2.修改处方审核记录时间和待审核
        inquiryPrescriptionAuditService.updatePrescriptionAudit(
            InquiryPrescriptionAuditSaveReqVO.builder().id(auditVO.getAuditRecordId()).auditStatus(PrescriptionAuditStatusEnum.AUDITING.getCode()).auditorApprovalTime(LocalDateTime.now()).auditorSignatureTime(LocalDateTime.now()).build());

        // 3.调用签章追加审核人
        PrescriptionSignatureAuditDto auditDto = InquiryPharmacistSignatureConvert.INSTANCE.convertSignAuditDto(InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription)
            , auditVO.setAuditorTypeEnum(AuditorTypeEnum.convertPharmacistType(this.getPharmacistType().getCode()))
            , adminUserApi.getUser(pharmacist.getUserId()));

        CommonResult<?> commonResult = TenantUtils.execute(prescription.getTenantId(), () -> inquirySignaturePrescriptionApi.auditPrescription(auditDto));
        if (commonResult.isError()) {
            throw exception0(commonResult.getCode(), commonResult.getMsg());
        }
        // 4.修改处方状态审核中
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(prescription.getId()).pharmacistPref(pharmacist.getPref())
            .pharmacistName(pharmacist.getName()).auditPrescriptionTime(LocalDateTime.now()).status(PrescriptionStatusEnum.APPROVAL_ING.getStatusCode()).build());

        // 5.删除领方Redis
        pharmacistAuditRedisService.prescriptionAuditReceiveDel(pharmacist.getId());

        return prescription;
    }


    /**
     * 审核驳回处方 - 锁处方单号 防止多个药师同时审核一张处方
     *
     * @param pharmacist 当前审方药师
     * @param auditVO    审方参数
     * @return 通过与否
     */
    @Transactional(rollbackFor = Exception.class)
    public InquiryPrescriptionRespDTO auditReject(InquiryPharmacistDO pharmacist, InquiryPrescriptionAuditVO auditVO) {
        // 1.校验处方状态是否通过 或 非当前药师
        InquiryPrescriptionRespDTO prescription = checkPrescriptionAuditStatus(auditVO, pharmacist);
        // 2.记录审核驳回
        InquiryPharmacistPrescriptionDTO prescriptionDTO = InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription);
        InquiryPrescriptionAuditSaveReqVO auditSaveReqVO = InquiryPharmacistAuditConvert.INSTANCE.convertCreateAuditRejectRecord(prescriptionDTO, adminUserApi.getUser(pharmacist.getUserId()), auditVO);
        inquiryPrescriptionAuditService.updatePrescriptionAudit(auditSaveReqVO.setId(auditVO.getAuditRecordId()));
        // 处理审核驳回水印
        String url = handleAuditRejectWatermark(prescription);
        InquiryPrescriptionUpdateDTO prescriptionUpdateDTO = InquiryPrescriptionUpdateDTO.builder().id(prescription.getId())
            .status(PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode()).invalidReason(auditVO.getAuditorRejectedReason()).auditPrescriptionTime(LocalDateTime.now())
            .pharmacistName(pharmacist.getName()).pharmacistPref(pharmacist.getPref())
            .prescriptionPdfUrl(url).prescriptionImgUrl(url).build();
        // 3.修改处方状态已驳回
        inquiryPrescriptionApi.updateInquiryPrescription(prescriptionUpdateDTO);
        // 4.删除领方Redis
        pharmacistAuditRedisService.prescriptionAuditReceiveDel(pharmacist.getId());

        return prescription;
    }

    private String handleAuditRejectWatermark(InquiryPrescriptionRespDTO prescription) {
        // 远程审方处理驳回水印
        if (Objects.equals(prescription.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode()) && StringUtils.isNotBlank(prescription.getExt().getRemotePrescriptionImg())) {
            // 为远程审方的处方图片添加【远程审方】水印
            return inquirySignatureImageApi.signatureImageMerge(
                InquirySignatureImageDto.builder()
                    .sourceUrl(prescription.getExt().getRemotePrescriptionImg())
                    .mergeUrl(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_AUDIT_REJECT_SIGNATURE_URL))
                    .offset(NumberUtil.parseDouble(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_SIGNATURE_OFFSET), -0.5))
                    .build());
        }
        return null;
    }

    /**
     * 校验处方审核状态
     *
     * @param auditVO    审核vo
     * @param pharmacist 药师
     * @return 待审处方
     */
    private InquiryPrescriptionRespDTO checkPrescriptionAuditStatus(InquiryPrescriptionAuditVO auditVO, InquiryPharmacistDO pharmacist) {
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(auditVO.getPref()).build());

        try {
            if (prescription == null
                || !PrescriptionStatusEnum.isCanAuditStatus(prescription.getStatus())
                || !Objects.equals(pharmacist.getUserId(), prescription.getDistributeUserId())) {
                throw exception(PRESCRIPTION_AUDITED_OR_TIMEOUT);
            }

            InquiryPrescriptionAuditDO prescriptionAudit = inquiryPrescriptionAuditService.getPrescriptionAudit(auditVO.getAuditRecordId());
            if (prescriptionAudit == null
                || !StringUtils.equals(prescriptionAudit.getPref(), auditVO.getPref())
                || !Objects.equals(prescriptionAudit.getAuditStatus(), PrescriptionAuditStatusEnum.PENDING.getCode())
                || !Objects.equals(prescriptionAudit.getAuditorId(), prescription.getDistributeUserId())) {
                throw exception(PRESCRIPTION_AUDITED_OR_TIMEOUT);
            }

            // 远程审方 校验
            if (Objects.equals(prescription.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {

                if (prescription.getExt().getHeadTenantId() == null
                    || (Objects.equals(auditVO.getAuditStatus(), PrescriptionAuditStatusEnum.APPROVED)
                    && auditVO.getCoordinate() == null)) {
                    throw exception(INQUIRY_PHARMACIST_AUDIT_PARAM_ERROR, "签名坐标为空或远程处方总部门店为空");
                }
                // 药师和门店没有关联 || 药师性质不是总部药师
                TenantUserRelationDto tenantUserRelation = tenantUserRelationApi.getAvailableTenantUserRelation(pharmacist.getUserId(), prescription.getExt().getHeadTenantId());
                if (tenantUserRelation == null
                    || !Objects.equals(pharmacist.getPharmacistNature(), PharmacistNatureEnum.HEAD_PHARMACIST.getCode())) {
                    throw exception(INQUIRY_PHARMACIST_CHAIN_AUDIT_ERROR); // 前端给出弹框确认提示
                }

                // 处方门店 和 连锁总部没关联
                TenantDto tenantDto = tenantApi.getTenant(prescription.getTenantId());
                if (!Objects.equals(tenantDto.getHeadTenantId(), prescription.getExt().getHeadTenantId())) {
                    log.warn("处方门店和连锁总部没有关联,处方pref:{},处方门店id:{},连锁总部id:{}", prescription.getPref(), prescription.getTenantId(), prescription.getExt().getHeadTenantId());
                    // 删除并移除审方池
                    inquiryRemoteAuditApi.batchCancelRemoteInquiry(Collections.singletonList(prescription.getInquiryPref()));
                    throw exception(INQUIRY_PHARMACIST_CHAIN_AUDIT_ERROR); // 前端给出弹框确认提示
                }
            }
        } catch (ServiceException e) {
            // 异常 - 删除领方预占Redis,药师可以重新获取新处方
            pharmacistAuditRedisService.prescriptionAuditReceiveDel(pharmacist.getId());
            throw exception0(e.getCode(), e.getMessage());
        }
        return prescription;
    }

    /**
     * 审方超时 重放审方池
     *
     * @param pharmacist   药师信息
     * @param prescription 处方信息
     * @return ture-超时归还 , false-无需操作
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean auditOutTime(InquiryPharmacistDO pharmacist, InquiryPharmacistPrescriptionDTO prescription) {
        // 校验审核表状态
        InquiryPrescriptionAuditDO prescriptionAudit = inquiryPrescriptionAuditService.getPrescriptionAudit(prescription.getAuditRecordId());
        if (prescriptionAudit == null || !Objects.equals(prescriptionAudit.getAuditStatus(), PrescriptionAuditStatusEnum.PENDING.getCode())) {
            return false;
        }
        // 根据当前审核人,还原成待审方
        inquiryPrescriptionApi.releasePharmacist(prescription.getId(), pharmacist.getUserId());
        // 标记超时的审核记录
        inquiryPrescriptionAuditService.updatePrescriptionAudit(InquiryPrescriptionAuditSaveReqVO.builder().id(prescriptionAudit.getId()).auditStatus(PrescriptionAuditStatusEnum.TIMEOUT.getCode()).build());
        // 删除领方Redis
        pharmacistAuditRedisService.prescriptionAuditReceiveDel(pharmacist.getId());

        // 远程视频审方且是PC的，超时直接关闭掉
        if (prescription.getExt() != null && prescription.getExt().isPrescriptionAuditVideo() && ClientChannelTypeEnum.isPc(prescriptionAudit.getClientChannelType())) {
            log.info("PC 视频审核超时,处方关闭,处方pref:{}", prescription.getPref());
            inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(prescription.getId()).status(PrescriptionStatusEnum.CANCELED.getStatusCode()).build());
            // 通知门店
            inquiryPharmacistImService.sendPrescriptionAuditMsgToPatient(prescription.getInquiryPref());
            return false;
        }

        return true;
    }

}
