package com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist;

import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.im.api.message.dto.ImEventMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 14:07
 * @Description: 药师IM转换
 **/
@Mapper
public interface InquiryPharmacistImConvert {

    InquiryPharmacistImConvert INSTANCE = Mappers.getMapper(InquiryPharmacistImConvert.class);

    default InquiryImMessageDto convertPrescriptionAuditMsgToPatient(String patientImAccount, ImEventPushEnum eventEnum , String inquiryPref){
        return InquiryImMessageDto.builder().toAccount(patientImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder().inquiryPref(inquiryPref).build()
        ).build()).build();
    }
}
