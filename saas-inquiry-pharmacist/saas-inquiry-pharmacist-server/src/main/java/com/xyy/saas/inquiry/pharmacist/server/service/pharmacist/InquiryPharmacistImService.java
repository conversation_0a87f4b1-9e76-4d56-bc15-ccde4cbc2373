package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 13:51
 * @Description: 药师IM相关服务
 **/
public interface InquiryPharmacistImService {

    /**
     * 发送处方审核消息给患者
     *
     * @param inquiryPref 问诊单号
     */
    void sendPrescriptionAuditMsgToPatient(String inquiryPref);

    /**
     * 发送处方笺绘制完成消息给患者
     *
     * @param inquiryPref 问诊单号
     */
    void sendPrescriptionDrawnFinish(String inquiryPref);

    /**
     * 发送审核处方视频给患者
     *
     * @param inquiryPref 问诊单号
     */
    void sendAuditPrescriptionVideoToPatient(String inquiryPref);

}
