package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistImConvert;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 13:53
 * @Description: 药师IM服务实现
 **/
@Service
@Slf4j
public class InquiryPharmacistImServiceImpl implements InquiryPharmacistImService {

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryImMessageApi inquiryImMessageApi;

    @Override
    public void sendPrescriptionAuditMsgToPatient(String inquiryPref) {
        sendPrescriptionMsg(inquiryPref, ImEventPushEnum.PRESCRIPTION_AUDIT);
    }

    @Override
    public void sendPrescriptionDrawnFinish(String inquiryPref) {
        sendPrescriptionMsg(inquiryPref, ImEventPushEnum.ISSUE_PRESCRIPTION);
    }

    @Override
    public void sendAuditPrescriptionVideoToPatient(String inquiryPref) {
        sendPrescriptionMsg(inquiryPref, ImEventPushEnum.PRESCRIPTION_AUDIT_VIDEO);
    }

    private void sendPrescriptionMsg(String inquiryPref, ImEventPushEnum imEventPushEnum) {
        ThreadPoolManager.execute(() -> {
            try {
                // 查询问诊信息
                InquiryRecordDto recordDto = inquiryApi.getInquiryRecord(inquiryPref);
                // 查询商家IM账号
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(recordDto.getCreator()), recordDto.getClientChannelType());
                // 发送消息通知商家处理
                InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertPrescriptionAuditMsgToPatient(patientIm, imEventPushEnum, inquiryPref);
                // 发送处方审核消息通知商家端
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{},发送处方审核消息通知商家失败,原因：{}", inquiryPref, e.getMessage(), e);
            }
        });
    }
}
