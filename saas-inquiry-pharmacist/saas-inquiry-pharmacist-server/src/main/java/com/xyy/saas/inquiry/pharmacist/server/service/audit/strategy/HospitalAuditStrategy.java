package com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 医院审方策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 17:33
 */
@Component
@Slf4j
public class HospitalAuditStrategy extends PrescriptionAuditStrategy {

    @Override
    public PrescriptionAuditTypeEnum getPrescriptionAuditType() {
        return null;
    }

    @Override
    public PharmacistTypeEnum getPharmacistType() {
        return PharmacistTypeEnum.HOSPITAL;
    }


    /**
     * 推送审方池
     */
    @Override
    public void pushPrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {
        // 判断医院药师是否真人审核,真人推审方池子,自动审核则轮询一个药师审核
        // 更新处方审核人类型
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(ppDto.getId()).pref(ppDto.getPref()).auditorType(AuditorTypeEnum.HOSPITAL_PHARMACIST.getCode()).build());


    }

    /**
     * 获取当前药师待审方数量
     */
    @Override
    public CommonResult<Long> waitReceiveCountSelf(InquiryPharmacistDO pharmacist) {
        return CommonResult.success(0L);
    }

    /**
     * 领取一个最早的处方
     */
    @Override
    public CommonResult<InquiryPharmacistPrescriptionDTO> receivePrescriptionSelf(InquiryPharmacistDO pharmacist) {
        return null;
    }


}
