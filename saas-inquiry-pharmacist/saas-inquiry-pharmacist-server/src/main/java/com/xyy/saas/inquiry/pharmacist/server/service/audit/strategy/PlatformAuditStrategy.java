package com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy;

import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.PRESCRIPTION_AUDIT_RECEIVE_END;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistQualificationEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.pharmacist.PharmacistAuditAreaTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.DistributeStatusEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

/**
 * 平台药师审方策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 17:33
 */
@Component
@Slf4j
public class PlatformAuditStrategy extends PrescriptionAuditStrategy {


    @Override
    public PrescriptionAuditTypeEnum getPrescriptionAuditType() {
        return PrescriptionAuditTypeEnum.PLATFORM;
    }

    @Override
    public PharmacistTypeEnum getPharmacistType() {
        return PharmacistTypeEnum.PLATFORM;
    }

    /**
     * 推送审方池
     * <p>
     * 根据outPrescriptionTime() 打分
     * <p>
     * 根据tenantDto.getProvince()判断当前门店所在区域是 全国可审 还是 当地可审
     */
    @Override
    public void pushPrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {
        // 更新处方审核人类型
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(ppDto.getId()).pref(ppDto.getPref()).auditorType(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode()).build());

        TenantDto tenantDto = tenantApi.getTenant(ppDto.getTenantId());
        // 获取地区池
        String optionValue = inquiryOptionConfigApi.getInquiryOptionValue(NumberUtils.toLong(tenantDto.getProvinceCode(), 0), InquiryOptionTypeEnum.PRES_PHARMACIST_AREA_TYPE);
        String regionAreaCode = PharmacistAuditAreaTypeEnum.getLocalArea(optionValue, tenantDto.getProvinceCode());

        // 判断处方是否远程审方
        if (Objects.equals(ppDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            pharmacistAuditRedisService.prescriptionWaitingReviewRemotePlatformPoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), regionAreaCode, ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
        } else {
            pharmacistAuditRedisService.prescriptionWaitingReviewPlatformPoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), regionAreaCode, ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
        }
    }

    @Override
    public void removePrescriptionAuditPool(InquiryPharmacistPrescriptionDTO ppDto) {

        TenantDto tenantDto = tenantApi.getTenant(ppDto.getTenantId());

        // 获取地区池
        String optionValue = inquiryOptionConfigApi.getInquiryOptionValue(NumberUtils.toLong(tenantDto.getProvinceCode(), 0), InquiryOptionTypeEnum.PRES_PHARMACIST_AREA_TYPE);
        String regionAreaCode = PharmacistAuditAreaTypeEnum.getLocalArea(optionValue, tenantDto.getProvinceCode());

        // 判断处方是否远程审方
        if (Objects.equals(ppDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            pharmacistAuditRedisService.prescriptionWaitingReviewRemotePlatformPoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), regionAreaCode, ppDto.getPref());
        } else {
            pharmacistAuditRedisService.prescriptionWaitingReviewPlatformPoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), regionAreaCode, ppDto.getPref());
        }

    }

    /**
     * 获取当前药师待审方数量
     */
    @Override
    public CommonResult<Long> waitReceiveCountSelf(InquiryPharmacistDO pharmacist) {

        Long poolCount = pharmacistAuditRedisService.prescriptionWaitingReviewPlatformPoolCount(EnvTagEnum.getByEnv(pharmacist.getEnvTag()), PharmacistQualificationEnum.fromCode(pharmacist.getQualification()), pharmacist.getProvinceCode());

        return CommonResult.success(Optional.ofNullable(poolCount).orElse(0L));
    }

    /**
     * 领取一个最早的处方
     */
    @Override
    public CommonResult<InquiryPharmacistPrescriptionDTO> receivePrescriptionSelf(InquiryPharmacistDO pharmacist) {
        // 根据药师执业省份处理审方池地区
        String pref = pharmacistAuditRedisService.prescriptionWaitingReviewPlatformPoolPopMin(EnvTagEnum.getByEnv(pharmacist.getEnvTag()), PharmacistQualificationEnum.fromCode(pharmacist.getQualification()), pharmacist.getProvinceCode());
        if (StringUtils.isBlank(pref)) {
            return CommonResult.error(PRESCRIPTION_AUDIT_RECEIVE_END);
        }
        InquiryPrescriptionQueryDTO queryDTO = InquiryPrescriptionQueryDTO.builder()
            .pref(pref)
            .auditorType(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode())
            .status(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode())
            .distributeStatus(DistributeStatusEnum.WAITING.getCode()).build();
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(queryDTO);
        if (prescription == null || !PrescriptionStatusEnum.isCanAuditStatus(prescription.getStatus())) {
            return CommonResult.error(PRESCRIPTION_AUDIT_RECEIVE_END);
        }
        // 分配处方给当前药师
        boolean distributed = inquiryPrescriptionApi.distributePharmacist(prescription.getId(), pharmacist.getUserId());
        if (!distributed) {
            return receivePrescriptionSelf(pharmacist); // 抢不到继续领取,直到抢到或者为空
        }
        return CommonResult.success(InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription));
    }

    /**
     * 归还 - 平台审方池
     *
     * @param pharmacist   药师信息
     * @param prescription 处方信息
     * @return ture-超时归还 , false-无需操作
     */
    @Override
    public boolean auditOutTime(InquiryPharmacistDO pharmacist, InquiryPharmacistPrescriptionDTO prescription) {
        boolean auditOutTime = super.auditOutTime(pharmacist, prescription);
        if (auditOutTime) {
            // 放回平台药师审方池
            pushPrescriptionAuditPool(prescription);
        }
        return auditOutTime;
    }
}
