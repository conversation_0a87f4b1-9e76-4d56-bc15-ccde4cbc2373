package com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 问诊记录 Response VO")
@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRecordDetailRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @ExcelProperty("租户ID")
    private Long tenantId;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @ExcelProperty("租户名称")
    private String tenantName;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊单号")
    private String pref;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "19102")
    @ExcelProperty("患者编码")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者年龄")
    private String patientAge;

    @Schema(description = "患者性别 1、男   2、女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者性别 1、男   2、女")
    private Integer patientSex;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者身份证号")
    private String patientIdCard;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "14039")
    @ExcelProperty("互联网医院编码")
    private String hospitalPref;

    @Schema(description = "互联网医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("互联网医院名称")
    private String hospitalName;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科室编码")
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("科室名称")
    private String deptName;

    @Schema(description = "处方笺模版id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22203")
    @ExcelProperty("处方笺模版id")
    private Long preTempId;

    @Schema(description = "医生姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "30134")
    @ExcelProperty("医生姓名")
    private String doctorName;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30134")
    @ExcelProperty("医生编码")
    private String doctorPref;

    @Schema(description = "接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消")
    private Integer inquiryStatus;

    @Schema(description = "取消开方原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @ExcelProperty("取消开方原因")
    private String cancelReason;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("问诊方式  1、图文问诊  2、视频问诊  3、电话问诊")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("问诊业务类型 1、药店问诊  2、远程审方")
    private Integer inquiryBizType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("客户端渠类型 0、app  1、pc  2、小程序 ")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("客户端系统类型 eg : ios  ,  android")
    private String clientOsType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊渠道 0、荷叶 1、智慧脸  2、海典ERP")
    private Integer bizChannelType;

    @Schema(description = "用药类型：0西药  、1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用药类型：0西药  、1中药")
    private Integer medicineType;

    @Schema(description = "是否自动开方：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否自动开方：0 否  、 1是")
    private Integer autoInquiry;

    @Schema(description = "是否自动派单：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否自动派单：0 否  、 1是")
    private Integer isAutoGrabInquiry;


    @Schema(description = "不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方", example = "不好")
    @ExcelProperty("不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方")
    private Integer unableAutoReason;

    @Schema(description = "IM平台类型  0、腾讯IM", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("IM平台类型  0、腾讯IM")
    private Integer imPlatform;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单编号")
    private String orderNo;

    @Schema(description = "问诊扩展字段")
    @ExcelProperty("问诊扩展字段")
    private InquiryDetailExtDto ext;

    @Schema(description = "医生接诊时间")
    private LocalDateTime startTime;

    @InquiryDateType("startTime")
    @ExcelProperty("医生接诊时间")
    private String startTimeStr;

    @Schema(description = "问诊结束时间")
    private LocalDateTime endTime;

    @InquiryDateType("endTime")
    @ExcelProperty("问诊结束时间")
    private String endTimeStr;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @InquiryDateType("outPrescriptionTime")
    @Schema(description = "医师出方时间")
    private String outPrescriptionTimeStr;

    @Schema(description = "处方单号")
    private String prescriptionPref;

    @Schema(description = "医生剩余开方时间-单位秒", requiredMode = Schema.RequiredMode.REQUIRED, example = "72")
    private Integer remainingSecond;

    @Schema(description = "录音地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("录音地址")
    private String mp3Url;

    @Schema(description = "MP3状态 0 未视频 1 视频中 2 已合流 3 已编码 4 已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("MP3状态 0 未视频 1 视频中 2 已合流 3 已编码 4 已完成")
    private Integer mp3Status;

    @Schema(description = "视频地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("视频地址")
    private String mp4Url;

    @Schema(description = "im问诊记录", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("im问诊记录")
    private String imPdf;

    @Schema(description = "视频混流id", requiredMode = Schema.RequiredMode.REQUIRED, example = "72")
    @ExcelProperty("视频混流id")
    private String streamId;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18661")
    @ExcelProperty("任务ID")
    private String transcodingId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @InquiryDateType("createTime")
    @ExcelProperty("创建时间")
    private String createTimeStr;

    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常")
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期")
    private Integer gestationLactationValue;

    @Schema(description = "慢病病情需要 0 否  1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("慢病病情需要 0 否  1是")
    private Integer slowDisease;

    @Schema(description = "主诉")
    @ExcelProperty("主诉")
    private List<String> mainSuit;

    @Schema(description = "过敏史  eg：青霉素|头孢")
    @ExcelProperty("过敏史  eg：青霉素|头孢")
    private List<String> allergic;

    @Schema(description = "诊断编码")
    @ExcelProperty("诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", example = "王五")
    @ExcelProperty("诊断说明")
    private List<String> diagnosisName;

    @Schema(description = "个人史")
    @ExcelProperty("个人史")
    private String patientHisDesc;

    @Schema(description = "现病史")
    @ExcelProperty("现病史")
    private String currentIllnessDesc;

    @Schema(description = "线下就医处方或病历图片")
    @ExcelProperty("线下就医处方或病历图片")
    private List<String> offlinePrescriptions;

    @Schema(description = "预购药明细")
    @ExcelProperty("预购药明细")
    private InquiryProductDto preDrugDetail;

    @Schema(description = "备注说明")
    @ExcelProperty("备注说明")
    private String remarks;

    @Schema(description = "门诊号")
    private String iptOtpNo;

    @Schema(description = "医生医保编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String doctorMedicareNo;

    @Schema(description = "医疗机构编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    private String institutionCode;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer prescriptionType;

}