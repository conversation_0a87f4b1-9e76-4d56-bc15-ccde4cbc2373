package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryImConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 14:03
 * @Description: 问诊IM服务实现类
 **/
@Service
@Slf4j
public class InquiryImServiceImpl implements InquiryImService {

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryImMessageApi inquiryImMessageApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Override
    public void batchNotifyDoctorForInquiryChange(List<String> doctorList) {
        if (CollectionUtils.isEmpty(doctorList)) {
            return;
        }
        ThreadPoolManager.execute(() -> {
            try {
                log.info("批量推送问诊单变更事件给医生,医生列表：{}", JSON.toJSONString(doctorList));
                // 批量查询医生IM账号
                List<String> doctorImList = inquiryImUserApi.getDoctorImAccountListByDoctorPrefList(doctorList);
                // 构建IM消息
                InquiryImMessageDto messageDto = InquiryImConvert.INSTANCE.convertBatchSystemMsgForInquiryChangeToDoctor(doctorImList, ImEventPushEnum.SEND_INQUIRY);
                // 给医生IM推送问诊单变更消息
                inquiryImMessageApi.batchSendSystemMessage(messageDto);
            } catch (Exception exception) {
                log.error("批量推送问诊单变更事件给医生异常,医生列表：{},异常原因：{}", JSON.toJSONString(doctorList), exception);
            }
        });
    }

    /**
     * 生成医患沟通（医生小助）一问一答对话记录
     *
     * @param inquiryRecordDO 问诊单信息
     */
    @Override
    public void generateAutomaticChatRecord(InquiryRecordDO inquiryRecordDO) {
        // 查询问诊单详情
        InquiryRecordDetailDO inquiryRecordDetailDO = inquiryRecordDetailMapper.selectByRecordPref(inquiryRecordDO.getPref());
        if (ObjectUtils.isEmpty(inquiryRecordDetailDO) || ObjectUtils.isEmpty(inquiryRecordDetailDO.extGet()) || CollectionUtils.isEmpty(inquiryRecordDetailDO.extGet().getQuestionAnswerList())) {
            return;
        }
        // 获取医生IM账号
        String doctorIm = inquiryImUserApi.getDoctorImAccountByDoctorPref(inquiryRecordDO.getDoctorPref());
        // 获取商家IM账号
        String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(inquiryRecordDO.getCreator()), inquiryRecordDO.getClientChannelType());
        List<InquiryImMessageDto> questionAnswerMessageList = new ArrayList<>();
        inquiryRecordDetailDO.extGet().getQuestionAnswerList().forEach(questionAnswer -> {
            questionAnswerMessageList.addAll(InquiryImConvert.INSTANCE.convertQuestionAnswer2MessageList(questionAnswer, doctorIm, patientIm, inquiryRecordDO.getPref()));
        });
        if (CollectionUtils.isEmpty(questionAnswerMessageList)) {
            return;
        }
        // 批量保存一问一答数据（不走IM，避免回调延迟）
        inquiryImMessageApi.batchInsertMessage(questionAnswerMessageList);
        // 组装用药申请单消息  以及 卡片消息异步发送给患者
        ThreadPoolManager.execute(() -> {
            try {
                List<InquiryImMessageDto> cardMessageList = new ArrayList<>();
                // 组装用药申请单申请单消息
                cardMessageList.add(InquiryImConvert.INSTANCE.convertTOApplyMessage(inquiryRecordDO, doctorIm, patientIm, inquiryRecordDetailDO));
                // 获取医生卡片消息
                InquiryDoctorCardInfoDto cardInfoDto = inquiryDoctorApi.getInquiryDoctorCardInfoByDoctorPref(inquiryRecordDO.getDoctorPref());
                // 组装接诊医生卡片消息
                cardMessageList.add(InquiryImConvert.INSTANCE.convertTODoctorCardMessage(inquiryRecordDO, doctorIm, patientIm, cardInfoDto));
                // 组装接诊语信息
                cardMessageList.add(InquiryImConvert.INSTANCE.convertTODoctorReceptionMessage(inquiryRecordDO, doctorIm, patientIm));
                for (InquiryImMessageDto cardMessage : cardMessageList) {
                    inquiryImMessageApi.sendUserMessage(cardMessage);
                    TimeUnit.MILLISECONDS.sleep(500);
                }
            } catch (InterruptedException e) {
                log.error("问诊单号：{},医生接诊事件IM消息发送异常，异常原因：{}", inquiryRecordDO.getPref(), e);
            }
        });
    }

    /**
     * 三方预问诊单审核消息发送
     *
     * @param preInquiryDO    预问诊对象
     * @param auditStatusEnum 审核结果
     */
    @Override
    public void sendPreInquiryAuditMessage(ThirdPartyPreInquiryDO preInquiryDO, InquiryRespVO inquiryRespVO, AuditStatusEnum auditStatusEnum) {
        // 只有小程序问诊审核通过需要通知
        if(ObjectUtil.notEqual(preInquiryDO.getTransmissionOrganId(), BizChannelTypeEnum.MINI_PROGRAM.getCode())){
            return;
        }
        ThreadPoolManager.execute(() -> {
            try {
                log.info("预问诊单号：{},发送预问诊审核消息：{}",preInquiryDO.getPref(), JSON.toJSONString(preInquiryDO));
                // 获取小程序端客户IM账号
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(preInquiryDO.getCreator()), ClientChannelTypeEnum.MINI_PROGRAM.getCode());
                // 构建IM消息体
                InquiryImMessageDto messageDto = InquiryImConvert.INSTANCE.convert2PreInquiryAuditEvent(patientIm, ImEventPushEnum.PRE_INQUIRY_AUDIT,preInquiryDO,auditStatusEnum,inquiryRespVO);
                // 给小程序用户发送预问诊审核消息
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception exception) {
                log.error("预问诊单号：{},审核事件通知小程序失败，失败原因：{}", preInquiryDO.getPref(), exception);
            }
        });
    }
}
