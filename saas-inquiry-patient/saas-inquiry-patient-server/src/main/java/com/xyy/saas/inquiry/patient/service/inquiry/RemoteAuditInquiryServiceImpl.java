package com.xyy.saas.inquiry.patient.service.inquiry;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_ID_CARD_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_REMOTE_AUDIT_NO_QUALIFICATION;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.RemoteAuditPrescriptionApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditInquiryReqVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.convert.inquiry.RemoteAuditInquiryConvert;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackProducer;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.IdCardUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 11:39
 * @Description: 远程审方问诊服务
 **/
@Service
@Slf4j
public class RemoteAuditInquiryServiceImpl implements RemoteAuditInquiryService {

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;

    @Resource
    private InquiryReBackProducer inquiryReBackProducer;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @DubboReference
    private RemoteAuditPrescriptionApi remoteAuditPrescriptionApi;

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<InquiryRespVO> createInquiry(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO) {
        // 参数校验
        paramCheck(remoteAuditInquiryReqVO);
        // 校验问诊权限，成功返回租户信息
        TenantDto tenantDto = checkInquiryPermission(remoteAuditInquiryReqVO);
        // 生成患者记录
        InquiryPatientInfoDO patientInfoDO = inquiryPatientInfoService.saveOrUpdateRemoteAuditInquiryPatent(PatientInfoConvert.INSTANCE.convertRemoteAuditPatientVO2DO(remoteAuditInquiryReqVO.getRemoteAuditBaseReqVO().getPatient()));
        // 查远程审方视频配置
        InquiryOptionConfigRespDto optionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PROC_REMOTE_AUDIT_PRESCRIPTION_VIDEO);
        // 转化问诊单记录
        InquiryRecordDto inquiryDto = RemoteAuditInquiryConvert.INSTANCE.convertRemoteAuditInquiryReqVO2InquiryDTO(remoteAuditInquiryReqVO, patientInfoDO, tenantDto, optionConfig);
        // 扣问诊额度的乐观锁update
        TenantDeductCostDto tenantDeductCostDto = tenantPackageCostApi.deductTenantCost(InquiryWayTypeEnum.TEXT, InquiryBizTypeEnum.REMOTE_INQUIRY, inquiryDto.getPref(), null);

        try {
            // 持久化问诊单
            inquiryRecordMapper.insert(InquiryRecordConvert.INSTANCE.convertDTO2DO(inquiryDto));
            // 持久化问诊单详情
            inquiryRecordDetailMapper.insert(InquiryRecordDetailConvert.INSTANCE.convertDTO2DO(inquiryDto.getInquiryRecordDetailDto()));
            // 调医院API 生成处方单据
            remoteAuditPrescriptionApi.saveRemotePrescription(RemoteAuditInquiryConvert.INSTANCE.convert2PrescriptionDTO(remoteAuditInquiryReqVO, inquiryDto, tenantDto, tenantDeductCostDto));
        } catch (RuntimeException runtimeException) {
            // 发送mq 加回额度
            inquiryReBackProducer.sendMessage(
                InquiryReBackCostEvent.builder().msg(TenantChangeCostDto.builder().bizId(inquiryDto.getPref()).recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build()).build());
            throw runtimeException;
        }
        boolean auditVideo = inquiryDto.getInquiryRecordDetailDto().getExt().isPrescriptionAuditVideo();
        return CommonResult.success(InquiryRespVO.builder().inquiryPref(inquiryDto.getPref()).prescriptionAuditVideo(auditVideo).build());
    }

    // 校验身份找是否合法
    private static void paramCheck(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO) {
        String idCard = remoteAuditInquiryReqVO.getRemoteAuditBaseReqVO().getPatient().getPatientIdCard();
        if (StringUtils.isNotBlank(idCard) && !IdCardUtil.validateCard(idCard)) {
            throw exception(INQUIRY_RECORD_ID_CARD_FAIL);
        }
    }

    /**
     * 校验问诊权限
     *
     * @param remoteAuditInquiryReqVO
     * @return
     */
    private TenantDto checkInquiryPermission(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO) {
        // 效验问诊权限、额度
        tenantPackageCostApi.isValidTenantPackage(BizTypeEnum.HYWZ, InquiryWayTypeEnum.TEXT, InquiryBizTypeEnum.REMOTE_INQUIRY);
        TenantDto tenantDto = tenantApi.getTenant();
        if (tenantDto == null || ObjectUtil.notEqual(tenantDto.getWzTenantType(), TenantTypeEnum.CHAIN_STORE)) {
            throw exception(INQUIRY_REMOTE_AUDIT_NO_QUALIFICATION);
        }
        TenantDto headTenant = tenantApi.getTenant(tenantDto.getHeadTenantId());
        if (ObjectUtil.isEmpty(headTenant) || CommonStatusEnum.isDisable(headTenant.getStatus())) {
            throw exception(INQUIRY_REMOTE_AUDIT_NO_QUALIFICATION);
        }
        return tenantDto;
    }
}
