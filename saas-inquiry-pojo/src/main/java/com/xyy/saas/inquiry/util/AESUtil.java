package com.xyy.saas.inquiry.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

public class AESUtil {
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding"; // 加密模式与填充方式
    private static final String HASH_ALGORITHM = "SHA-256"; // 密钥哈希算法

    public static final  String AESUtil_KEY = "HYWZ0987654321231231";
    /**
     * 加密（返回Base64编码的字符串，格式：IV + 密文）
     * @param plainText 明文
     * @param secretKey 密钥（任意长度，自动规范为32字节）
     * @return Base64编码的加密结果
     */
    public static String encrypt(String plainText, String secretKey) throws Exception {
        // 1. 规范化密钥（SHA-256哈希后截取32字节）
        byte[] keyBytes = normalizeKey(secretKey, 32);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

        // 2. 生成随机初始向量（IV）
        byte[] ivBytes = new byte[16];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(ivBytes);
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        // 3. 初始化加密器并执行加密
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

        // 4. 组合IV和密文，返回Base64
        byte[] combined = new byte[ivBytes.length + encryptedBytes.length];
        System.arraycopy(ivBytes, 0, combined, 0, ivBytes.length);
        System.arraycopy(encryptedBytes, 0, combined, ivBytes.length, encryptedBytes.length);
        return Base64.getEncoder().encodeToString(combined);
    }

    /**
     * 解密
     * @param encryptedText Base64编码的加密字符串（含IV）
     * @param secretKey 密钥（需与加密时相同）
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText, String secretKey) throws Exception {
        // 1. 解码Base64并分离IV和密文
        byte[] combined = Base64.getDecoder().decode(encryptedText);
        byte[] ivBytes = new byte[16];
        byte[] encryptedBytes = new byte[combined.length - 16];
        System.arraycopy(combined, 0, ivBytes, 0, 16);
        System.arraycopy(combined, 16, encryptedBytes, 0, encryptedBytes.length);

        // 2. 规范化密钥
        byte[] keyBytes = normalizeKey(secretKey, 32);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        // 3. 初始化解密器并执行解密
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * 密钥规范化（哈希后截取指定长度）
     * @param secretKey 原始密钥
     * @param length 目标长度（16/24/32对应AES-128/192/256）
     */
    private static byte[] normalizeKey(String secretKey, int length) throws Exception {
        if (secretKey == null || secretKey.isEmpty())
            throw new IllegalArgumentException("密钥不能为空");

        MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
        byte[] hashedBytes = digest.digest(secretKey.getBytes(StandardCharsets.UTF_8));
        byte[] normalizedKey = new byte[length];
        System.arraycopy(hashedBytes, 0, normalizedKey, 0, Math.min(hashedBytes.length, length));
        return normalizedKey;
    }

    // 测试示例
    public static void main(String[] args) throws Exception {
        String plainText = "你好，这是一条测试数据！";
        String secretKey = "HYWZ0987654321231231"; // 任意长度密钥

        // 加密
        String encrypted = encrypt(plainText, secretKey);
        System.out.println("加密结果: " + encrypted);

        // 解密
        String decrypted = decrypt("fBvtS0Q4fSFnuPJ3AMOX58CfZJwepmhUTQaEx1MCXvClZ2OEB91l1Zqiq/EEQcWlwTnnd1h0k2uwwLZs1FHa/NAziw52dilmap+dIb4nz2H1kqeXEy1Pq7NaUqyZKT0t", secretKey);
        System.out.println("解密结果: " + decrypted);
    }
}
