package com.xyy.saas.inquiry.enums.transmitter;

import com.xyy.saas.inquiry.pojo.transmitter.TransmissionReqDataBaseDTO;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum NodeTypeEnum {
    /**
     * ------------------------------------------------------公共节点----------------------------------------------------------------------
     */
    //公共节点
    COMMON(9999901, null, "公共节点", null),

    //获取token节点
    GET_TOKEN(9999902, null, "获取token节点", null),

    //写上注释分隔，表名这块属于互联网监管节点
    /**
     * ------------------------------------------------------互联网监管----------------------------------------------------------------------
     */

    INTERNET_SUPERVISION_CONDITION(29999, PrescriptionSupervisionConditionTransmitterDTO.class, "互联网监管-监管条件", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-在线挂号
    INTERNET_SUPERVISION_REGISTRATION_FEEDBACK(30000, PrescriptionTransmitterDTO.class, "互联网监管-在线挂号", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传门诊病例
    INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE(30001, PrescriptionTransmitterDTO.class, "互联网监管-上传门诊病例", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-线上处方点评
    INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_COMMENT(30002, PrescriptionTransmitterDTO.class, "互联网监管-线上处方点评", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-诊疗结算
    INTERNET_SUPERVISION_TREATMENT_SETTLEMENT(30003, PrescriptionTransmitterDTO.class, "互联网监管-诊疗结算", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-派药
    INTERNET_SUPERVISION_SEND_DRUG(30004, PrescriptionTransmitterDTO.class, "互联网监管-派药", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-机构信息采集接口
    INTERNET_SUPERVISION_INSTITUTION_INFO_COLLECTION(30005, PrescriptionTransmitterDTO.class, "互联网监管-机构信息采集接口", OrganTypeEnum.INTERNET_SUPERVISION),


    // 互联网监管-上传派药反馈
    // INTERNET_SUPERVISION_SEND_DRUG_FEEDBACK(30007, "互联网监管-上传派药反馈", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传机构满意度
    // INTERNET_SUPERVISION_UPLOAD_ORGAN_SATISFACTION(30006, "互联网监管-上传机构满意度", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传整体服务满意度
    // INTERNET_SUPERVISION_UPLOAD_OVERALL_SERVICE_SATISFACTION(30007, "互联网监管-上传整体服务满意度", OrganTypeEnum.INTERNET_SUPERVISION),

    /**
     * ------------------------------------------------------HIS对接----------------------------------------------------------------------
     */
    // HIS对接 查询挂号信息
    HIS_REGISTRATION_QUERY(40001, RegistrationTransmitterDTO.class, "HIS对接-挂号信息查询", OrganTypeEnum.HIS),

    // HIS对接 处方流转
    // HIS_PRESCRIPTION_TRANSFER(40002, "HIS对接-处方流转", OrganTypeEnum.HIS),

    // HIS对接 处方上传
    // HIS_PRESCRIPTION_UPLOAD(40003, "HIS对接-处方上传", OrganTypeEnum.HIS),

    // HIS对接 查看处方
    // HIS_PRESCRIPTION_CHECK(40004, "HIS对接-查看处方", OrganTypeEnum.HIS),

    // HIS对接 更改预约订单状态
    HIS_PRESCRIPTION_ORDER_STATUS(40005, RegistrationTransmitterDTO.class, "HIS对接-更改预约订单状态", OrganTypeEnum.HIS);


    private final Integer code;
    private final Class<? extends TransmissionReqDataBaseDTO> reqClazz;
    private final String desc;
    private final OrganTypeEnum organType;

    public static NodeTypeEnum fromCode(Integer code) {
        for (NodeTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}