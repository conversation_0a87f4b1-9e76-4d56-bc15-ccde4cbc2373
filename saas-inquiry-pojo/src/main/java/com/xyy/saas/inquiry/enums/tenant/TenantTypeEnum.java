package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Desc 门店类型（1-单店 2连锁门店 3连锁总部）
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantTypeEnum implements IntArrayValuable {

    SINGLE_STORE(1, "单体门店"),
    CHAIN_STORE(2, "连锁门店"),
    CHAIN_HEADQUARTERS(3, "连锁总部"),
    CHAIN_STORE_DIRECTLY(4, "连锁直营"),
    CHAIN_STORE_JOIN(5, "连锁加盟"),

    ;

    private final int code;
    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getCode(), code))
            .findFirst()
            .orElse(null);
    }

    /**
     * 连锁门店必须关联总部
     *
     * @return
     */
    public boolean requiredHeadquarters() {
        return this == CHAIN_STORE;
    }
}