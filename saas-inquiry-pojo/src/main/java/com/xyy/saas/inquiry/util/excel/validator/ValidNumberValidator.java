package com.xyy.saas.inquiry.util.excel.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

public class ValidNumberValidator implements ConstraintValidator<ValidNumberFormat, String> {

    private int scale;

    private double min;

    private double max;

    @Override
    public void initialize(ValidNumberFormat constraintAnnotation) {
        this.scale = constraintAnnotation.scale();
        this.min = constraintAnnotation.min();
        this.max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true; // 空值由 @NotBlank 校验
        }
        try {

            BigDecimal decimal = new BigDecimal(value);

            if (decimal.doubleValue() < min || decimal.doubleValue() > max) {
                return false;
            }

            return decimal.scale() <= scale && decimal.scale() >= 0;

        } catch (NumberFormatException e) {
            return false;
        }
    }
}