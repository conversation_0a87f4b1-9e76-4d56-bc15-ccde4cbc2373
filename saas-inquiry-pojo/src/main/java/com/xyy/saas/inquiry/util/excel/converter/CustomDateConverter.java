package com.xyy.saas.inquiry.util.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class CustomDateConverter implements Converter<LocalDateTime> {

    private static final DateTimeFormatter DATE_FORMATTER1 =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DATE_FORMATTER2 =
        DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public Class<LocalDateTime> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDateTime convertToJavaData(
        ReadCellData<?> cellData,
        ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration
    ) {
        String dateStr = cellData.getStringValue();
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER2);
        } catch (Exception e) {
            // ignore
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER1);
        } catch (Exception e) {
            // ignore
        }
        return null;
    }
}