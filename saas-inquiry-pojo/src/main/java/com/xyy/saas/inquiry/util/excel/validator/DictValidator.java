package com.xyy.saas.inquiry.util.excel.validator;

import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class DictValidator implements ConstraintValidator<DictValid, String> {

    private String dictType;

    @Override
    public void initialize(DictValid constraintAnnotation) {
        this.dictType = constraintAnnotation.dictType();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 空值由@NotNull校验
        }
        try {
            return DictFrameworkUtils.getDictDataLabel(dictType, value) != null; // 存在返回true，否则false
        } catch (Exception e) {
            return false;
        }
    }
}
