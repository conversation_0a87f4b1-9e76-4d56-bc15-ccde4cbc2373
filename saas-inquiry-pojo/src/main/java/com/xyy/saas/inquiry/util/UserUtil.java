package com.xyy.saas.inquiry.util;

import com.aliyun.openservices.shade.com.google.common.base.Function;
import com.xyy.saas.inquiry.pojo.BaseDto;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/01/23 17:15
 */
public class UserUtil {

    /**
     * 填充用户信息
     *
     * @param list     需要填充的列表
     * @param supplier 执行查user的dubbo接口表达式
     * @param <T>      list数据类型
     */
    public static <T extends BaseDto> void fillUserInfo(List<T> list, Function<List<Long>, Map<Long, String>> supplier) {
        List<Long> userIdList = list.stream().flatMap(i -> Stream.of(i.getCreator(), i.getUpdater())).map(NumberUtils::toLong).filter(i -> i >= 0).toList();
        Map<Long, String> userMap = supplier.apply(userIdList);
        list.forEach(c -> {
            c.setCreator(userMap.getOrDefault(NumberUtils.toLong(c.getCreator()), StringUtils.equals(c.getCreator(), "0") ? "系统" : c.getCreator()));
            c.setUpdater(userMap.getOrDefault(NumberUtils.toLong(c.getUpdater()), StringUtils.equals(c.getUpdater(), "0") ? "系统" : c.getUpdater()));
        });
    }


}
