package com.xyy.saas.inquiry.util;

import cn.iocoder.yudao.framework.common.util.monitor.TraceIdUtil;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskDecorator;

/**
 * MDC线程池 包装了执行方法
 *
 * @Author:chenxiaoyi
 */
public class ThreadPoolExecutorMdcWrapper extends ThreadPoolExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolExecutorMdcWrapper.class);

    public ThreadPoolExecutorMdcWrapper(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public ThreadPoolExecutorMdcWrapper(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public ThreadPoolExecutorMdcWrapper(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public ThreadPoolExecutorMdcWrapper(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    public static ThreadPoolExecutorMdcWrapper newFixedThreadPool(int nThreads) {
        return new ThreadPoolExecutorMdcWrapper(nThreads, nThreads,
                                                0L, TimeUnit.MILLISECONDS,
                                                new LinkedBlockingQueue<>());
    }

    @Override
    public void execute(Runnable command) {
        super.execute(wrap(command));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(wrap(task));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return super.submit(wrap(task), result);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(wrap(task));
    }

    /**
     * 包装执行方法，设置进去父线程上下文
     *
     * @param runnable 执行线程
     * @return Runnable
     */
    public static Runnable wrap(final Runnable runnable) {
        Map<String, String> contextMap = TraceIdUtil.getCopyOfContextMap();
        return () -> {
            TraceIdUtil.setContextMap(contextMap);
            TraceIdUtil.putIfAbsent();
            try {
                runnable.run();
            } finally {
//                TraceIdUtil.clear();
//                logger.info("TraceIdUtil.clear()之后输出");
            }
        };
    }

    /**
     * 包装执行方法，设置进去父线程上下文
     *
     * @param callable 执行线程
     * @return Runnable
     */
    public static <T> Callable<T> wrap(final Callable<T> callable) {
        Map<String, String> contextMap = TraceIdUtil.getCopyOfContextMap();
        return () -> {
            TraceIdUtil.setContextMap(contextMap);
            TraceIdUtil.putIfAbsent();
            try {
                return callable.call();
            } finally {
                TraceIdUtil.clear();
            }
        };
    }

    /**
     * 包装MDC TaskDecorator 用于异步多线程池 (@Async)
     */
    public static class MdcTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(final Runnable runnable) {
            Map<String, String> contextMap = TraceIdUtil.getCopyOfContextMap();
            return () -> {
                TraceIdUtil.setContextMap(contextMap);
                TraceIdUtil.putIfAbsent();
                try {
                    runnable.run();
                } finally {
                    TraceIdUtil.clear();
                }
            };
        }
    }
}
