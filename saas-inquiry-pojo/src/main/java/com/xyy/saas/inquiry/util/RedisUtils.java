package com.xyy.saas.inquiry.util;

import cn.hutool.core.thread.ThreadUtil;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

/**
 * @Author: xucao
 * @Date: 2024/12/17 10:04
 * @Description: redis 工具类
 */
@Slf4j
public class RedisUtils {

    private RedisUtils() {
    }

    @SuppressWarnings("unchecked")
    private static final RedisTemplate<String, Object> redisTemplate = SpringUtils
        .getBean("redisTemplate", RedisTemplate.class);

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public static boolean expire(final String key, final long timeout) {

        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public static boolean expire(final String key, final long timeout, final TimeUnit unit) {

        Boolean ret = redisTemplate.expire(key, timeout, unit);
        return ret != null && ret;
    }


    /**
     * 获取指定key 的剩余时间
     *
     * @param key
     * @return
     */
    public static long getExpire(final String key) {
        Long ret = redisTemplate.getExpire(key);
        return ret == null ? 0 : ret;
    }


    /**
     * 删除单个key
     *
     * @param key 键
     * @return true=删除成功；false=删除失败
     */
    public static boolean del(final String key) {

        Boolean ret = redisTemplate.delete(key);
        return ret != null && ret;
    }

    /**
     * 删除多个key
     *
     * @param keys 键集合
     * @return 成功删除的个数
     */
    public static long del(final Collection<String> keys) {

        Long ret = redisTemplate.delete(keys);
        return ret == null ? 0 : ret;
    }

    /**
     * 存入普通对象
     *
     * @param key   Redis键
     * @param value 值
     */
    public static void set(final String key, final Object value) {

        redisTemplate.opsForValue().set(key, value, 1, TimeUnit.MINUTES);
    }

    // 存储普通对象操作

    /**
     * 存入普通对象
     *
     * @param key     键
     * @param value   值
     * @param timeout 有效期，单位秒
     */
    public static void set(final String key, final Object value, final long timeout) {

        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 获取普通对象
     *
     * @param key 键
     * @return 对象
     */
    public static Object get(final String key) {

        return redisTemplate.opsForValue().get(key);
    }

    // 存储Hash操作

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public static void hPut(final String key, final String hKey, final Object value) {

        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 往Hash中存入多个数据
     *
     * @param key    Redis键
     * @param values Hash键值对
     */
    public static void hPutAll(final String key, final Map<String, Object> values) {

        redisTemplate.opsForHash().putAll(key, values);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public static Object hGet(final String key, final String hKey) {

        return redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public static List<Object> hMultiGet(final String key, final Collection<Object> hKeys) {

        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    // 存储Set相关操作

    /**
     * 往Set中存入数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 存入的个数
     */
    public static long sSet(final String key, final Object... values) {
        Long count = redisTemplate.opsForSet().add(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 删除Set中的数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除的个数
     */
    public static long sDel(final String key, final Object... values) {
        Long count = redisTemplate.opsForSet().remove(key, values);
        return count == null ? 0 : count;
    }

    // 存储List相关操作

    /**
     * 往List中存入数据
     *
     * @param key   Redis键
     * @param value 数据
     * @return 存入的个数
     */
    public static long lPush(final String key, final Object value) {
        Long count = redisTemplate.opsForList().leftPush(key, value);
        return count == null ? 0 : count;
    }

    /**
     * 从List中移除某个元素
     *
     * @param key
     * @param value
     * @return
     */
    public static long lRem(final String key, final Object value) {
        Long count = redisTemplate.opsForList().remove(key, 0, value);
        return count == null ? 0 : count;
    }

    /**
     * 从List中（队列头）取出数据
     *
     * @param key Redis键
     * @return 取出的数据
     */
    public static Object rPop(final String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    public static long lPushAll(final String key, final Collection<Object> values) {
        Long count = redisTemplate.opsForList().leftPushAll(key, values);
        return count == null ? 0 : count;
    }


    /**
     * 从List中（队列尾）取出数据
     *
     * @param key   key
     * @param count 获取的个数
     * @return 获取的对象集合
     */
    public static List<Object> rPop(final String key, final Integer count) {
        return redisTemplate.opsForList().rightPop(key, count);
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    public static long lPushAll(final String key, final Object... values) {
        Long count = redisTemplate.opsForList().leftPushAll(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 从List中获取begin到end之间的元素
     *
     * @param key   Redis键
     * @param start 开始位置
     * @param end   结束位置（start=0，end=-1表示获取全部元素）
     * @return List对象
     */
    public static List<Object> lGet(final String key, final int start, final int end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 从List中获取所有元素，按列表顺序（leftPush时,先进的列表尾部）
     *
     * @param key Redis键
     * @return List对象
     */
    public static List<Object> lGetAll(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 获取List长度
     */
    public static long lSize(final String key) {
        return Optional.ofNullable(redisTemplate.opsForList().size(key)).orElse(0L);
    }

    /**
     * 批量获取一批key
     *
     * @param keys key集合
     * @return value 列表
     */
    public static List<Object> mGet(final Collection<String> keys) {
        return redisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * 获取当前key的递增值
     *
     * @param key key
     * @return
     */
    public static Long incrementKey(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    /**
     * 添加到zset中
     *
     * @param key
     * @param value
     * @param score 打分,一般是时间戳
     */
    public static void zSetAdd(String key, String value, double score) {
        redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 从zset中移除
     *
     * @param key
     * @param value
     */
    public static void zSetRemove(String key, String value) {
        redisTemplate.opsForZSet().remove(key, value);
    }


    /**
     * 分布式锁-加锁 SET key value NX PX milliseconds
     *
     * @param lockKey
     * @param requestId
     * @param expireTime
     * @return
     */
    public static boolean tryLock(String lockKey, String requestId, long expireTime) {
        AssertUtils.notEmpty(lockKey, "lockKey 不能为空");
        AssertUtils.notEmpty(requestId, "requestId 不能为空");
        AssertUtils.isTrue(expireTime > 1000L, "expireTime 必须大于 1000");
        boolean flag = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, requestId, expireTime, TimeUnit.MILLISECONDS));
        log.info("tryLock, lockKey:{}, requestId:{}, expireTime:{}ms, flag:{}", lockKey, requestId, expireTime, flag);
        return flag;
    }

    /**
     * 分布式锁-加锁（自动续期）
     *
     * @param lockKey
     * @param requestId
     * @param expireTime
     * @return
     */
    public static boolean tryLockWithRenewal(String lockKey, String requestId, long expireTime) {
        boolean result = tryLock(lockKey, requestId, expireTime);
        if (result) {
            // 创建并启动一个线程，定时续期
            RedisLockRenewalTask task = new RedisLockRenewalTask(lockKey, requestId, expireTime);
            RENEWAL_TASKS.put(requestId, task);
            ThreadPoolManager.execute(task);
        }
        return result;
    }

    /**
     * 释放Redis锁并停止续期任务
     * @param lockKey 锁键
     * @param requestId 请求ID
     */
    public static void releaseLockWithRenewal(String lockKey, String requestId) {
        // 停止续期任务
        RedisLockRenewalTask task = RENEWAL_TASKS.remove(requestId);
        if (task != null) {
            task.stop();
        }
        // 释放锁
        releaseLock(lockKey, requestId);
    }

    /**
     * Redis锁续期任务类
     */
    private static class RedisLockRenewalTask implements Runnable {
        private final String lockKey;
        private final String requestId;
        private final long expireTime;
        private volatile boolean shouldRun = true;

        public RedisLockRenewalTask(String lockKey, String requestId, long expireTime) {
            this.lockKey = lockKey;
            this.requestId = requestId;
            this.expireTime = expireTime;
        }

        public void stop() {
            this.shouldRun = false;
        }

        @Override
        public void run() {
            try {
                while (shouldRun && !Thread.currentThread().isInterrupted()) {
                    ThreadUtil.sleep(expireTime / 3);
                    if (!renewLock(lockKey, requestId, expireTime)) {
                        break;
                    }
                }
            } catch (Exception e) {
                log.warn("Redis lock renewal task interrupted or failed", e);
            }
        }
    }

    // 存储锁续期任务的引用，以便可以停止续期
    private static final Map<String, RedisLockRenewalTask> RENEWAL_TASKS = new ConcurrentHashMap<>();

    // 分布式锁释放 lua脚本
    private static final RedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end", Long.class);
    // 分布式锁续期 lua脚本
    private static final RedisScript<Long> RENEW_LOCK_LUA_SCRIPT = new DefaultRedisScript<>("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('pexpire', KEYS[1], ARGV[2]) else return 0 end", Long.class);

    /**
     * 分布式锁-解锁 lua脚本先get key的值，如果是传入的value，则删除key并返回1
     *
     * @param lockKey
     * @param requestId
     * @return
     */
    public static boolean releaseLock(String lockKey, String requestId) {
        AssertUtils.notEmpty(lockKey, "lockKey 不能为空");
        AssertUtils.notEmpty(requestId, "requestId 不能为空");
        boolean flag = redisTemplate.execute(UNLOCK_LUA_SCRIPT, List.of(lockKey), requestId) == 1L;
        log.info("releaseLock, lockKey:{}, requestId:{}, flag:{}", lockKey, requestId, flag);
        return flag;
    }

    /**
     * 分布式锁-续期 lua脚本先get key的值，如果是传入的value，则延长过期时间并返回1
     *
     * @param lockKey
     * @param requestId
     * @return
     */
    public static boolean renewLock(String lockKey, String requestId, long expireTime) {
        AssertUtils.notEmpty(lockKey, "lockKey 不能为空");
        AssertUtils.notEmpty(requestId, "requestId 不能为空");
        AssertUtils.isTrue(expireTime > 100L, "expireTime 必须大于 100");
        boolean flag = redisTemplate.execute(RENEW_LOCK_LUA_SCRIPT, List.of(lockKey), requestId, expireTime) == 1L;
        log.info("renewLock, lockKey:{}, requestId:{}, expireTime:{}ms, flag:{}", lockKey, requestId, expireTime, flag);
        return flag;
    }


    // 自旋等待时间(毫秒)
    private static final long SPIN_WAIT_TIME = 200;

    // 默认锁超时时间(毫秒) 30*60*1000 30分钟
    private static final long DEFAULT_TIMEOUT = 1800000;

    /**
     * 简化版获取锁（使用默认参数）
     */
    public static boolean tryLockWithSpin(String lockKey, String requestId) {
        return tryLock(lockKey, requestId, DEFAULT_TIMEOUT, DEFAULT_TIMEOUT);
    }

    /**
     * 获取分布式锁（带自旋）
     *
     * @param lockKey     锁key
     * @param requestId   请求标识（可用UUID）
     * @param timeout     锁超时时间(毫秒)
     * @param maxWaitTime 最大等待时间(毫秒)
     * @return 是否获取成功
     */
    public static boolean tryLock(String lockKey, String requestId, long timeout, long maxWaitTime) {
        long startTime = System.currentTimeMillis();

        try {
            // 自旋获取锁
            while (System.currentTimeMillis() - startTime < maxWaitTime) {
                // 尝试获取锁
                Boolean success = redisTemplate.opsForValue().setIfAbsent(
                    lockKey,
                    requestId,
                    timeout,
                    TimeUnit.MILLISECONDS
                );

                if (Boolean.TRUE.equals(success)) {
                    log.info("tryLock, lockKey:{}, requestId:{}", lockKey, requestId);
                    return true;
                }

                // 等待一段时间再重试
                Thread.sleep(SPIN_WAIT_TIME);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return false;
    }

}
