package com.xyy.saas.inquiry.pojo.transmitter.internet;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import com.xyy.saas.inquiry.pojo.transmitter.TransmissionReqDataBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 互联网监管-处方DTO
 *
 * @Author:chenxiaoyi
 * @Date:2025/02/18 16:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrescriptionTransmitterDTO extends TransmissionReqDataBaseDTO {

    @Schema(description = "处方编码", example = "1517")
    private String pref;

    @Schema(description = "问诊编码", example = "32287")
    private String inquiryPref;

    @Schema(description = "患者编码", example = "30337")
    private String patientPref;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer patientSex;

    @Schema(description = "租户ID", example = "1517")
    private Long tenantId;

    @Schema(description = "用药类型：0西药，1中药", example = "2")
    private Integer medicineType;

    @Schema(description = "互联网医院编码", example = "21843")
    private String hospitalPref;

    @Schema(description = "是否自动开方：0 否  、 1是")
    private Integer autoInquiry;

    @Schema(description = "科室编码")
    private String deptPref;

    @Schema(description = "科室名称", example = "芋艿")
    private String deptName;

    @Schema(description = "医生编码", example = "20472")
    private String doctorPref;

    @Schema(description = "医师姓名", example = "王五")
    private String doctorName;

    @Schema(description = "药师编码", example = "20472")
    private String pharmacistPref;

    @Schema(description = "药师姓名", example = "20472")
    private String pharmacistName;

    @Schema(description = "问诊开始时间")
    private LocalDateTime inquiryStartTime;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "药师审方时间")
    private LocalDateTime auditPrescriptionTime;

    @Schema(description = "诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明")
    private List<String> diagnosisName;


    @Schema(description = "电子处方外配流水号")
    private String electronicRxSn;

    @Schema(description = "外配处方单号")
    private String extPref;

    @Schema(description = "处方拓展字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private PrescriptionExtDto ext;

    @Schema(description = "门店信息")
    private TenantDto tenantInfo;
}
