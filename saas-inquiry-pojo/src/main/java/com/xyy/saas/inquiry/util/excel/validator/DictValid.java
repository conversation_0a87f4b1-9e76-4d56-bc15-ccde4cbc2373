package com.xyy.saas.inquiry.util.excel.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DictValidator.class)
public @interface DictValid {

    String message() default "字典值不存在";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String dictType(); // 关联的字典类型
}