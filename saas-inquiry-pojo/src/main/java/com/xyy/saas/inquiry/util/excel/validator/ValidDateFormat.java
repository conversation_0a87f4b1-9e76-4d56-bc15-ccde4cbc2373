package com.xyy.saas.inquiry.util.excel.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidDateFormatValidator.class)
public @interface ValidDateFormat {

    String message() default "日期格式错误，应为 yyyy-MM-dd";

    String pattern() default "yyyy-MM-dd"; // 默认格式

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}