package com.xyy.saas.inquiry.pojo.prescription;

import com.xyy.saas.inquiry.enums.prescription.PrescriptionPricingTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/11 18:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "处方扩展信息ext")
@Accessors(chain = true)
public class PrescriptionExtDto implements Serializable {

    /**
     * {@link PrescriptionPricingTypeEnum}
     */
    @Schema(description = "处方划价状态")
    private Integer pricingStatus;

    @Schema(description = "处方划价总价")
    private BigDecimal pricingPrice;

    @Schema(description = "关联的结算发票号")
    private String setlInvoiceNumber;

    @Schema(description = "总部id-仅远程审方处方调用")
    private Long headTenantId;

    @Schema(description = "远程审方上传的处方图片")
    private String remotePrescriptionImg;

    /**
     * 远程处方关联单号,当处方本身是远程处方时,此单号是门店处方原始单号。当处方本身是正常处方时,此单号是远程处方单号
     */
    private String remotePrescriptionPref;

    @Schema(description = "处方审核是否视频")
    private boolean prescriptionAuditVideo;


    @Schema(description = "处方日期格式 DO中处方笺日期格式")
    private Integer inquiryPresDateType;

    @Schema(description = "签章值")
    private Integer sealValueType;

    @Schema(description = "处方废弃操作人")
    private String abandonUser;

    @Schema(description = "处方废弃时间")
    private String abandonTime;

    @Schema(description = "处方废弃原因")
    private String abandonReason;

    // 中药 *******************
    /**
     * 总剂量  eg:10 副
     */
    @Schema(description = "中药总剂量  eg:10 副")
    private String tcmTotalDosage;

    /**
     * 每x日 eg: 每tcmDaily日tcmDailyDosage剂
     */
    @Schema(description = "每x日  eg:2")
    @Length(max = 1000, message = "总剂量最大长度1000")
    private String tcmDaily;

    /**
     * 每日剂量 eg:每日3 剂
     */
    @Schema(description = "中药每x日剂量 eg:每日3 剂")
    private String tcmDailyDosage;

    /**
     * 每剂几次用药 eg:每剂2 次用药
     */
    @Schema(description = "中药每剂几次用药 eg:每剂2 次用药")
    private String tcmUsage;

    /**
     * 加工方法 eg: 打粉冲服
     */
    @Schema(description = "中药加工方法 eg: 打粉冲服")
    private String tcmProcessingMethod;

    /**
     * 用法 eg: 温服
     */
    @Schema(description = "中药用法 eg: 温服")
    private String tcmDirections;

}
