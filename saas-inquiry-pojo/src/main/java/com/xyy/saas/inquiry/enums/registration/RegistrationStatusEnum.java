package com.xyy.saas.inquiry.enums.registration;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;

/**
 * 就诊登记单据状态
 */
@Getter
@RequiredArgsConstructor
public enum RegistrationStatusEnum implements IntArrayValuable {
    INIT(0, "初始"),

    REG_SUCCESS(1, "登记成功"),

    PROGRESS(2, "执行中"),

    COMPLETED(3, "已完成"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(RegistrationStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static RegistrationStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(INIT);
    }
}
