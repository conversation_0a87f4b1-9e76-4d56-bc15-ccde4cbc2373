package com.xyy.saas.inquiry.drugstore.server;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 单元测试扫描当前包路径,相关api测试写到这个层级下或者建立相关包路径 com.xyy.saas.inquiry.drugstore.server
 */
@SpringBootTest(classes = SaasInquiryDrugstoreServerApplication.class)
@ActiveProfiles("test")
class TenantPackageCostApiImplTests {

    @Resource
    private TenantPackageCostApi tenantPackageCostApi;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(1831623538495963137L);
    }

    /**
     * 初始化门店套餐
     */
    @Test
    public void initTenantPackageCost_success() {
        TenantPackageCostDto tenantPackageCostDto = JSON.parseObject(
            "{\"endTime\":\"2024-09-21T20:32:53\",\"inquiryPackageItems\":[{\"count\":-1,\"inquiryWayType\":1,\"unlimited\":true},{\"count\":100,\"inquiryWayType\":2,\"unlimited\":false}],\"packageRelationId\":1833846174957412354,\"startTime\":\"2024-09-26T20:32:53\",\"status\":1,\"tenantId\":1831623538495963137}",
            TenantPackageCostDto.class);
        tenantPackageCostDto.setCreator("1");
        tenantPackageCostApi.initTenantPackageCost(tenantPackageCostDto);
    }

    /**
     * 验证扣减门店套餐
     */
    @Test
    public void deductTenantCost_success() {
        tenantPackageCostApi.deductTenantCost(InquiryWayTypeEnum.VIDEO, "1", null);
    }

}
