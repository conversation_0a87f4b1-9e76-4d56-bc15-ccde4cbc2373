package com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageCostExtDto;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店问诊套餐额度 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_tenant_package_cost", autoResultMap = true)
@KeySequence("saas_tenant_package_cost_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantPackageCostDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;
    /**
     * 当前在用的套餐订单表id
     */
    private Long tenantPackageId;

    /**
     * 问诊医院id
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> hospitalPrefs;


    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    /**
     * 问诊审核类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum}
     */
    private Integer inquiryAuditType;

    /**
     * 问诊额度 -1不限
     */
    private Long cost;
    /**
     * 问诊剩余额度 -1不限
     */
    private Long surplusCost;
    /**
     * 套餐开始时间
     */
    private LocalDateTime startTime;
    /**
     * 套餐结束时间
     */
    private LocalDateTime endTime;
    /**
     * 问诊形式 1图文 2视频
     */
    private Integer inquiryWayType;
    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    private Integer status;

    /**
     * 扩展信息，存储 value 信息 格式：{"prescriptionValue":[0,1]}
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private TenantPackageCostExtDto ext;

    /**
     * 获取有效的扩展信息，为空时返回默认值 用于兼容旧数据
     *
     * @return 扩展信息
     */
    public TenantPackageCostExtDto getExtOrDefault() {
        return ext != null ? ext : TenantPackageCostExtDto.getDefault();
    }

}