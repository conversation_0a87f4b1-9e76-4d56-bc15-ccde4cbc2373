package com.xyy.saas.inquiry.drugstore.server.api.tenant;

import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugStoreBaseInfoService;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostService;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 门店套餐包额度apiImpl
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:54
 */
@DubboService
public class TenantPackageCostApiImpl implements TenantPackageCostApi {

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    @Resource
    private DrugStoreBaseInfoService drugStoreBaseInfoService;

    @Override
    public void initTenantPackageCost(TenantPackageCostDto tenantPackageCostDto) {
        tenantPackageCostService.saveTenantPackageCost(tenantPackageCostDto);
    }

    @Override
    public void isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, Integer prescriptionType) {
        tenantPackageCostService.isValidTenantPackage(bizTypeEnum, inquiryWayTypeEnum, inquiryBizTypeEnum, prescriptionType);
    }

    @Override
    public TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum wayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, String bizId, Integer prescriptionType) {
        return tenantPackageCostService.deductTenantCost(wayTypeEnum, inquiryBizTypeEnum, bizId, prescriptionType);
    }

    @Override
    public void reBackTenantCost(TenantChangeCostDto changeCostDto) {
        tenantPackageCostService.reBackTenantCost(changeCostDto);
    }

    @Override
    public boolean validPlatformReviewCost() {
        return tenantPackageCostService.validPlatformReviewCost();
    }

    @Override
    public TenantPackageCostDto getTenantPackageCostById(Long costId) {
        return tenantPackageCostService.getTenantPackageCostById(costId);
    }

    @Override
    public TenantPackageCostDto getTenantPackageCostByLogBizId(String bizId, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostService.getTenantPackageCostByLogBizId(bizId, costRecordTypeEnum);
    }

    @Override
    public Map<String, TenantPackageCostDto> getTenantPackageCostByLogBizIds(List<String> bizIds, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostService.getTenantPackageCostByLogBizIds(bizIds, costRecordTypeEnum);
    }


    @Override
    public List<TenantPackageCostDto> selectUserTenantPackages(Long userId, List<TenantPackageEffectiveStatusEnum> effectiveStatusEnums) {
        return drugStoreBaseInfoService.selectUserTenantPackages(userId, effectiveStatusEnums);
    }
}
