package com.xyy.saas.inquiry.drugstore.server.convert.tennat;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostMessageDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItems;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageCostExtDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 14:36
 */
@Mapper
public interface TenantPackageCostConvert {

    TenantPackageCostConvert INSTANCE = Mappers.getMapper(TenantPackageCostConvert.class);


    TenantPackageCostDO tenantPackageCostDto2CostRelationDO(TenantPackageCostDto tenantPackageCostDto);


    default List<TenantPackageCostDO> tenantPackageCostDto2CostRelationDOLists(TenantPackageCostDto tenantPackageCostDto) {
        if (CollUtil.isEmpty(tenantPackageCostDto.getInquiryPackageItems())) {
            return null;
        }

        List<TenantPackageCostDO> result = new ArrayList<>();

        for (InquiryPackageItem packageItem : tenantPackageCostDto.getInquiryPackageItems()) {
            // 获取有效的问诊项目列表（支持新旧格式）
            List<InquiryPackageItem> effectiveItems = packageItem.effectiveItems();

            for (InquiryPackageItem item : effectiveItems) {
                TenantPackageCostDO packageCostDO = tenantPackageCostDto2CostRelationDO(tenantPackageCostDto);
                packageCostDO.setInquiryWayType(item.getInquiryWayType());
                packageCostDO.setCost(item.isUnlimited() ? TenantPackageConstant.UN_LIMITED_COST : item.getCount());
                packageCostDO.setSurplusCost(item.isUnlimited() ? TenantPackageConstant.UN_LIMITED_COST : item.getCount());

                // 设置扩展信息
                if (packageItem.hasItems()) {
                    packageCostDO.setHospitalPrefs(packageItem.getHospitalPref());
                    // 新格式：使用 packageItem 的 type 和 value
                    TenantPackageCostExtDto ext = TenantPackageCostExtDto.builder()
                        .prescriptionValue(packageItem.getPrescriptionValue())
                        .build();
                    packageCostDO.setExt(ext);
                }

                result.add(packageCostDO);
            }
        }

        return result;
    }

    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "status", ignore = true)
    TenantPackageCostReqVO tenantPackageCostUpdateStatusDto2Vo(TenantPackageCostDto packageCostDto);

    /**
     * 转换剩余额度套餐Item（兼容格式） 优先使用新格式（V2），如果没有 ext 信息则使用旧格式但包含 hospitalPref
     *
     * @param packageCostDOS
     * @return
     */
    default List<InquiryPackageItem> convertSurplusInquiryPackageItems(List<TenantPackageCostDO> packageCostDOS) {
        if (packageCostDOS == null || packageCostDOS.isEmpty()) {
            return new ArrayList<>();
        }

        // 检查是否有 ext 信息，如果有则使用新格式
        boolean convertV2 = packageCostDOS.stream().anyMatch(cost -> Objects.equals(cost.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode()));

        // 使用新格式转换
        if (convertV2) {
            return convertSurplusInquiryPackageItemsV2(packageCostDOS);
        }
        // 使用旧格式但包含 hospitalPref 兼容性处理
        return packageCostDOS.stream().map(c -> InquiryPackageItem.builder()
                .inquiryWayType(c.getInquiryWayType())
                .count(c.getSurplusCost())
                .unlimited(TenantPackageConstant.isUnlimitedCost(c.getSurplusCost()))
                .hospitalPref(c.getHospitalPrefs()) // 添加 hospitalPref 兼容性
                .build())
            .collect(Collectors.toList());

    }

    /**
     * 转换剩余额度套餐Item（新格式，支持分组） 根据 ext 信息进行分组聚合
     *
     * @param packageCostDOS 套餐额度列表
     * @return 新格式的问诊套餐项目列表
     */
    default List<InquiryPackageItem> convertSurplusInquiryPackageItemsV2(List<TenantPackageCostDO> packageCostDOS) {
        if (CollUtil.isEmpty(packageCostDOS)) {
            return new ArrayList<>();
        }

        // 按 ext 信息分组 医院+统筹 下 图文 + 视频
        Map<String, List<TenantPackageCostDO>> groupedCosts = packageCostDOS.stream()
            .collect(Collectors.groupingBy(cost -> {
                TenantPackageCostExtDto ext = cost.getExtOrDefault();
                return Optional.ofNullable(cost.getHospitalPrefs()).orElse(List.of()) + "_" + Optional.ofNullable(ext.getPrescriptionValue()).orElse(List.of());
            }));

        List<InquiryPackageItem> result = new ArrayList<>();

        groupedCosts.forEach((key, costs) -> {
            TenantPackageCostExtDto ext = costs.getFirst().getExtOrDefault();

            // 构建 items - 使用新的 InquiryPackageItems 类型
            List<InquiryPackageItems> items = costs.stream()
                .map(c -> InquiryPackageItems.builder()
                    .inquiryWayType(c.getInquiryWayType())
                    .count(c.getSurplusCost())
                    // .checked(true)
                    .unlimited(TenantPackageConstant.isUnlimitedCost(c.getSurplusCost()))
                    .build())
                .collect(Collectors.toList());

            // 构建新格式的 InquiryPackageItem
            InquiryPackageItem newItem = InquiryPackageItem.builder()
                .prescriptionValue(ext.getPrescriptionValue())
                .hospitalPref(costs.getFirst().getHospitalPrefs())
                .items(items)
                .build();

            result.add(newItem);
        });

        return result;
    }

    /**
     * 转换初始额度套餐Item
     *
     * @param packageCostDOS
     * @return
     */
    default List<InquiryPackageItem> convertCostInquiryPackageItems(List<TenantPackageCostDO> packageCostDOS) {
        if (packageCostDOS == null || packageCostDOS.isEmpty()) {
            return new ArrayList<>();
        }
        return packageCostDOS.stream().map(c -> InquiryPackageItem.builder()
                .inquiryWayType(c.getInquiryWayType())
                .count(c.getCost())
                .unlimited(TenantPackageConstant.isUnlimitedCost(c.getCost())).build())
            .collect(Collectors.toList());
    }

    TenantPackageCostDto convertCostDto(TenantPackageCostMessageDto msg);

    TenantPackageCostDto convertDto(TenantPackageCostDO packageCostDO);

    List<TenantPackageCostDto> convertDtos(List<TenantPackageCostDO> packageCostDOS);
}
