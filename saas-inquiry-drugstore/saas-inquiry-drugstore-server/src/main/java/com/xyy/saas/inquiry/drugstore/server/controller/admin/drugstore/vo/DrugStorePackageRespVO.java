package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 门店套餐信息 Response VO")
@Data
public class DrugStorePackageRespVO {

    /**
     * 开通订单id
     */
    @Schema(description = "套餐订单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long id;

    @Schema(description = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private String packageName;

    @Schema(description = "开通时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private LocalDateTime createTime;

    @Schema(description = "套餐类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer packageNature;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal price;

    @Schema(description = "服务开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private LocalDateTime startTime;

    @Schema(description = "服务结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private LocalDateTime endTime;

    @Schema(description = "套餐状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "剩余天数到期,如果有值,前端展示:剩余endDay天到期", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long endDay;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否生效", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer effective;

    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    @Schema(description = "套餐额度")
    private List<InquiryPackageItem> packageCosts;

    private String packageCostStr;

    @Schema(description = "套餐额度文案")
    private String inquiryPackageItemStr;

    @Schema(description = "剩余额度")
    private List<InquiryPackageItem> surplusCosts;

    @Schema(description = "剩余额度文案")
    private String surplusCostStr;

    public String getPackageCostStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getPackageCosts());
    }
    //
    // public String getSurplusCostStr() {
    //     return InquiryPackageItem.getInquiryPackageItemStr(getSurplusCosts());
    // }


}