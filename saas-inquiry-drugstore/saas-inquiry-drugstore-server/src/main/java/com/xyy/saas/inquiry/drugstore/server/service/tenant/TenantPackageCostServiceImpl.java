package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_COST_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_COST_DEDUCT_ERROR;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_NO_ERROR;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_RECORD_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantPackageShareApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationDto;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostConvert;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostLogConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostLogMapper;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostMapper;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 门店问诊套餐额度 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantPackageCostServiceImpl implements TenantPackageCostService {

    @Resource
    private TenantPackageCostMapper tenantPackageCostMapper;

    @Resource
    private TenantPackageCostLogMapper tenantPackageCostLogMapper;

    @Autowired
    private TenantApi tenantApi;

    @Autowired
    private TenantPackageShareApi tenantPackageShareApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @TenantIgnore // 忽略多门店,此接口为后台调用无门店id
    public void saveTenantPackageCost(TenantPackageCostDto tenantPackageCostDto) {
        List<TenantPackageCostDO> tenantPackageCostDOList = TenantPackageCostConvert.INSTANCE.tenantPackageCostDto2CostRelationDOLists(tenantPackageCostDto);
        if (CollUtil.isEmpty(tenantPackageCostDOList)) {
            return;
        }
        // 查询问诊套餐关系id 额度是否存在
        TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder()
            .tenantId(tenantPackageCostDto.getTenantId())
            .tenantPackageId(tenantPackageCostDto.getTenantPackageId())
            .inquiryWayTypes(convertList(tenantPackageCostDOList, TenantPackageCostDO::getInquiryWayType)).build();
        List<TenantPackageCostDO> existsPackageCostRelationDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
        if (CollUtil.isNotEmpty(existsPackageCostRelationDOS)) {
            log.info("saveTenantPackageCost,业务上仅修改时间:{}", existsPackageCostRelationDOS);
            tenantPackageCostMapper.updateServerTimeByIds(tenantPackageCostDto, convertList(existsPackageCostRelationDOS, TenantPackageCostDO::getId));
            return;
        }
        // 批量写入
        tenantPackageCostMapper.insertBatch(tenantPackageCostDOList);
        // 记录套餐额度记录表
        List<TenantPackageCostLogDO> tenantPackageCostLogDOS = TenantPackageCostLogConvert.INSTANCE.tenantPackageCostRelationDO2RecordDos(tenantPackageCostDOList, CostRecordTypeEnum.NORMAL);
        tenantPackageCostLogMapper.insertBatch(tenantPackageCostLogDOS);
    }


    @Override
    public List<TenantPackageCostDO> isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, Integer prescriptionType) {
        // TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder()
        //     .bizType(bizTypeEnum.getCode())
        //     .inquiryWayType(inquiryWayTypeEnum == null ? null : inquiryWayTypeEnum.getCode())
        //     .inquiryBizType(inquiryBizTypeEnum == null ? null : inquiryBizTypeEnum.getCode())
        //     .status(TenantPackageRelationStatusEnum.NORMAL.getCode())
        //     .inServerTime(true).build();
        // // 查询当前门店是否存在正常得可用问诊类型套餐
        // List<TenantPackageCostDO> tenantPackageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
        // // 添加共享套餐
        // tenantPackageCostDOS.addAll(queryTenantSharePackageCost(reqVO));
        // // 筛选额度处方类型
        // tenantPackageCostDOS = tenantPackageCostDOS.stream().filter(tpc -> prescriptionType == null || CollUtil.contains(tpc.getExtOrDefault().getPrescriptionValue(), prescriptionType)).toList();
        // // 判断当前套餐是否还有额度
        // boolean hasCost = tenantPackageCostDOS.stream().anyMatch(tpc -> TenantPackageConstant.isUnlimitedCost(tpc.getSurplusCost()) || tpc.getSurplusCost() > 0);
        List<TenantPackageCostDO> packageCostDOS = availableInquiryCosts(bizTypeEnum, inquiryWayTypeEnum, inquiryBizTypeEnum, prescriptionType);
        if (CollUtil.isEmpty(packageCostDOS)) {
            throw exception(TENANT_PACKAGE_INQUIRY_NO_ERROR);
            // throw new RuntimeException(TENANT_PACKAGE_INQUIRY_NO_ERROR.getMsg());
        }
        return packageCostDOS;
    }


    @Override
    public List<TenantPackageCostDO> availableInquiryCosts(BizTypeEnum bizTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum) {
        return availableInquiryCosts(bizTypeEnum, null, inquiryBizTypeEnum, null);
    }

    /**
     * 获取可用的问诊套餐
     *
     * @param bizTypeEnum
     * @param inquiryWayTypeEnum
     * @param inquiryBizTypeEnum
     * @param prescriptionType
     * @return
     */
    private List<TenantPackageCostDO> availableInquiryCosts(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, Integer prescriptionType) {
        TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder()
            .bizType(bizTypeEnum.getCode())
            .inquiryWayType(inquiryWayTypeEnum == null ? null : inquiryWayTypeEnum.getCode())
            .inquiryBizType(inquiryBizTypeEnum == null ? null : inquiryBizTypeEnum.getCode())
            .status(TenantPackageRelationStatusEnum.NORMAL.getCode())
            .inServerTime(true).build();
        // 查询当前门店是否存在正常得可用问诊类型套餐
        List<TenantPackageCostDO> tenantPackageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
        // 添加共享套餐
        tenantPackageCostDOS.addAll(queryTenantSharePackageCost(reqVO));

        // 筛选额度处方类型
        tenantPackageCostDOS = tenantPackageCostDOS.stream().filter(tpc -> prescriptionType == null || CollUtil.contains(tpc.getExtOrDefault().getPrescriptionValue(), prescriptionType)).toList();

        // 过滤当前套餐是否还有额度
        return tenantPackageCostDOS.stream().filter(c -> TenantPackageConstant.isUnlimitedCost(c.getSurplusCost()) || c.getSurplusCost() > 0).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum wayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, String bizId, Integer prescriptionType) {

        // 查询当前门店是否存在正常得可用问诊类型套餐
        List<TenantPackageCostDO> tenantPackageCostDOS = isValidTenantPackage(BizTypeEnum.HYWZ, wayTypeEnum, inquiryBizTypeEnum, prescriptionType);

        Long tenantId = TenantContextHolder.getRequiredTenantId();

        // 查询不限额度或者 剩余额度够用的 ,扣减额度优先级  如果门店有套餐优先取门店的,没有则取总部的,取逻辑： 有效期内,1.无限最优 2.其次到期时间最近的 3.其次最早开通的
        TenantPackageCostDO tenantPackageCostDO = tenantPackageCostDOS.parallelStream()
            .filter(tpc -> TenantPackageConstant.isUnlimitedCost(tpc.getSurplusCost())
                || tpc.getSurplusCost() >= TenantPackageConstant.INQUIRY_COST_UNIT) // 1. 过滤不限 或者 额度够用的
            .min(Comparator.comparing((TenantPackageCostDO tpc)
                    -> Objects.equals(tpc.getTenantId(), tenantId) ? 0 : 1) // 当前门店的在前 eg: 1 2 3  总部门店在后 eg：4 5 6
                .thenComparing((TenantPackageCostDO tpc) -> TenantPackageConstant.isUnlimitedCost(tpc.getSurplusCost()) ? 0 : 1) // 无限优先 eg : 1 3 2  5 4 6
                .thenComparing(TenantPackageCostDO::getEndTime) // 到期时间最近的 eg: 2 1 3  6 4 5
                .thenComparing(TenantPackageCostDO::getStartTime) // 开通时间最早的 eg: 3 2 1  5 4 6
            ).orElse(null);

        log.debug("查询可用套餐tenantId:{}:cost:{}", tenantId, tenantPackageCostDO);
        if (tenantPackageCostDO == null) {
            throw exception(TENANT_PACKAGE_INQUIRY_NO_ERROR);
            // throw new RuntimeException(TENANT_PACKAGE_INQUIRY_NO_ERROR.getMsg());
        }
        // 如果不是不限额度 扣减问诊额度
        if (!TenantPackageConstant.isUnlimitedCost(tenantPackageCostDO.getSurplusCost())) {
            int updateRow = TenantUtils.executeIgnore(() -> tenantPackageCostMapper.deductTenantCost(tenantPackageCostDO.getId(), tenantPackageCostDO.getSurplusCost(), TenantPackageConstant.INQUIRY_COST_UNIT));
            if (updateRow <= 0) {
                throw exception(TENANT_PACKAGE_INQUIRY_COST_DEDUCT_ERROR);
                // throw new RuntimeException(TENANT_PACKAGE_INQUIRY_COST_DEDUCT_ERROR.getMsg());
            }
        }
        // insert问诊套餐扣除额度记录表
        TenantPackageCostLogDO tenantPackageCostLogDO = TenantPackageCostLogDO.builder().tenantId(TenantContextHolder.getTenantId())
            .costId(tenantPackageCostDO.getId())
            .changeCost(TenantPackageConstant.INQUIRY_COST_UNIT)
            .bizType(tenantPackageCostDO.getBizType())
            .wayType(wayTypeEnum.getCode())
            .bizId(bizId)
            .recordType(CostRecordTypeEnum.INQUIRY.getCode()).build();
        int createRow = tenantPackageCostLogMapper.insert(tenantPackageCostLogDO);
        if (createRow <= 0) {
            throw exception(TENANT_PACKAGE_INQUIRY_RECORD_ERROR);
            // throw new RuntimeException(TENANT_PACKAGE_INQUIRY_RECORD_ERROR.getMsg());
        }
        return TenantDeductCostDto.builder().hospitalPrefs(tenantPackageCostDO.getHospitalPrefs()).costId(tenantPackageCostDO.getId()).costLogId(tenantPackageCostLogDO.getId()).build();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @TenantIgnore
    public void reBackTenantCost(TenantChangeCostDto changeCostDto) {
        // 参数校验
        if (changeCostDto == null || (changeCostDto.getBizId() == null && CollUtil.isEmpty(changeCostDto.getBizIds()))) {
            log.warn("额度加回失败,参数不合法:{}", changeCostDto);
            return;
        }

        // 统一处理单个和批量情况
        List<String> bizIds = changeCostDto.getBizId() != null
            ? Collections.singletonList(changeCostDto.getBizId())
            : changeCostDto.getBizIds();

        // 查询扣减日志记录
        List<TenantPackageCostLogDO> packageCostLogs = tenantPackageCostLogMapper.selectByBizIds(
            bizIds, changeCostDto.getRecordType());

        if (CollUtil.isEmpty(packageCostLogs)) {
            log.info("额度加回失败,未找到对应记录,bizIds:{},recordType:{}", bizIds, changeCostDto.getRecordType());
            return;
        }

        // 批量查询关联的成本记录
        Set<Long> costIds = packageCostLogs.stream().map(TenantPackageCostLogDO::getCostId).collect(Collectors.toSet());

        Map<Long, TenantPackageCostDO> costMap = tenantPackageCostMapper.selectBatchIds(costIds)
            .stream().collect(Collectors.toMap(TenantPackageCostDO::getId, Function.identity()));

        // 按成本ID分组处理
        Map<Long, List<TenantPackageCostLogDO>> logsByCostId = packageCostLogs.stream().collect(Collectors.groupingBy(TenantPackageCostLogDO::getCostId));

        // 批量回退额度
        logsByCostId.forEach((costId, logs) -> {
            TenantPackageCostDO costDO = costMap.get(costId);
            if (costDO == null) {
                log.warn("额度加回失败,未找到成本记录,costId:{}", costId);
                return;
            }
            // 不是无限套餐 加回额度
            if (!TenantPackageConstant.isUnlimitedCost(costDO.getSurplusCost())) {

                int updateCount = TenantUtils.executeIgnore(() -> tenantPackageCostMapper.reBackTenantCost(
                    costId, costDO.getSurplusCost(), (long) logs.size() * TenantPackageConstant.INQUIRY_COST_UNIT));

                if (updateCount <= 0) {
                    throw exception(TENANT_PACKAGE_INQUIRY_COST_DEDUCT_ERROR);
                }
            }
        });

        // 生成并插入日志记录
        List<TenantPackageCostLogDO> logRecords = packageCostLogs.stream()
            .map(log -> {
                TenantPackageCostDO costDO = costMap.get(log.getCostId());
                return TenantPackageCostLogConvert.INSTANCE.costLogConvertReBack(costDO, log, changeCostDto);
            }).filter(Objects::nonNull).collect(Collectors.toList());

        tenantPackageCostLogMapper.insertBatch(logRecords);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantPackageCostStatus(TenantPackageCostDto packageCostDto) {
        // 查询套餐额度
        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(TenantPackageCostConvert.INSTANCE.tenantPackageCostUpdateStatusDto2Vo(packageCostDto));
        if (CollUtil.isEmpty(packageCostDOS)) {
            return;
        }
        Set<TenantPackageCostDO> packageCostDOSet = packageCostDOS.stream().map(p -> {
            TenantPackageCostDO packageCostDO = TenantPackageCostDO.builder().id(p.getId()).status(packageCostDto.getStatus()).build();
            packageCostDO.setUpdater(packageCostDto.getCreator());
            return packageCostDO;
        }).collect(Collectors.toSet());
        // 更新 cost
        tenantPackageCostMapper.updateBatch(packageCostDOSet);
        // 记录log
        Set<TenantPackageCostLogDO> packageCostLogDOS = packageCostDOSet.stream().map(p -> TenantPackageCostLogDO.builder()
            .tenantId(TenantContextHolder.getTenantId())
            .wayType(p.getInquiryWayType())
            .costId(p.getId())
            .bizType(p.getBizType())
            .recordType(p.getStatus()).build()).collect(Collectors.toSet());
        tenantPackageCostLogMapper.insertBatch(packageCostLogDOS);
    }

    /**
     * 切套餐包(作废旧套餐，新增套餐)
     *
     * @param packageCostDto 额度dto
     */
    @Override
    @TenantIgnore
    @Transactional(rollbackFor = Exception.class)
    public void changeTenantPackageCost(TenantPackageCostDto packageCostDto) {
        List<TenantPackageCostDO> existsNewCostList = tenantPackageCostMapper.queryTenantPackageCostByCondition(
            TenantPackageCostReqVO.builder().tenantId(packageCostDto.getTenantId()).tenantPackageId(packageCostDto.getTenantPackageId()).build());
        if (CollUtil.isNotEmpty(existsNewCostList)) {
            return;
        }
        // 查询问诊套餐关系id 额度是否存在
        TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder().tenantId(packageCostDto.getTenantId()).tenantPackageId(packageCostDto.getOldTenantPackageId()).build();
        List<TenantPackageCostDO> oldCostList = tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
        // 旧额度
        List<InquiryPackageItem> oldCosts = TenantPackageCostConvert.INSTANCE.convertCostInquiryPackageItems(oldCostList);
        // 剩余额度
        List<InquiryPackageItem> surplusCosts = TenantPackageCostConvert.INSTANCE.convertSurplusInquiryPackageItems(oldCostList);
        // 计算新剩余额度
        List<InquiryPackageItem> newSurplusCosts = compareAndCalculateCost(packageCostDto, oldCosts, surplusCosts);
        Map<Integer, Long> newSurplusMap = CollectionUtils.convertMap(newSurplusCosts, InquiryPackageItem::getInquiryWayType, InquiryPackageItem::getCount);

        List<TenantPackageCostDO> tenantPackageCostDOList = TenantPackageCostConvert.INSTANCE.tenantPackageCostDto2CostRelationDOLists(packageCostDto);
        List<TenantPackageCostDO> insertCostList = tenantPackageCostDOList.stream().peek(c -> c.setSurplusCost(newSurplusMap.get(c.getInquiryWayType()))).collect(Collectors.toList());

        // 新增额度 + 日志
        tenantPackageCostMapper.insertBatch(insertCostList);
        List<TenantPackageCostLogDO> createLogs = TenantPackageCostLogConvert.INSTANCE.tenantPackageCostRelationDO2RecordDos(insertCostList, CostRecordTypeEnum.NORMAL);
        tenantPackageCostLogMapper.insertBatch(createLogs);

        // 作废旧数据 + 日志
        Set<TenantPackageCostDO> updateCosts = oldCostList.stream().map(c -> TenantPackageCostDO.builder().id(c.getId()).status(TenantPackageRelationStatusEnum.ABANDONED.getCode()).build()).collect(Collectors.toSet());
        tenantPackageCostMapper.updateById(updateCosts);
        List<TenantPackageCostLogDO> abandonedLogs = TenantPackageCostLogConvert.INSTANCE.tenantPackageCostRelationDO2RecordDos(oldCostList, CostRecordTypeEnum.ABANDONED);
        tenantPackageCostLogMapper.insertBatch(abandonedLogs);

    }

    /**
     * 计算切换套餐额度 - 主入口方法 根据inquiryBizType选择不同的比较策略
     */
    private List<InquiryPackageItem> compareAndCalculateCost(TenantPackageCostDto tenantPackageCostDto, List<InquiryPackageItem> oldCosts, List<InquiryPackageItem> surplusCosts) {

        boolean isDrugstoreInquiry = Objects.equals(tenantPackageCostDto.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode());

        if (isDrugstoreInquiry) {
            return compareAndCalculateCostForDrugstore(tenantPackageCostDto, oldCosts, surplusCosts);
        } else {
            return compareAndCalculateCostForGeneral(tenantPackageCostDto, oldCosts, surplusCosts);
        }
    }

    /**
     * 药店问诊的切换套餐额度计算 比较维度：医院编码+处方类型+问诊方式 计算切换套餐额度 1.问诊类型：新套餐比旧套餐少则去掉,多则新增 2.问诊额度：剩余额度和新套餐额度 取最小值 例如: 旧 图文 100  余额 10 新 视频 200  图文 50： 余额 视频200  图文 10   // 新套餐 - 旧套餐 <=0   取新额度 和 剩余额度 的最小值 新 图文 5 ：          余额 5                // 新套餐 - 旧套餐
     * <=0   取新额度 和 剩余额度 的最小值 新 图文 150：余额 图文 60 (|150-100| + 剩余的10 ) // 新套餐 - 旧套餐 > 0   取新额度 - 旧套餐的 差值 + 剩余额度 业务逻辑： 1. 构建复合键映射：将旧套餐和剩余额度按复合键（医院+处方类型+问诊方式）进行分组 2. 遍历新套餐的每个项目，使用复合键查找对应的旧额度和剩余额度 3. 应用切换套餐的计算规则： - 如果新额度 <= 旧额度：取 min(新额度, 剩余额度)
     * - 如果新额度 > 旧额度：取 剩余额度 + (新额度 - 旧额度)
     */
    private List<InquiryPackageItem> compareAndCalculateCostForDrugstore(TenantPackageCostDto tenantPackageCostDto, List<InquiryPackageItem> oldCosts, List<InquiryPackageItem> surplusCosts) {

        // 构建复合键映射：医院+处方类型+问诊方式 -> 额度
        Map<String, Long> oldCostMap = buildCompositeCostMap(oldCosts);
        Map<String, Long> oldSurplusCostMap = buildCompositeCostMap(surplusCosts);

        return tenantPackageCostDto.getInquiryPackageItems().stream().peek(c -> {
            // 获取有效的问诊项目列表（兼容新旧格式）
            List<InquiryPackageItem> effectiveItems = c.effectiveItems();
            for (InquiryPackageItem item : effectiveItems) {
                // 构建复合键：医院编码_处方类型_问诊方式
                String compositeKey = buildCompositeKey(c.getHospitalPref(), c.getPrescriptionValue(), item.getInquiryWayType());
                Long oldCost = oldCostMap.get(compositeKey);
                Long oldSurplusCost = oldSurplusCostMap.get(compositeKey);

                if (oldCost != null && oldSurplusCost != null) {
                    log.info("更换套餐包从[{}]->[{}],药店问诊复合键[{}],新旧数据变更,新额度:{},旧额度:{},剩余额度:{}",
                        tenantPackageCostDto.getOldTenantPackageId(), tenantPackageCostDto.getTenantPackageId(),
                        compositeKey, item.getCount(), oldCost, oldSurplusCost);

                    // 应用切换套餐的计算规则
                    if (item.getCount() - oldCost <= 0) {
                        // 新套餐额度 <= 旧套餐额度：取新额度和剩余额度的最小值
                        item.setCount(Math.min(item.getCount(), oldSurplusCost));
                    } else {
                        // 新套餐额度 > 旧套餐额度：取剩余额度 + (新额度 - 旧额度)
                        item.setCount((TenantPackageConstant.isUnlimitedCost(oldSurplusCost) ? 0 : oldSurplusCost) +
                            (item.getCount() - (TenantPackageConstant.isUnlimitedCost(oldCost) ? 0 : oldCost)));
                    }

                    log.info("更换套餐包从[{}]->[{}],药店问诊复合键[{}],最新剩余额度:{}",
                        tenantPackageCostDto.getOldTenantPackageId(), tenantPackageCostDto.getTenantPackageId(),
                        compositeKey, item.getCount());
                }
            }
        }).collect(Collectors.toList());
    }

    /**
     * 通用问诊的切换套餐额度计算（原有逻辑） 比较维度：问诊方式
     * <p>
     * 业务逻辑： 1. 构建简单映射：将旧套餐和剩余额度按问诊方式进行分组 2. 遍历新套餐的每个项目，使用问诊方式查找对应的旧额度和剩余额度 3. 应用相同的切换套餐计算规则
     */
    private List<InquiryPackageItem> compareAndCalculateCostForGeneral(TenantPackageCostDto tenantPackageCostDto, List<InquiryPackageItem> oldCosts, List<InquiryPackageItem> surplusCosts) {

        // 构建简单映射：问诊方式 -> 额度
        Map<Integer, Long> oldCostMap = CollectionUtils.convertMap(oldCosts, InquiryPackageItem::getInquiryWayType, InquiryPackageItem::getCount);
        Map<Integer, Long> oldSurplusCostMap = CollectionUtils.convertMap(surplusCosts, InquiryPackageItem::getInquiryWayType, InquiryPackageItem::getCount);

        return tenantPackageCostDto.getInquiryPackageItems().stream().peek(c -> {
            if (oldCostMap.get(c.getInquiryWayType()) != null) {
                Long oldCost = oldCostMap.get(c.getInquiryWayType());
                Long oldSurplusCost = oldSurplusCostMap.get(c.getInquiryWayType());
                log.info("更换套餐包从[{}]->[{}],新旧数据变更,新额度:{},旧额度:{},剩余额度:{}",
                    tenantPackageCostDto.getOldTenantPackageId(), tenantPackageCostDto.getTenantPackageId(), c.getCount(), oldCost, oldSurplusCost);

                // 应用切换套餐的计算规则（与药店问诊相同的逻辑）  // 新套餐 - 旧套餐 <=0   取新额度 和 剩余额度 的最小值
                if (c.getCount() - oldCost <= 0) {
                    c.setCount(Math.min(c.getCount(), oldSurplusCost));
                } else {
                    // 新套餐 - 旧套餐 > 0   取新额度 - 旧套餐的 差值 + 余额   (-1  无限则取 0 相加)
                    c.setCount((TenantPackageConstant.isUnlimitedCost(oldSurplusCost) ? 0 : oldSurplusCost) +
                        (c.getCount() - (TenantPackageConstant.isUnlimitedCost(oldCost) ? 0 : oldCost)));
                }

                log.info("更换套餐包从[{}]->[{}],新旧数据变更,最新剩余额度:{}",
                    tenantPackageCostDto.getOldTenantPackageId(), tenantPackageCostDto.getTenantPackageId(), c.getCount());
            }
        }).collect(Collectors.toList());
    }

    /**
     * 构建药店问诊的复合键映射：医院+处方类型+问诊方式 -> 额度
     */
    private Map<String, Long> buildCompositeCostMap(List<InquiryPackageItem> costs) {
        Map<String, Long> costMap = new HashMap<>();
        if (CollUtil.isEmpty(costs)) {
            return costMap;
        }

        for (InquiryPackageItem cost : costs) {
            for (InquiryPackageItem item : cost.effectiveItems()) {
                String key = buildCompositeKey(cost.getHospitalPref(), cost.getPrescriptionValue(), item.getInquiryWayType());
                costMap.put(key, item.getCount());
            }
        }
        return costMap;
    }

    /**
     * 构建复合键：医院编码_处方类型_问诊方式
     */
    private String buildCompositeKey(List<String> hospitalPref, List<Integer> prescriptionValue, Integer inquiryWayType) {
        String hospitalStr = CollUtil.isEmpty(hospitalPref) ? "" : String.join(",", hospitalPref);
        String prescriptionStr = CollUtil.isEmpty(prescriptionValue) ? "" : prescriptionValue.stream().map(String::valueOf).collect(Collectors.joining(","));
        return hospitalStr + "_" + prescriptionStr + "_" + inquiryWayType;
    }


    @Override
    public Long createTenantPackageCostRelation(TenantPackageCostSaveReqVO createReqVO) {
        // 插入
        TenantPackageCostDO tenantPackageCostRelation = BeanUtils.toBean(createReqVO, TenantPackageCostDO.class);
        tenantPackageCostMapper.insert(tenantPackageCostRelation);
        // 返回
        return tenantPackageCostRelation.getId();
    }

    @Override
    public void updateTenantPackageCostRelation(TenantPackageCostSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantPackageCostRelationExists(updateReqVO.getId());
        // 更新
        TenantPackageCostDO updateObj = BeanUtils.toBean(updateReqVO, TenantPackageCostDO.class);
        tenantPackageCostMapper.updateById(updateObj);
    }

    @Override
    public void deleteTenantPackageCostRelation(Long id) {
        // 校验存在
        validateTenantPackageCostRelationExists(id);
        // 删除
        tenantPackageCostMapper.deleteById(id);
    }

    private TenantPackageCostDO validateTenantPackageCostRelationExists(Long id) {
        TenantPackageCostDO packageCostDO = tenantPackageCostMapper.selectById(id);
        if (packageCostDO == null) {
            throw exception(TENANT_PACKAGE_COST_RELATION_NOT_EXISTS);
        }
        return packageCostDO;
    }

    @Override
    public PageResult<TenantPackageCostDO> getTenantPackageCostRelationPage(TenantPackageCostReqVO pageReqVO) {
        return tenantPackageCostMapper.selectPage(pageReqVO);
    }


    @Override
    public List<TenantPackageCostDO> queryTenantPackageCostByCondition(TenantPackageCostReqVO reqVO) {
        return tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
    }


    @Override
    public boolean validPlatformReviewCost() {
        TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder().bizType(BizTypeEnum.HYWZ.getCode())
            .status(TenantPackageRelationStatusEnum.NORMAL.getCode())
            .inquiryBizType(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode())
            .inquiryAuditType(InquiryAuditTypeEnum.PLATFORM.getCode())
            .inServerTime(true).build();
        // 查询当前门店是否存在正常得可用问诊类型套餐
        List<TenantPackageCostDO> tenantPackageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO);
        // 判断当前套餐是否还有额度
        return tenantPackageCostDOS.stream().anyMatch(tpc -> TenantPackageConstant.isUnlimitedCost(tpc.getSurplusCost()) || tpc.getSurplusCost() > 0);
    }

    @Override
    @TenantIgnore
    public TenantPackageCostDto getTenantPackageCostById(Long costId) {
        final TenantPackageCostDO packageCostDO = tenantPackageCostMapper.selectById(costId);
        return TenantPackageCostConvert.INSTANCE.convertDto(packageCostDO);
    }

    @Override
    @TenantIgnore
    public TenantPackageCostDto getTenantPackageCostByLogBizId(String bizId, CostRecordTypeEnum costRecordTypeEnum) {
        // 根据业务id查询扣减日志记录获取 costId
        TenantPackageCostLogDO packageCostLogDO = tenantPackageCostLogMapper.selectByBizId(bizId, costRecordTypeEnum.getCode());
        if (packageCostLogDO == null) {
            log.info("根据套餐costLog 未找到对应记录,bizId:{},recordType:{}", bizId, costRecordTypeEnum.getDesc());
            return null;
        }
        return getTenantPackageCostById(packageCostLogDO.getCostId());
    }


    @Override
    @TenantIgnore
    public Map<String, TenantPackageCostDto> getTenantPackageCostByLogBizIds(List<String> bizIds, CostRecordTypeEnum costRecordTypeEnum) {
        if (CollUtil.isEmpty(bizIds)) {
            return Map.of();
        }

        Map<String, Long> costLogMap = tenantPackageCostLogMapper.selectByBizIds(bizIds, costRecordTypeEnum.getCode())
            .stream().collect(Collectors.toMap(TenantPackageCostLogDO::getBizId, TenantPackageCostLogDO::getCostId, (a, b) -> b));

        if (CollUtil.isEmpty(costLogMap)) {
            return Map.of();
        }

        Map<Long, TenantPackageCostDO> costDOMap = tenantPackageCostMapper.selectBatchIds(costLogMap.values()).stream().collect(Collectors.toMap(TenantPackageCostDO::getId, Function.identity()));

        return costLogMap.entrySet().stream().map(t -> {
            TenantPackageCostDO costDO = costDOMap.get(t.getValue());
            TenantPackageCostDto costDto = TenantPackageCostConvert.INSTANCE.convertDto(costDO);
            return Map.entry(t.getKey(), costDto);
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }


    /**
     * 获取门店共享套餐
     *
     * @param reqVO
     * @return
     */
    private List<TenantPackageCostDO> queryTenantSharePackageCost(TenantPackageCostReqVO reqVO) {

        Long tenantId = Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId());
        // 查门店被共享的总部套餐
        List<TenantPackageShareRelationDto> shareRelationList = tenantPackageShareApi.getTenantPackageShareRelationList(tenantId, BizTypeEnum.fromCode(reqVO.getBizType()));

        if (CollUtil.isEmpty(shareRelationList)) {
            return new ArrayList<>();
        }

        List<Long> sharePackageIds = shareRelationList.stream().map(TenantPackageShareRelationDto::getTenantPackageId).distinct().toList();

        reqVO.setTenantPackageIds(sharePackageIds).setTenantId(shareRelationList.getFirst().getHeadTenantId()).setTenantIds(null);
        // 查总部生效的套餐
        return TenantUtils.execute(reqVO.getTenantId(), () -> tenantPackageCostMapper.queryTenantPackageCostByCondition(reqVO));

    }
}