package com.xyy.saas.inquiry.drugstore.api.tenant.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.param.ParamConfigExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店参数配置 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantParamConfigDto extends BaseDto {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 系统类型 0-问诊,1-saas...
     */
    private Integer bizType;
    /**
     * 参数类型 eg:1荷叶问诊服务 x问诊小程序二维码
     */
    private Integer paramType;
    /**
     * 参数类型 eg:1荷叶问诊服务 x问诊小程序二维码
     */
    private String paramName;
    /**
     * 值 eg:1 开启 x http://xxx.jpg
     */
    private String paramValue;

    /**
     * 扩展信息
     */
    private ParamConfigExtDto ext;
    /**
     * 参数描述
     */
    private String description;

}