package com.xyy.saas.inquiry.drugstore.api.tenant;

import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import java.util.List;
import java.util.Map;

/**
 * 商户套餐包额度api
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:31
 */
public interface TenantPackageCostApi {

    /**
     * 开通门店套餐 - 初始化套餐包额度
     *
     * @return
     */
    void initTenantPackageCost(TenantPackageCostDto tenantPackageCostDto);

    /**
     * 判断药店套餐是否有效 1. 套餐是否存在 2. 套餐是否过期 3. 套餐是否在有效期内 4. 套餐是有额度限制的，且当前用户的额度是否足够 默认判断药店问诊套餐
     *
     * @param bizTypeEnum        业务线
     * @param inquiryWayTypeEnum 问诊类型
     */
    default void isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, Integer prescriptionType) {
        isValidTenantPackage(bizTypeEnum, inquiryWayTypeEnum, InquiryBizTypeEnum.DRUGSTORE_INQUIRY, prescriptionType);
    }

    default void isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum) {
        isValidTenantPackage(bizTypeEnum, inquiryWayTypeEnum, inquiryBizTypeEnum, null);
    }


    void isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, Integer prescriptionType);


    /**
     * 扣减门店套餐额度
     *
     * @param inquiryWayTypeEnum 问诊类型
     * @param bizId              业务Id
     * @return
     */
    default TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum inquiryWayTypeEnum, String bizId, Integer prescriptionType) {
        return deductTenantCost(inquiryWayTypeEnum, InquiryBizTypeEnum.DRUGSTORE_INQUIRY, bizId, prescriptionType);
    }

    TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, String bizId, Integer prescriptionType);

    /**
     * 问诊额度回退
     *
     * @param changeCostDto 额度changeCostDto
     */
    void reBackTenantCost(TenantChangeCostDto changeCostDto);

    /**
     * 判断是否有平台审方的套餐 仅校验药店问诊类型 的套餐
     *
     * @return true-有
     */
    boolean validPlatformReviewCost();

    /**
     * 根据额度id查询门店套餐额度信息
     *
     * @param costId 额度id
     * @return 套餐额度信息
     */
    TenantPackageCostDto getTenantPackageCostById(Long costId);

    /**
     * 根据业务id查询所扣除的套餐额度信息
     *
     * @param bizId              业务ID
     * @param costRecordTypeEnum 额度记录类型
     * @return
     */
    TenantPackageCostDto getTenantPackageCostByLogBizId(String bizId, CostRecordTypeEnum costRecordTypeEnum);

    /**
     * 根据业务ids查询所扣除的套餐额度信息
     *
     * @param bizIds             业务ids
     * @param costRecordTypeEnum 操作类型
     * @return <BizId,TenantPackageCostDto>
     */
    Map<String, TenantPackageCostDto> getTenantPackageCostByLogBizIds(List<String> bizIds, CostRecordTypeEnum costRecordTypeEnum);

    /**
     * 根据用户id查询用户套餐信息
     *
     * @param userId 用户id
     * @return 套餐信息
     */
    List<TenantPackageCostDto> selectUserTenantPackages(Long userId, List<TenantPackageEffectiveStatusEnum> effectiveStatusEnums);

}
