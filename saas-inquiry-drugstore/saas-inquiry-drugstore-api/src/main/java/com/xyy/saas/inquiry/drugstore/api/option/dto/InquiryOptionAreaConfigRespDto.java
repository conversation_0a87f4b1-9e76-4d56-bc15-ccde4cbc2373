package com.xyy.saas.inquiry.drugstore.api.option.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import java.util.List;

@Schema(description = "管理后台 - 区域-问诊配置选项 Response VO")
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryOptionAreaConfigRespDto extends BaseDto {

    private Long id;

    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    private Integer optionType;
    /**
     * 配置值
     */
    private String optionValue;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;

    // 问诊单配置-抗菌药品配置
    /**
     * 抗菌药品配置-分级目录id
     */
    private Long formAntimicrobialDrugCatalogId;
    /**
     * 抗菌药品配置-分级目录名称
     */
    private String formAntimicrobialDrugCatalogName;
    /**
     * 抗菌药品配置-分级目录文件链接
     */
    private String formAntimicrobialDrugCatalogUrl;

    // 问诊流程配置-处方类型填写开关
    /**
     * 问诊流程配置-处方类型填写开关-是否必填
     */
    private Boolean procPrescriptionTypeRequired;

    // 问诊流程配置-家庭住址填写开关
    /**
     * 问诊流程配置-家庭住址填写开关-是否必填
     */
    private Boolean procHomeAddressRequired;

    // 问诊流程配置-监护人填写开关
    /**
     * 问诊流程配置-监护人填写开关-是否必填
     */
    // private Boolean procGuardianRequired;

    @Schema(description = "监护人填写年龄最低限度,如果<=6岁 则页面需要填写监护人信息", example = "6")
    private Integer procGuardianAge;

    // 问诊流程配置-问诊合规配置(地区规则)
    /**
     * 问诊合规配置
     */
    private Boolean procInquiryCompliance;

    /**
     * 问诊合规配置-患者年龄限制-大于等于
     */
    private Integer procInquiryComplianceForPatientAgeGe;

    /**
     * 问诊合规配置-患者年龄限制-且小于
     */
    private Integer procInquiryComplianceForPatientAgeLt;

    /**
     * 问诊合规配置-妊娠哺乳是否可发起问诊
     */
    private Boolean procInquiryComplianceAllowForPregnancyLactation;

    // 问诊流程配置-录屏问诊配置
    /**
     * 录屏问诊配置
     */
    private Boolean procVideoInquiry;
    /**
     * 录屏问诊配置-比例
     */
    private Integer procVideoInquiryRatio;

    // 问诊流程配置-医生接诊默认页面
    /**
     * 医生接诊默认页面
     */
    private String procDoctorAdmissionDefaultPage;


    /**
     * 医院编码 - 全真人问诊时使用
     */
    private List<String> presAllRealPeopleInquiryHospitalPrefs;

    private List<String> presAllRealPeopleInquiryHospitalPrefNames;


    /**
     * area 医院编码
     */
    private List<String> areaInquiryHospitalPrefs;

    private List<String> areaInquiryHospitalNames;


    public InquiryOptionAreaConfigRespDto extToThis(String ext) {
        if (StringUtils.isBlank(ext)) {
            return this;
        }
        InquiryOptionAreaConfigRespDto extObject = JsonUtils.parseObject(ext, InquiryOptionAreaConfigRespDto.class);
        if (extObject == null) {
            return this;
        }
        // null属性忽略
        BeanUtil.copyProperties(extObject, this, CopyOptions.create().ignoreNullValue());
        return this;
    }

}