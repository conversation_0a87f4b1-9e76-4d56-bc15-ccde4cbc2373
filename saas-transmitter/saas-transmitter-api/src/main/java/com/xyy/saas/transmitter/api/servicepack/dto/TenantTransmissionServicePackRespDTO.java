package com.xyy.saas.transmitter.api.servicepack.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantTransmissionServicePackRespDTO extends BaseDto {

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 服务包名称
     */
    private String name;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）
     */
    private Integer organType;
    /**
     * 协议配置包id
     */
    private Integer configPackageId;
    /**
     * 动态库资源
     */
    private String dllResource;
    /**
     * 小票模板资源
     */
    private String ticketResource;
    /**
     * 账单模板资源
     */
    private String billResource;
    /**
     * 拓展资源
     */
    private String extResource;
    /**
     * 接口文档
     */
    private String apiDoc;
    /**
     * 版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）
     */
    private Long version;
    /**
     * 环境：0-测试；1-灰度；2-上线；
     */
    private Integer env;
    /**
     * 是否禁用
     */
    private Boolean disable;

} 