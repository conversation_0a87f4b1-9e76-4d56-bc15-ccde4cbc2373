package com.xyy.saas.transmitter.server.service.servicepack;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackRespVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackSaveReqVO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import java.util.List;

/**
 * 服务包 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionServicePackService {

    /**
     * 创建服务包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTransmissionServicePack(@Valid TransmissionServicePackSaveReqVO createReqVO);

    /**
     * 更新服务包
     *
     * @param updateReqVO 更新信息
     */
    void updateTransmissionServicePack(@Valid TransmissionServicePackSaveReqVO updateReqVO);

    /**
     * 删除服务包
     *
     * @param id 编号
     */
    void deleteTransmissionServicePack(Integer id);

    /**
     * 获得服务包
     *
     * @param id 编号
     * @return 服务包
     */
    TransmissionServicePackRespVO getTransmissionServicePack(Integer id);

    /**
     * 获得服务包分页
     *
     * @param pageReqVO 分页查询
     * @return 服务包分页
     */
    PageResult<TransmissionServicePackRespVO> getTransmissionServicePackPage(TransmissionServicePackPageReqVO pageReqVO);

    /**
     * 查询服务包列表
     *
     * @param reqVO 请求入参
     * @return 服务包列表
     */
    List<TenantTransmissionServicePackRespDTO> selectList(TransmissionServicePackPageReqVO reqVO);

    /**
     * 根据条件查询一个服务包
     *
     * @param reqVO
     * @return
     */
    TenantTransmissionServicePackRespDTO selectOneServicePack(TransmissionServicePackPageReqVO reqVO);

    /**
     * 查询服务包数量
     *
     * @param reqVO
     * @return
     */
    Long selectCountServicePack(TransmissionServicePackPageReqVO reqVO);

    /**
     * 查询服务包配置 根据租户和业务信息查询相关的服务包配置
     * <p>
     * 查询流程： 1. 参数校验：验证必要参数的有效性 2. 查询关系：获取租户绑定的服务包关系 3. 获取配置：查询服务包的详细配置 4. 数据过滤：根据业务类型过滤配置
     * <p>
     * 异常处理： - 查询失败返回空列表 - 记录详细的错误日志 - 保证查询过程的稳定性
     *
     * @param tenantId               租户ID
     * @param organType              机构类型
     * @param nodeType               节点类型
     * @param servicePackId          服务包ID
     * @param validateProtocolConfig 是否需要验证协议配置存在
     * @return 服务包配置列表
     */
    List<TransmissionServicePackDTO> queryTenantServicePackByNode(Long tenantId, Integer organType,
        NodeTypeEnum nodeType, Integer servicePackId, boolean validateProtocolConfig);


}