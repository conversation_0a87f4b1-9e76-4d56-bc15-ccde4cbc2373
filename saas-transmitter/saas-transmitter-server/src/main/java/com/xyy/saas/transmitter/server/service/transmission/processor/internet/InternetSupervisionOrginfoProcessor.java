package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.*;
import org.springframework.stereotype.Component;

/**
 * 互联网监管-机构信息
 */
@Component
public class InternetSupervisionOrginfoProcessor extends InternetSupervisionLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.INTERNET_SUPERVISION_INSTITUTION_INFO_COLLECTION;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        final PrescriptionTransmitterDTO dataObj = transmissionReqDTO.getDataObj(PrescriptionTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getFullName()).setIdCard(dataObj.getIdCard()).setBusinessNo(dataObj.getBusinessNo());
    }

    @Override
    protected void validateParameters(TransmissionReqDTO transmissionReqDTO, PreValidationConfig config) {
        super.validateParameters(transmissionReqDTO, config);
    }

    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO, PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        PrescriptionTransmitterDTO data = (PrescriptionTransmitterDTO) transmissionReqDTO.getData();
        //
        fillOperateUserInfo(transmissionReqDTO.getAux(), config, data.getUserId());
        fillPrescriptionParameters(transmissionReqDTO, config, data);
    }

    @Override
    protected boolean preFilter(TransmissionReqDTO transmissionReqDTO, FilterConfig filterConfig) {
        return super.preFilter(transmissionReqDTO, filterConfig);
    }

    @Override
    protected void processLogicConfig(TransmissionReqDTO transmissionReqDTO, TransmissionTaskRecordSaveReqVO task,
        LogicValidationConfig config, TransmissionServicePackDTO servicePack) {
        super.processLogicConfig(transmissionReqDTO, task, config, servicePack);
    }
} 