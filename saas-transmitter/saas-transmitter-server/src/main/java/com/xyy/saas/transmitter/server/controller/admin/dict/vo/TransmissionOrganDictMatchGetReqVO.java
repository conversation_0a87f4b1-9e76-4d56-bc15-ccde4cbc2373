package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;

@Schema(description = "管理后台 - 服务商数据字典配对详情 Request VO")
@Data
@ToString(callSuper = true)
@Builder
public class TransmissionOrganDictMatchGetReqVO implements Serializable {

    @Schema(description = "医药行业行政机构ID", example = "4327")
    private Integer organId;

    @Schema(description = "字典类型", example = "1")
    private String dictType;

    @Schema(description = "saas字典名", example = "26981")
    private String dictName;

    @Schema(description = "saas字典id", example = "26981")
    private Long dictId;


}