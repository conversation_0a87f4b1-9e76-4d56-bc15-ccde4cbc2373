package com.xyy.saas.transmitter.server.controller.admin.config;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemRespVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import com.xyy.saas.transmitter.server.service.config.TransmissionConfigItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 协议配置")
@RestController
@RequestMapping("/transmitter/transmission-config-item")
@Validated
public class TransmissionConfigItemController {

    @Resource
    private TransmissionConfigItemService transmissionConfigItemService;

    @PostMapping("/create-or-update")
    @Operation(summary = "创建或更新协议配置")
    @Idempotent
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:create')")
    public CommonResult<Integer> createOrUpdateTransmissionConfigItem(@Valid @RequestBody TransmissionConfigItemSaveReqVO reqVO) {
        return success(transmissionConfigItemService.createOrUpdateTransmissionConfigItem(reqVO));
    }

    @Deprecated
    @PostMapping("/create")
    @Operation(summary = "创建协议配置")
    @Idempotent
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:create')")
    public CommonResult<Integer> createTransmissionConfigItem(@Valid @RequestBody TransmissionConfigItemSaveReqVO createReqVO) {
        return success(transmissionConfigItemService.createOrUpdateTransmissionConfigItem(createReqVO));
    }

    @Deprecated
    @PutMapping("/update")
    @Operation(summary = "更新协议配置")
    @Idempotent
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:update')")
    public CommonResult<Boolean> updateTransmissionConfigItem(@Valid @RequestBody TransmissionConfigItemSaveReqVO updateReqVO) {
        transmissionConfigItemService.createOrUpdateTransmissionConfigItem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Idempotent
    @Operation(summary = "删除协议配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:delete')")
    public CommonResult<Boolean> deleteTransmissionConfigItem(@RequestParam("id") Integer id) {
        transmissionConfigItemService.deleteTransmissionConfigItem(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:query')")
    public CommonResult<TransmissionConfigItemRespVO> getTransmissionConfigItem(@RequestParam("id") Integer id) {
        TransmissionConfigItemDO transmissionConfigItem = transmissionConfigItemService.getTransmissionConfigItem(id);
        return success(BeanUtils.toBean(transmissionConfigItem, TransmissionConfigItemRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议配置分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:query')")
    public CommonResult<PageResult<TransmissionConfigItemRespVO>> getTransmissionConfigItemPage(@Valid TransmissionConfigItemPageReqVO pageReqVO) {
        PageResult<TransmissionConfigItemDO> pageResult = transmissionConfigItemService.getTransmissionConfigItemPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransmissionConfigItemRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得协议配置分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:query')")
    public CommonResult<List<TransmissionConfigItemRespVO>> getTransmissionConfigItemList(@Valid TransmissionConfigItemPageReqVO reqVO) {
        List<TransmissionConfigItemDO> list = transmissionConfigItemService.getTransmissionConfigItemList(reqVO);
        return success(BeanUtils.toBean(list, TransmissionConfigItemRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议配置 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-item:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionConfigItemExcel(@Valid TransmissionConfigItemPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionConfigItemDO> list = transmissionConfigItemService.getTransmissionConfigItemPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议配置.xls", "数据", TransmissionConfigItemRespVO.class,
            BeanUtils.toBean(list, TransmissionConfigItemRespVO.class));
    }

}