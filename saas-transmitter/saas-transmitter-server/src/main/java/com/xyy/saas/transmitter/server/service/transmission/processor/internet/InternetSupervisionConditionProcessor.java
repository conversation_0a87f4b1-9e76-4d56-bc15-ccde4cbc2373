package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import cn.hutool.core.bean.BeanUtil;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.FilterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.LogicValidationConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreValidationConfig;
import org.springframework.stereotype.Component;

/**
 * 互联网监管-线上处方点评处理器 处理线上处方点评相关的业务逻辑
 * <p>
 * 处理职责： 1. 参数校验：验证处方点评参数 2. 数据处理：处理点评内容 3. 结果转换：转换为统一格式
 */
@Component
public class InternetSupervisionConditionProcessor extends InternetSupervisionLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.INTERNET_SUPERVISION_CONDITION;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        PrescriptionSupervisionConditionTransmitterDTO dataObj = transmissionReqDTO.getDataObj(PrescriptionSupervisionConditionTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getFullName()).setIdCard(dataObj.getIdCard()).setBusinessNo(dataObj.getBusinessNo());
    }

    @Override
    protected void validateParameters(TransmissionReqDTO transmissionReqDTO, PreValidationConfig config) {
        super.validateParameters(transmissionReqDTO, config);
        // 添加处方点评特有的参数验证逻辑
    }

    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO, PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);
        // 添加处方点评特有的前置参数处理逻辑
        // 添加处方点评特有的后置参数处理逻辑
        PrescriptionSupervisionConditionTransmitterDTO data = (PrescriptionSupervisionConditionTransmitterDTO) transmissionReqDTO.getData();
        // 0.填充CA信息
        fillPrescriptionCaInfo(transmissionReqDTO.getAux(), BeanUtil.toBean(config, PostParameterConfig.class), data.getPref());
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);

    }

    @Override
    protected boolean preFilter(TransmissionReqDTO transmissionReqDTO, FilterConfig filterConfig) {
        // 添加处方点评特有的前置过滤逻辑
        return super.preFilter(transmissionReqDTO, filterConfig);
    }

    @Override
    protected void processLogicConfig(TransmissionReqDTO transmissionReqDTO, TransmissionTaskRecordSaveReqVO task,
        LogicValidationConfig config, TransmissionServicePackDTO servicePack) {
        super.processLogicConfig(transmissionReqDTO, task, config, servicePack);
        // 添加处方点评特有的业务处理逻辑
    }
} 