package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.InquiryClinicalCaseApi;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseRespDto;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.registration.MedicalRegistrationApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureContractApi;
import com.xyy.saas.inquiry.signature.dto.signature.ElectSignInfoDto;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.convert.transmission.TransmissionInternetConvert;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 互联网监管 基础处理器 提供 互联网监管通用的处理逻辑
 * <p>
 * 核心功能： 1. 通用参数处理 2. 基础校验逻辑 3.  互联网监管特定配置处理
 */
@Component
public abstract class InternetSupervisionLogicConfigProcessor extends LogicConfigProcessor {

    @Override
    public OrganTypeEnum getOrganType() {
        return OrganTypeEnum.INTERNET_SUPERVISION;
    }

    /**
     * 扩展字段常量类
     */
    public static class AuxConstant {

        public static final String DOCTOR_INFO = "doctorInfo";

        public static final String PHARMACIST_INFO = "pharmacistInfo";

        public static final String MEDICAL_REGISTRATION_INFO = "medicalRegistrationInfo";

        public static final String PRESCRIPTION_DETAIL = "prescriptionDetail";

        public static final String STDLIB_PRODUCT = "stdlibProduct";

        public static final String CLINICAL_CASE = "clinicalCase";

        public static final String PRESCRIPTION_CA = "prescriptionCa";

        public static final String OPERATE_USER_INFO = "operateUserInfo";

        public static final String INQUIRY_DETAIL_INFO = "inquiryDetailInfo";

    }

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @DubboReference
    private InquiryPharmacistApi inquiryPharmacistApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @DubboReference
    private InquiryClinicalCaseApi inquiryClinicalCaseApi;

    @DubboReference
    private MedicalRegistrationApi medicalRegistrationApi;

    @DubboReference
    private InquirySignatureContractApi inquirySignatureContractApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    /**
     * 填充医生 药师信息+职称
     *
     * @param aux            辅助信息映射，用于存储医生或药师的信息
     * @param config         配置对象，包含预参数配置
     * @param doctorPref     医生的偏好字符串
     * @param pharmacistPref 药师的偏好字符串
     */
    public void fillDoctorPharmacistInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String doctorPref, String pharmacistPref, String hospitalPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.DOCTOR_INFO)) {
            if (StringUtils.isNotBlank(doctorPref)) {
                aux.put(AuxConstant.DOCTOR_INFO, inquiryDoctorApi.getInquiryDoctorSupervision(doctorPref, hospitalPref));
            }
        }

        if (CollUtil.contains(config.getNodes(), AuxConstant.PHARMACIST_INFO)) {
            if (StringUtils.isNotBlank(pharmacistPref)) {
                aux.put(AuxConstant.PHARMACIST_INFO, inquiryPharmacistApi.getPharmacistByPref(pharmacistPref));
            }
        }

    }

    /**
     * 填充挂号信息
     *
     * @param aux         辅助数据Map，用于存储挂号信息
     * @param config      逻辑配置对象，包含需要的配置信息
     * @param bizTypeEnum 业务类型枚举，表示当前业务的类型
     * @param inquiryPref 问诊编号
     */
    public void fillMedicalRegistrationInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, BizTypeEnum bizTypeEnum, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.MEDICAL_REGISTRATION_INFO)) {
            aux.put(AuxConstant.MEDICAL_REGISTRATION_INFO, medicalRegistrationApi.getMedicalRegistrationInfo(bizTypeEnum, inquiryPref));
        }
    }

    /**
     * 填充处方详情信息
     *
     * @param aux              辅助数据Map，用于存储填充的处方详情信息
     * @param config           逻辑配置，包含需要填充的信息节点配置
     * @param prescriptionPref 处方编号
     */
    public void fillPrescriptionDetailInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String prescriptionPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.PRESCRIPTION_DETAIL)) {
            List<InquiryPrescriptionDetailRespDTO> prescriptionDetail = inquiryPrescriptionDetailApi.getPrescriptionDetail(prescriptionPref);

            // 填充转换标准库商品字段 eg:剂型
            if (CollUtil.contains(config.getNodes(), AuxConstant.STDLIB_PRODUCT)) {
                fillStdProductInfo(prescriptionDetail);
            }
            aux.put(AuxConstant.PRESCRIPTION_DETAIL, prescriptionDetail);
        }
    }

    /**
     * 填充标准库商品信息
     *
     * @param prescriptionDetail
     */
    private void fillStdProductInfo(List<InquiryPrescriptionDetailRespDTO> prescriptionDetail) {
        if (CollUtil.isEmpty(prescriptionDetail)) {
            return;
        }
        StdlibProductSearchDto searchDto = new StdlibProductSearchDto();
        searchDto.setMidStdlibIdList(prescriptionDetail.stream().map(s -> NumberUtil.parseLong(s.getStandardId(), 0L)).toList());
        Map<Long, ProductStdlibDto> stdlibDtoMap = productStdlibApi.searchProductStdlibList(searchDto, searchDto.getMidStdlibIdList().size()).stream()
            .collect(Collectors.toMap(ProductStdlibDto::getMidStdlibId, Function.identity(), (a, b) -> b));
        for (InquiryPrescriptionDetailRespDTO dto : prescriptionDetail) {
            if (stdlibDtoMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)) != null) {
                dto.setDosageForm(stdlibDtoMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)).getDosageForm());
            }
        }
    }

    /**
     * 填充临床病例信息
     *
     * @param aux         辅助信息映射，用于存储各种辅助数据
     * @param config      配置信息，包含是否需要填充临床病例信息的配置
     * @param inquiryPref 问诊编号
     */
    public void fillClinicalCaseInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.CLINICAL_CASE)) {
            InquiryClinicalCaseRespDto clinicalCase = inquiryClinicalCaseApi.getInquiryClinicalCase(inquiryPref);
            aux.put(AuxConstant.CLINICAL_CASE, TransmissionInternetConvert.INSTANCE.convert(clinicalCase));
        }
    }

    /**
     * 填充处方CA信息
     *
     * @param aux              辅助数据Map，用于存储填充的处方详情信息
     * @param config           逻辑配置，包含需要填充的信息节点配置
     * @param prescriptionPref 处方编号
     */
    public void fillPrescriptionCaInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String prescriptionPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.PRESCRIPTION_CA)) {
            // 获取pdf中电子签名信息
            Map<String, ElectSignInfoDto> electSignInfoDtoMap = inquirySignatureContractApi.getSignaturePlatformContractElectSignInfo(prescriptionPref, ContractTypeEnum.PRESCRIPTION);
            if (CollUtil.isEmpty(electSignInfoDtoMap) || CollUtil.size(electSignInfoDtoMap) < 2) {
                // throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "CA电子签名获取失败");
            }
            aux.put(AuxConstant.PRESCRIPTION_CA, electSignInfoDtoMap);
        }
    }

    /**
     * 填充操作人用户信息
     *
     * @param aux
     * @param config
     * @param userId
     */
    public void fillOperateUserInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, Long userId) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.OPERATE_USER_INFO) && userId != null) {
            aux.put(AuxConstant.OPERATE_USER_INFO, adminUserApi.getUser(userId));
        }
    }

    /**
     * 填充监护人信息
     *
     * @param aux
     * @param config
     * @param inquiryPref
     */
    protected void fillInquiryDetailInfo(Map<String, Object> aux, PostParameterConfig config, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), AuxConstant.INQUIRY_DETAIL_INFO)) {
            aux.put(AuxConstant.INQUIRY_DETAIL_INFO, inquiryApi.getInquiryRecordDetail(inquiryPref));
        }
        InquiryRecordDetailDto detailDto = new InquiryRecordDetailDto();
        StringUtils.join(detailDto.getAllergic());
    }

    public static void main(String[] args) {
        String aa = null;
        System.out.println(StringUtils.substring(aa, 6, 14));
    }

    /**
     * 填充处方相关参数
     */
    protected void fillPrescriptionParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config, PrescriptionTransmitterDTO data) {
        // 0.填充CA信息
        fillPrescriptionCaInfo(transmissionReqDTO.getAux(), config, data.getPref());

        // 1. 就诊挂号记录(就诊流水号)
        fillMedicalRegistrationInfo(transmissionReqDTO.getAux(), config, BizTypeEnum.HYWZ, data.getInquiryPref());

        // 2. 门诊病例 (过敏、证候...)
        fillClinicalCaseInfo(transmissionReqDTO.getAux(), config, data.getInquiryPref());

        // 3. 医生药师编码职称
        fillDoctorPharmacistInfo(transmissionReqDTO.getAux(), config, data.getDoctorPref(), data.getPharmacistPref(), data.getHospitalPref());

        // 4. 处方明细
        fillPrescriptionDetailInfo(transmissionReqDTO.getAux(), config, data.getPref());

        // 5.问诊单详情(监护人等)
        fillInquiryDetailInfo(transmissionReqDTO.getAux(), config, data.getInquiryPref());

    }


}