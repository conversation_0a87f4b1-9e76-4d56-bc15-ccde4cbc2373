package com.xyy.saas.transmitter.server.service.dict;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROVIDER_DICT_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.DictTypeApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictTypeRespDTO;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictExcelVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.transmitter.server.convert.dict.TransmitterDictConvert;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.transmitter.server.dal.mysql.dict.TransmissionOrganDictMapper;
import com.xyy.saas.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 服务商字典 Service 实现类 transmission_organ_dict
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransmissionOrganDictServiceImpl implements TransmissionOrganDictService {

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private DictTypeApi dictTypeApi;

    @Resource
    private TransmissionOrganDictMapper transmissionOrganDictMapper;

    @Resource
    private TransmissionOrganMapper organMapper;

    @Override
    public Long createTransmissionProviderDict(TransmissionOrganDictSaveReqVO createReqVO) {
        DictTypeRespDTO dictType = dictTypeApi.getDictType(createReqVO.getDictType());
        if (dictType == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
        TransmissionOrganDictDO transmissionProviderDict = BeanUtils.toBean(createReqVO, TransmissionOrganDictDO.class);
        transmissionProviderDict.setDictName(dictType.getName());
        transmissionOrganDictMapper.insert(transmissionProviderDict);
        return transmissionProviderDict.getId();
    }

    @Override
    public void updateTransmissionProviderDict(TransmissionOrganDictSaveReqVO updateReqVO) {
        // 校验存在
        validateTransmissionProviderDictExists(updateReqVO.getId());
        DictTypeRespDTO dictType = dictTypeApi.getDictType(updateReqVO.getDictType());
        if (dictType == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
        TransmissionOrganDictDO updateObj = BeanUtils.toBean(updateReqVO, TransmissionOrganDictDO.class);
        updateObj.setDictName(dictType.getName());
        transmissionOrganDictMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransmissionProviderDict(Long id) {
        // 校验存在
        validateTransmissionProviderDictExists(id);
        // 删除
        transmissionOrganDictMapper.deleteById(id);
    }

    private void validateTransmissionProviderDictExists(Long id) {
        if (transmissionOrganDictMapper.selectById(id) == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
    }

    @Override
    public TransmissionOrganDictDO getTransmissionProviderDict(Long id) {
        return transmissionOrganDictMapper.selectById(id);
    }

    @Override
    public PageResult<TransmissionOrganDictRespVO> getTransmissionProviderDictPage(TransmissionOrganDictPageReqVO pageReqVO) {
        PageResult<TransmissionOrganDictDO> pageResult = transmissionOrganDictMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(null, pageResult.getTotal());
        }
        //组装名称
        Set<Integer> organIds = CollectionUtils.convertSet(pageResult.getList(), TransmissionOrganDictDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
            .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));
        List<TransmissionOrganDictRespVO> list = pageResult.getList().stream().map(d -> {
            TransmissionOrganDictRespVO vo = TransmitterDictConvert.INSTANCE.convertVo(d);
            vo.setOrganName(organDOMap.getOrDefault(d.getOrganId(), new TransmissionOrganDO()).getName());
            return vo;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDto importDictList(List<TransmissionOrganDictExcelVO> list, Boolean updateSupport, Integer organId, String dictValue) {
        if (CollUtil.isEmpty(list)) {
            throw new RuntimeException("导入数据不能为空");
        }
        // 校验服务商

        // 校验字典类型
        DictDataRespDTO dictData = dictDataApi.getDictData(null, DictTypeConstants.TRANSMISSION_ORGAN_DICT, dictValue);
        if (dictData == null) {
            throw new RuntimeException("导入数据字典不存在");
        }
        // 校验-转换saveVO
        List<TransmissionOrganDictSaveReqVO> dictSaveReqVOS = checkAndGetImportList(list, organId, dictData);
        if (CollUtil.isEmpty(dictSaveReqVOS)) {
            return ImportResultDto.builder().totalCount((long) list.size()).failureCount((long) list.size()).fileName(dictData.getLabel() + "失败数据.xls").fileUrl("").build();
        }
        // 去重
        List<TransmissionOrganDictSaveReqVO> saveReqVOS = dictSaveReqVOS.stream().distinct().toList();
        TransmissionOrganDictPageReqVO reqVO = TransmissionOrganDictPageReqVO.builder().organId(organId).dictType(dictValue).build();

        for (List<TransmissionOrganDictSaveReqVO> reqVOS : Lists.partition(saveReqVOS, 500)) {
            reqVO.setValues(CollectionUtils.convertList(reqVOS, TransmissionOrganDictSaveReqVO::getValue));
            Map<String, TransmissionOrganDictDO> existsDictMap = transmissionOrganDictMapper.selectByCondition(reqVO).stream()
                .collect(Collectors.toMap(TransmissionOrganDictDO::getValue, Function.identity(), (a, b) -> b));
            // 修改
            List<TransmissionOrganDictDO> updateList = reqVOS.stream().filter(r -> existsDictMap.containsKey(r.getValue()))
                .map(r -> TransmitterDictConvert.INSTANCE.convertDo(r).setId(existsDictMap.get(r.getValue()).getId())).toList();
            if (BooleanUtil.isTrue(updateSupport) && CollUtil.isNotEmpty(updateList)) {
                transmissionOrganDictMapper.updateBatch(updateList);
            }
            // 新增
            List<TransmissionOrganDictDO> createList = reqVOS.stream().filter(r -> !existsDictMap.containsKey(r.getValue()))
                .map(TransmitterDictConvert.INSTANCE::convertDo).toList();
            if (CollUtil.isNotEmpty(createList)) {
                transmissionOrganDictMapper.insertBatch(createList);
            }
        }

        List<TransmissionOrganDictExcelVO> failList = list.stream().filter(d -> StringUtils.isNotBlank(d.getErrMsg())).toList();
        if (CollUtil.isNotEmpty(failList)) {
            // 上传文件
        }
        return ImportResultDto.builder().totalCount((long) list.size()).successCount((long) (list.size() - failList.size())).failureCount((long) failList.size()).fileName(dictData.getLabel() + "失败数据.xls").fileUrl("").build();
    }


    private List<TransmissionOrganDictSaveReqVO> checkAndGetImportList(List<TransmissionOrganDictExcelVO> list, Integer organId, DictDataRespDTO dictData) {
        List<TransmissionOrganDictSaveReqVO> realList = new ArrayList<>();
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        for (TransmissionOrganDictExcelVO excelVO : list) {
            TransmissionOrganDictSaveReqVO saveReqVO = TransmitterDictConvert.INSTANCE.convertExcelVO(excelVO, organId, dictData);
            Set<ConstraintViolation<TransmissionOrganDictSaveReqVO>> validate = validator.validate(saveReqVO);
            String errMsg = Optional.ofNullable(validate).orElse(new HashSet<>()).stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(errMsg)) {
                excelVO.setErrMsg(errMsg);
                continue;
            }
            realList.add(saveReqVO);
        }
        return realList;
    }
}