package com.xyy.saas.inquiry.product.server;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.user.server.InquiryUserServerApplication;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * desc  启动web环境，测试controller层（事务不回滚）
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@ActiveProfiles("test")
@SpringBootTest(classes = InquiryUserServerApplication.class, webEnvironment = WebEnvironment.DEFINED_PORT, properties = {
    "spring.config.location=classpath:/application-test.yml,classpath:/application.yml" // 同时加载两个配置文件
})
//@MapperScan(basePackages = {"cn.iocoder.yudao.module", "com.xyy.saas.inquiry"})
@Transactional(rollbackFor = Throwable.class)
@AutoConfigureMockMvc
public abstract class WebMvcIntegrationTest {

    protected static final Logger log = LoggerFactory.getLogger(WebMvcIntegrationTest.class);

    @Resource
    protected MockMvc mockMvc;

    @Resource
    protected ObjectMapper objectMapper;
}
