spring:
  cloud:
    nacos:
      server-addr: 172.20.7.26:8848
      discovery:
        namespace: ${spring.profiles.active}
        group: http
      config:
        namespace: ${spring.profiles.active}
        group: DEFAULT_GROUP
        server-addr: 172.20.7.26:8848
        prefix: ${spring.application.name}
        file-extension: yaml
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml
      - optional:nacos:inquiry-global-${spring.profiles.active}.yaml

  datasource:
    dynamic:
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************* # MODE 使用 MySQL 模式；DATABASE_TO_UPPER  配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************************* # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
  data:
    redis:
      host: db01-medicare-test.redis.ybm100.top # 地址
      port: 30002 # 端口
      password: JEf8CVrnY0G3RPEZ # 密码，建议生产环境开启
      database: 15


rocketmq:
  name-server: **********:9876
  #  topic:
  #    inquiry-topic: xyy_saas_inquiry_topic_test
  producer:
    group: saas_inquiry_system

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

external:
  rocketmq:
    middle:
      nameServer: mq1-me-test.rocketmq.ybm100.top:9876;mq2-me-test.rocketmq.ybm100.top:9876;mq3-me-test.rocketmq.ybm100.top:9876;mq4-me-test.rocketmq.ybm100.top:9876
    saas:
      nameServer: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876

#event:
#  bus:
#    append-profile: true

yudao:
  swagger:
    http-domain: http://127.0.0.1:48080

logging:
  level:
    com.baomidou.mybatisplus: debug