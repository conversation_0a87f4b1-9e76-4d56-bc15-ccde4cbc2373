package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantServicePackRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.service.tenant.servicepack.TenantTransmissionServicePackRelationService;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/07 11:52
 */
@Service
public class TenantServicePackRelationApiImpl implements TenantServicePackRelationApi {

    @Resource
    private TenantTransmissionServicePackRelationService tenantTransmissionServicePackRelationService;

    @Override
    public Map<Integer, Long> selectCountByOrgans(List<Integer> organIds, Integer status) {
        return tenantTransmissionServicePackRelationService.selectCountByOrgans(organIds, status);
    }

    @Override
    public Map<Integer, Long> selectCountByServicePacks(List<Integer> servicePackIds, Integer status) {
        return tenantTransmissionServicePackRelationService.selectCountByServicePacks(servicePackIds, status);
    }

    @Override
    public Map<Long, Long> selectCountByCatalogIds(List<Long> catalogIds, Integer status) {
        return tenantTransmissionServicePackRelationService.selectCountByCatalogIds(catalogIds, status);
    }

    @Override
    public List<TenantServicePackRelationDto> selectByCondition(TenantServicePackRelationReqDto reqDto) {
        List<TenantTransmissionServicePackRelationDO> relationDOS = tenantTransmissionServicePackRelationService.selectByCondition(TenantServicePackRelationConvert.INSTANCE.convertReqVo(reqDto));
        return TenantServicePackRelationConvert.INSTANCE.convertDto(relationDOS);
    }


    /**
     * 获取门店机构目录id - 为空也缓存10分钟
     *
     * @param organTypeEnum 机构医疗类型
     * @return
     */
    @Override
    @Cacheable(value = RedisKeyConstants.SYSTEM_SERVICE_PACK_RELATION_KEY + "#1m", key = "'catalog'+T(cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder).getTenantId()"
        , unless = "#result == null")
    public Long getTenantOrganCatalogId(OrganTypeEnum organTypeEnum) {
        TenantServicePackRelationPageReqVO reqVO = TenantServicePackRelationPageReqVO.builder()
            .tenantId(TenantContextHolder.getTenantId())
            .organType(organTypeEnum.getCode())
            .status(TenantServicePackRelationStatusEnum.OPEN.getCode()).build();

        TenantTransmissionServicePackRelationDO servicePackRelationDO = tenantTransmissionServicePackRelationService.selectTenantServicePack(reqVO);
        return servicePackRelationDO == null ? null : servicePackRelationDO.getCatalogId();
    }

    /**
     * 分页查询租户
     *
     * @param reqDto
     * @return
     */
    @Override
    public PageResult<CatalogRelationTenantDto> getCatalogRelationTenantPage(TenantServicePackRelationReqDto reqDto) {

        return tenantTransmissionServicePackRelationService.getCatalogRelationTenantPage(reqDto);
    }
}
