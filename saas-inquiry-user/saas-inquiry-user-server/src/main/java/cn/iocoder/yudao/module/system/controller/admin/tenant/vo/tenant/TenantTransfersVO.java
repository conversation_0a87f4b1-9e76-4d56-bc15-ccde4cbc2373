package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.framework.operatelog.core.CommonStatusParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.TenantParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.TenantTypeParseFunction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xyy.saas.inquiry.constant.SystemConstant;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import java.util.List;

@Schema(description = "管理后台 - 门店转让 VO")
@Data
@Accessors(chain = true)
public class TenantTransfersVO {

    private Long tenantId;

    @Schema(description = "转让员工ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14411")
    private Long userId;

    @Schema(description = "手机号码", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @NotEmpty(message = "手机号码不能为空")
    private String mobile;

    @Schema(description = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "默认验证码:1111")
    // @NotEmpty(message = "验证码不能为空")
    private String code;

}
