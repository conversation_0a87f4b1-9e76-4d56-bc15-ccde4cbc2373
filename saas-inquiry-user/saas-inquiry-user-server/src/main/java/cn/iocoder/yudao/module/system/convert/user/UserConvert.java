package cn.iocoder.yudao.module.system.convert.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.collection.MapUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import cn.iocoder.yudao.module.system.api.user.dto.SSOUser;
import cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.dept.vo.post.PostSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoDto;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    default List<UserRespVO> convertList(List<UserRespVO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> convert(user, deptMap.get(user.getDeptId())));
    }

    default UserRespVO convert(UserRespVO user, DeptDO dept) {
        if (dept != null) {
            user.setDeptName(dept.getName());
        }
        return user;
    }

    default List<UserSimpleRespVO> convertSimpleList(List<AdminUserDO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> {
            UserSimpleRespVO userVO = BeanUtils.toBean(user, UserSimpleRespVO.class);
            MapUtils.findAndThen(deptMap, user.getDeptId(), dept -> userVO.setDeptName(dept.getName()));
            return userVO;
        });
    }

    default UserProfileRespVO convert(AdminUserDO user, List<RoleDO> userRoles,
        DeptDO dept, List<PostDO> posts, List<SocialUserDO> socialUsers) {
        UserProfileRespVO userVO = BeanUtils.toBean(user, UserProfileRespVO.class);
        userVO.setRoles(BeanUtils.toBean(userRoles, RoleSimpleRespVO.class));
        userVO.setDept(BeanUtils.toBean(dept, DeptSimpleRespVO.class));
        userVO.setPosts(BeanUtils.toBean(posts, PostSimpleRespVO.class));
        userVO.setSocialUsers(BeanUtils.toBean(socialUsers, UserProfileRespVO.SocialUser.class));
        return userVO;
    }

    @Mappings({
        @Mapping(source = "account", target = "username"),
    })
    UserSaveReqVO convertSsoUser(SSOUser ssoUser);

    default void convertSsoUser2Do(SSOUser ssoUser, AdminUserDO adminUserDO) {
        adminUserDO.setMobile(ssoUser.getMobile());
        adminUserDO.setEmail(ssoUser.getEmail());
        adminUserDO.setPassword(null); // 密码制空
    }

    @Mappings({
        @Mapping(source = "sex", target = "sex", defaultValue = "1"),
    })
    AdminUserDO convertVo2Do(UserSaveReqVO createReqVO);

    AdminUserRespDTO convertDto(AdminUserDO user);

    default AdminUserRespDTO convertDto(AdminUserDO user, List<RoleDO> userRoles) {
        AdminUserRespDTO respDTO = convertDto(user);
        if (CollUtil.isNotEmpty(userRoles)) {
            respDTO.setRoleCodes(CollectionUtils.convertList(userRoles, RoleDO::getCode));
            respDTO.setRoleIds(CollectionUtils.convertList(userRoles, RoleDO::getId));
            respDTO.setRoleNames(CollectionUtils.convertList(userRoles, RoleDO::getName));
        }
        return respDTO;
    }

    AdminUserRespDTO convertMember2Dto(MemberUserRespDTO user);

    UserSaveReqVO convertSaveOrUpdateVo(AdminUserSaveDTO userSaveDTO);

    UserRespVO convertUserVo(AdminUserDO user);

    default UserRespVO convertUserVoWithTenantList(AdminUserDO userDO, List<TenantUserRelationDO> relationDOList) {
        UserRespVO user = convertUserVo(userDO);
        user.setOnlyDefaultTenant(!org.springframework.util.CollectionUtils.isEmpty(relationDOList) && relationDOList.stream().allMatch(relationDO -> ObjectUtil.equals(relationDO.getTenantId(), TenantConstant.DEFAULT_TENANT_ID)));
        return user;
    }

    @Mapping(target = "userId", source = "id")
    UserBaseInfoDto convertChangeDto(AdminUserDO userDO);

    default UserInfoVO convertUserInfo(AdminUserRespDTO tenantUser, TenantDO tenantDO, List<RoleDO> roleDOS) {
        UserInfoVO userInfoVO = convertUserDto2Vo(tenantUser);
        userInfoVO.setTenant(TenantConvert.INSTANCE.convertDto2Vo(tenantDO));
        if (CollUtil.isNotEmpty(roleDOS)) {
            userInfoVO.setRoleCodes(CollectionUtils.convertSet(roleDOS, RoleDO::getCode));
            userInfoVO.setRoleIds(CollectionUtils.convertSet(roleDOS, RoleDO::getId));
            userInfoVO.setRoleNames(CollectionUtils.convertSet(roleDOS, RoleDO::getName));
        }
        return userInfoVO;
    }

    UserInfoVO convertUserDto2Vo(AdminUserRespDTO tenantUser);

    UserBaseInfoDto convertChangeDto(TenantUserRelationDO tenantUserRelation);

    UserSaveReqVO convertSaveDTO(AdminUserSaveDTO userSaveDTO);
}
