package cn.iocoder.yudao.module.system.api.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantBizRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantHeadService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 多门店的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantHeadService tenantHeadService;

    @Resource
    private TenantBizRelationService tenantBizRelationService;

//    @Override
//    public List<Long> getTenantIdList() {
//        return tenantService.getTenantIdList();
//    }

    @Override
    public TenantDto getTenant() {
        TenantDO tenantDO = tenantService.getRequiredTenant(TenantContextHolder.getRequiredTenantId());
        return TenantConvert.INSTANCE.convertDto(tenantDO);
    }

    @Override
    public TenantDto getTenant(Long tenantId) {
        TenantDO tenantDO = tenantService.getRequiredTenant(tenantId);
        return TenantConvert.INSTANCE.convertDto(tenantDO);
    }

    /**
     * 获取门店信息对外暴露
     *
     * @param tenantId
     * @return 门店DTO
     */
    @Override
    public List<TenantDto> getTenantList(List<Long> tenantId) {
        List<TenantDO> tenantDOList = tenantService.getTenantList(tenantId);
        return TenantConvert.INSTANCE.convertDtos(tenantDOList);
    }


    @Override
    public List<TenantDto> getTenantList(String nameOrPref) {
        List<TenantDO> tenantDOList = tenantService.getTenantInfo(nameOrPref);
        return TenantConvert.INSTANCE.convertDtos(tenantDOList);
    }

    @Override
    public PageResult<TenantRespDto> pageTenant(TenantReqDto tenantReqDto) {
        PageResult<TenantRespVO> pageResult = tenantService.getTenantPage(TenantConvert.INSTANCE.convert(tenantReqDto));
        return new PageResult<>(TenantConvert.INSTANCE.convertRespDtos(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public void validateTenant(Long id) {
        tenantService.validTenant(id);
    }

    @Override
    public List<Long> getTenantIdsByHeadId() {
        return tenantHeadService.getTenantIdsByHeadId();
    }

    @Override
    public List<TenantDto> getTenantListByNames(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return List.of();
        }
        List<TenantDO> tenantDOList = tenantService.getTenantListByNames(names);
        return TenantConvert.INSTANCE.convertDtos(tenantDOList);
    }

    @Override
    public void updateTenantStatus(Long id, Integer status) {
        if (id == null) {
            return;
        }
        tenantService.updateTenantStatus(id, status);
    }
}
