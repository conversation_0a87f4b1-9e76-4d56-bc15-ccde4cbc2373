package cn.iocoder.yudao.module.member.controller.app.auth;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.service.auth.MemberAuthService;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginTenantReqVO;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户 APP - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AppAuthController {

    @Resource
    private MemberAuthService authService;

    @Resource
    private AdminAuthService adminAuthService;

    @PostMapping("/weixin-mini-app-login")
    @PermitAll
    @Operation(summary = "微信小程序的一键登录")
    public CommonResult<AuthLoginRespVO> weixinMiniAppLogin(@RequestBody @Valid AppAuthWeixinMiniAppLoginReqVO reqVO) {
        AppAuthLoginRespVO loginRespVO = authService.weixinMiniAppLogin(reqVO);
        return success(adminAuthService.loginWithTenantId(AuthLoginTenantReqVO.builder()
            .token(loginRespVO.getAccessToken())
            .userId(loginRespVO.getUserId())
            .tenantId(reqVO.getTenantId())
            .clientChannelType(ClientChannelTypeEnum.MINI_PROGRAM.getCode())
            .build()));
    }

}
