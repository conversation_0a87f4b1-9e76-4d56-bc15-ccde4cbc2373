package cn.iocoder.yudao.module.system.api.tenant.migration;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.migration.dto.MigrationTrailPackageDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageRelationConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantUserRelationConvert;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantUserRelationMapper;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.tenant.TenantCertificateService;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/06 16:33
 */
@Service
public class TenantMigrationApiImpl implements TenantMigrationApi {


    @Resource
    private TenantPackageRelationService tenantPackageRelationService;

    @Resource
    private TenantPackageRelationMapper tenantPackageRelationMapper;

    @Resource
    private TenantUserRelationMapper tenantUserRelationMapper;

    @Resource
    private TenantUserRelationService tenantUserRelationService;

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantCertificateService tenantCertificateService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    @Lazy
    private AdminAuthService adminAuthService;

    @Override
    public Long migrationTenant(MigrationDrugStoreRespDto dto) {

        TenantSaveReqVO saveReqVO = TenantConvert.INSTANCE.convertMigrationSaveVo(dto);

        if (dto.getTenantId() != null) {
            TenantDO tenant = tenantService.getTenant(dto.getTenantId());
            CopyOptions options = CopyOptions.create()
                .setIgnoreNullValue(true) // 忽略源对象的 null 值
                .setOverride(false);      // 不覆盖目标对象已有值（即仅当目标字段为 null 时才设置）
            BeanUtil.copyProperties(TenantConvert.INSTANCE.convertDo2SaveVo(tenant), saveReqVO, options);
            // saveReqVO.setId(tenant.getId());
            // saveReqVO.setHeadTenantId(tenant.getHeadTenantId());

            tenantCertificateService.deleteTenantCertificateByTenantId(dto.getTenantId());
            TenantDO tenantDO = tenantService.updateTenant(saveReqVO);
            return tenantDO.getId();
        }
        saveReqVO.setPassword(StringUtils.substring(dto.getContactPhone(), 5, 12));
        saveReqVO.setImport(true);
        return tenantService.createTenant(saveReqVO);
    }

    @Override
    public Long migrationEmployee(AdminUserSaveDTO userSaveDTO) {

        UserSaveReqVO userSaveReqVO = UserConvert.INSTANCE.convertSaveDTO(userSaveDTO);

        return adminUserService.createUserStore(userSaveReqVO);
    }

    @Override
    public void bindTenant(Long userId) {

        TenantUserRelationDO bindTenantRelationDo = tenantUserRelationService.getUserBindTenantRelationDo(userId);

        tenantUserRelationService.createTenantUserRelation(TenantUserRelationConvert.INSTANCE.convertDto(bindTenantRelationDo));
    }

    @Override
    public void unBindEmployee(Long tenantId) {

        TenantDO tenant = tenantService.getTenant(tenantId);
        Long adminUserId = tenant.getContactUserId();

        List<TenantUserRelationDO> userRelationDOS = tenantUserRelationMapper.selectByTenantsId(tenantId).stream()
            .filter(tenantUserRelationDO -> !Objects.equals(tenantUserRelationDO.getUserId(), adminUserId)).toList();

        if (CollUtil.isEmpty(userRelationDOS)) {
            return;
        }
        // 1.删除绑定关系
        TenantUtils.executeIgnore(() -> tenantUserRelationMapper.deleteByIds(userRelationDOS.stream().map(TenantUserRelationDO::getId).collect(Collectors.toList())));

        for (TenantUserRelationDO relationDO : userRelationDOS) {
            // 2.解除角色
            permissionService.deleteUserRoleBatchTenant(relationDO.getUserId(), Collections.singletonList(relationDO.getTenantId()), null);

            //3.退出登录
            adminAuthService.logout(relationDO.getUserId(), Collections.singletonList(relationDO.getTenantId()), LoginLogTypeEnum.LOGOUT_DELETE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTrailPackage(MigrationTrailPackageDto trailPackageDto) {

        TenantPackageRelationSaveReqVO relationSaveReqVO = TenantPackageRelationConvert.INSTANCE.convertMigrationTrailPackageDto(trailPackageDto);

        return tenantPackageRelationService.createTenantPackageRelation(relationSaveReqVO);
    }

    @Override
    public List<Long> migrationPackage(List<MigrationPackageRespDto> saveDtos) {
        if (CollUtil.isEmpty(saveDtos)) {
            return List.of();
        }

        List<TenantPackageRelationSaveReqVO> saveReqVOList = TenantPackageRelationConvert.INSTANCE.convertMigrationPackages(saveDtos);

        return tenantPackageRelationService.migrationTenantPackageRelation(saveReqVOList);
    }

    @Override
    public void deleteMigrationPackage(List<Long> packageIds) {
        if (CollUtil.isNotEmpty(packageIds)) {
            tenantPackageRelationMapper.deleteByIds(packageIds);
        }
    }
}
