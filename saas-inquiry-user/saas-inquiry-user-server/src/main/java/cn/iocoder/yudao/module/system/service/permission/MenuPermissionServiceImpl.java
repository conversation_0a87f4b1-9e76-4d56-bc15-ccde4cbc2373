package cn.iocoder.yudao.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVo;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.enums.permission.MenuTypeEnum;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.ClientTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/09 17:47
 */
@Service
@Slf4j
public class MenuPermissionServiceImpl implements MenuPermissionService {

    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private TenantService tenantService;

    @Override
    public AuthPermissionInfoRespVo getMenuPermissionInfo(ClientTypeEnum client) {
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUserStore(getLoginUserId());
        if (user == null) {
            return null;
        }
        TenantRespVO tenantVo = tenantService.getTenantVo(TenantContextHolder.getRequiredTenantId());
        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollUtil.isEmpty(roleIds)) {
            return AuthConvert.INSTANCE.convert(user, tenantVo, Collections.emptyList(), Collections.emptyList());
        }
        List<RoleDO> roles = TenantUtils.executeIgnore(() -> roleService.getRoleList(roleIds));
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList = menuService.filterDisableMenus(menuList);
        if(!TenantConstant.isSystemTenant()) {
            // 过滤业务线菜单
            menuList = menuService.filterBizMenus(menuList, tenantVo);
        }
        // 过滤各端菜单
        menuList = menuService.filterClientMenus(menuList, MenuTypeEnum.clientMenuTypes(client));

        return AuthConvert.INSTANCE.convert(user, tenantVo, roles, menuList);
    }

    @Override
    public List<MenuDO> getAdminMenuPermissionInfo() {
        TenantRespVO tenantVo = tenantService.getTenantVo(TenantContextHolder.getRequiredTenantId());
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUserStore(tenantVo.getContactUserId());
        if (user == null) {
            return null;
        }
        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(tenantVo.getContactUserId());
        if (CollUtil.isEmpty(roleIds)) {
            return List.of();
        }
        List<RoleDO> roles = TenantUtils.executeIgnore(() -> roleService.getRoleList(roleIds));
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList = menuService.filterDisableMenus(menuList);
        if(!TenantConstant.isSystemTenant()) {
            // 过滤业务线菜单
            menuList = menuService.filterBizMenus(menuList, tenantVo);
        }

        menuList.sort(Comparator.comparing(MenuDO::getSort));
        return menuList;
    }
}
