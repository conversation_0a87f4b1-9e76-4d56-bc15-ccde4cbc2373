package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.service.tenant.handler.TenantInfoHandler;
import cn.iocoder.yudao.module.system.service.tenant.handler.TenantMenuHandler;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantService {

    /**
     * 创建门店
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenant(TenantSaveReqVO createReqVO);

    /**
     * 更新门店
     *
     * @param updateReqVO 更新信息
     */
    TenantDO updateTenant(TenantSaveReqVO updateReqVO);

    /**
     * 更新门店的角色菜单
     *
     * @param tenantId 门店编号
     * @param menuIds  菜单编号数组
     */
    void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds);

    /**
     * 删除门店
     *
     * @param id 编号
     */
    void deleteTenant(Long id);

    /**
     * 获得门店
     *
     * @param id 编号
     * @return 门店
     */
    TenantDO getTenant(Long id);

    /**
     * 获取门店信息
     *
     * @param tenantId
     * @return
     */
    TenantDO getRequiredTenant(Long tenantId);

    /**
     * 获得门店分页
     *
     * @param pageReqVO 分页查询
     * @return 门店分页
     */
    PageResult<TenantRespVO> getTenantPage(TenantPageReqVO pageReqVO);

    /**
     * 获得名字对应的门店
     *
     * @param name 门店名
     * @return 门店
     */
    TenantDO getTenantByName(String name);

    /**
     * 获取管理的门店
     *
     * @param userId 管理员id
     * @return 门店列表
     */
    List<TenantDO> getTenantByAdminUserId(Long userId);

    /**
     * 进行门店的信息处理逻辑 其中，门店编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantInfo(TenantInfoHandler handler);

    /**
     * 进行门店的菜单处理逻辑 其中，门店编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantMenu(TenantMenuHandler handler);

    /**
     * 获得所有门店
     *
     * @return 门店编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验门店是否合法
     *
     * @param id 门店编号
     */
    TenantDO validTenant(Long id);

    /**
     * 根据userId获取 当前用户的门店列表
     *
     * @param userId 登录userId
     * @return
     */
    List<TenantSimpleRespVO> getTenantListByUserId(@NotNull Long userId);

    /**
     * 获得门店展示信息
     *
     * @param id 编号
     * @return 门店
     */
    TenantRespVO getTenantVo(Long id);

    /**
     * 获取门店列表
     *
     * @param tenantIds 门店ids
     * @return 门店列表
     */
    List<TenantDO> getTenantList(List<Long> tenantIds);

    default Map<Long, TenantDO> getTenantListMap(List<Long> tenantIds) {
        return getTenantList(tenantIds).stream().collect(Collectors.toMap(TenantDO::getId, Function.identity()));
    }

    /**
     * 校验是否门店管理员 门店管理员不可操作
     *
     * @param userId 用户id
     */
    void validTenantAdminUserId(Long userId);

    /**
     * 获取门店信息
     *
     * @param tenantName 门店名称或者编号
     * @return
     */
    List<TenantDO> getTenantInfo(String nameOrPref);

    /**
     * 根据门店名称或者营业执照名称查询 门店列表
     *
     * @param name
     * @return
     */
    List<TenantDO> getTenantListByNames(List<String> names);

    /**
     * 禁用门店
     *
     * @param id
     */
    void updateTenantStatus(Long id, Integer status);

    /**
     * 获取业务角色
     *
     * @param tenantType
     * @param bizType
     * @param shareType
     * @param roleIds
     */
    void getBizRoles(Integer tenantType, Integer bizType, Integer shareType, Set<Long> roleIds);

}
