package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门店 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant", autoResultMap = true)
@KeySequence("system_tenant_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TenantDO extends BaseDO {

//    /**
//     * 套餐编号 - 系统
//     */
//    public static final Long PACKAGE_ID_SYSTEM = 0L;

    /**
     * 门店编号
     */
    @TableId
    private Long id;

    /**
     * 编码
     */
    private String pref;

    /**
     * 门店名
     */
    private String name;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    private Integer type;
    /**
     * 租户编号（总部）
     */
    private Long headTenantId;

    /**
     * 联系人的用户编号
     * <p>
     * 关联 {@link AdminUserDO#getId()}
     */
    private Long contactUserId;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系手机
     */
    private String contactMobile;
    /**
     * 营业执照名称
     */
    private String businessLicenseName;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;

    /**
     * 问诊系统业务类型开通
     */
    private Integer wzBizTypeStatus;
    /**
     * 账号数量
     */
    private Integer wzAccountCount;
//    /**
//     * 门店状态（0正常 1停用）
//     * 枚举 {@link CommonStatusEnum}
//     */
//    private Integer wzStatus;
    /**
     * 智慧脸业务线类型开通
     */
    private Integer zhlBizTypeStatus;
    /**
     * 智慧脸账号数量
     */
    private Integer zhlAccountCount;
//    /**
//     * 智慧脸系统状态(0正常 1停用)
//     * 枚举 {@link CommonStatusEnum}
//     */
//    private Integer zhlStatus;
    /**
     * 门店状态 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;

    /**
     * 环境标志：prod-真实数据；test-测试数据；show-线上演示数据
     */
    private String envTag;

    /**
     * 智慧脸药店标识
     */
    private String smartfaceOrganSign;

    /**
     * 扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private TenantExtDto ext;

    /**
     * 业务类型
     */
    @TableField(exist = false)
    private Integer bizType;

    /**
     * 业务类型
     */
    @TableField(exist = false)
    private String bizTypes;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    @TableField(exist = false)
    private Integer bizTenantType;

    /**
     * 问诊租户类型（1-单店 2连锁门店 3连锁总部）
     */
    @TableField(exist = false)
    private Integer wzTenantType;

    /**
     * 租户编号(总部)
     */
    @TableField(exist = false)
    private Long bizHeadTenantId;

    /**
     * 业务线信息
     */
    @TableField(exist = false)
    private List<TenantBizRelationSaveReqVO> bizRelations;
}
