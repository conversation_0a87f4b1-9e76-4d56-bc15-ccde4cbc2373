package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 用户分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserPageReqVO extends PageParam {

    @Schema(description = "用户名，模糊匹配", example = "yudao")
    private String nickname;

    @Schema(description = "手机号码，模糊匹配", example = "yudao")
    private String mobile;

    @Schema(description = "登录账号，模糊匹配", example = "yudao")
    private String username;

    @Schema(description = "账号状态", example = "1")
    private Integer userAccountStatus;

    @Schema(description = "员工状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "部门编号，同时筛选子部门", example = "1024")
    private Long deptId;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 角色ids
     */
    private List<Long> roleIds;

    /**
     * 角色code
     */
    private String roleCode;

//    public Long getTenantId() {
//        if (TenantConstant.isDefaultTenant()) {
//            return tenantId;
//        }
//        return TenantContextHolder.getTenantId();
//    }
}
