package cn.iocoder.yudao.module.system.api.user;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.security.config.SecurityProperties;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2TokenService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * @Author: xucao
 * @DateTime: 2025/5/8 13:30
 * @Description: 用户api  兼容社交用户登录
 **/
@Service
public class UserCompatApiImpl implements UserCompatApi {

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private OAuth2TokenService oauth2TokenService;

    /**
     * 获取登录用户的信息
     *
     * @return 用户对象信息
     */
    @Override
    public AdminUserRespDTO getUser(String token) {
        // 查询token信息
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.getAccessToken(token);
        // 根据用户类型，获取用户信息
        if (ObjectUtil.equals(accessTokenDO.getUserType(), UserTypeEnum.ADMIN.getValue())) {
            return adminUserApi.getUser();
        }
        return getUserForMember(accessTokenDO.getUserId());
    }

    private AdminUserRespDTO getUserForMember(Long userId) {
        return UserConvert.INSTANCE.convertMember2Dto(memberUserApi.getUser(userId));
    }
}
