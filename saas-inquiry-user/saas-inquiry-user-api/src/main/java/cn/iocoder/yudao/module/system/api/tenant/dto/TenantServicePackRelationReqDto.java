package cn.iocoder.yudao.module.system.api.tenant.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店-开通服务包关系 Dto
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantServicePackRelationReqDto extends PageParam {

    /**
     * 门店id
     */
    private Long tenantId;

    private List<Long> tenantIds;

    /**
     * 服务包ID
     */
    private Integer servicePackId;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 服务机构类型（1-医保、2-药监、3-互联网医院监管、99-其他）
     */
    private Integer organType;
    /**
     * 版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122）
     */
    private Long servicePackVersion;

    /**
     * 状态 0未开通 1开通 {@link TenantServicePackRelationStatusEnum}
     */
    private Integer status;

    /**
     * 目录ID
     */
    private Long catalogId;

    /**
     * 目录ID列表
     */
    private List<Long> catalogIdList;

    /**
     * 租户名字或者pref
     */
    private String tenantNameOrPref;

    /**
     * 租户联系人手机号
     */
    private String tenantContactMobile;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

}