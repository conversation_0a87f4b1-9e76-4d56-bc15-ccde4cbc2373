package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店-开通服务包关系 Dto
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantServicePackRelationDto extends BaseDto {

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 服务包ID
     */
    private Integer servicePackId;

    /**
     * 目录版本id
     */
    private Long catalogId;

    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 服务机构类型（1-医保、2-药监、3-互联网医院监管、99-其他）
     */
    private Integer organType;
    /**
     * 版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122）
     */
    private Long servicePackVersion;

    /**
     * 开通关系扩展信息 - 网络、(医院)等
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ServicePackRelationExtDto ext;

    /**
     * 状态 0未开通 1开通
     */
    private Integer status;

    @JsonIgnore
    public ServicePackRelationExtDto extGet() {
        if (ext == null) {
            ext = new ServicePackRelationExtDto();
        }
        return ext;
    }
}