package cn.iocoder.yudao.module.system.enums;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String USER_TYPE = "user_type"; // 用户类型
    String COMMON_STATUS = "common_status"; // 系统状态
    String SYSTEM_ENV_TAG = "system_env_tag"; // 环境标识

    // ========== SYSTEM 模块 ==========

    String USER_SEX = "system_user_sex"; // 用户性别

    String TENANT_TYPE = "tenant_type"; // 门店类型
    String INQUIRY_TENANT_TYPE = "inquiry_tenant_type"; // 门店类型
    String SYSTEM_BIZ_TYPE = "system_biz_type";

    String LOGIN_TYPE = "system_login_type"; // 登录日志的类型
    String LOGIN_RESULT = "system_login_result"; // 登录结果

    String ERROR_CODE_TYPE = "system_error_code_type"; // 错误码的类型枚举

    String SMS_CHANNEL_CODE = "system_sms_channel_code"; // 短信渠道编码
    String SMS_TEMPLATE_TYPE = "system_sms_template_type"; // 短信模板类型
    String SMS_SEND_STATUS = "system_sms_send_status"; // 短信发送状态
    String SMS_RECEIVE_STATUS = "system_sms_receive_status"; // 短信接收状态

    String DRUG_DIRECTIONS = "drug_directions"; // 给药途径
    String DRUG_USE_FREQUENCY = "drug_use_frequency"; // 给药频次
    String DRUG_DOSE_UNIT = "drug_dose_unit"; // 给药单位

    String PRODUCT_SPU_CATEGORY = "product_spu_category"; // 商品大类
    String PRODUCT_UNIT = "product_unit"; // 单位
    String PRODUCT_DOSAGE_FORM = "product_dosage_form"; // 剂型
    String PRODUCT_BUSINESS_SCOPE = "product_business_scope"; // 经营范围
    String PRODUCT_PRES_CATEGORY = "product_pres_category"; // 处方分类
    String PRODUCT_STORAGE_WAY = "product_storage_way"; // 储存条件
    String PRODUCT_TAX_RATE = "product_tax_rate"; // 税率

    String PRESCRIPTION_MEDICINE_TYPE = "prescription_medicine_type"; // 处方药品类型
    String PRESCRIPTION_TYPE = "prescription_type"; // 处方类型
    String PRESCRIPTION_STATUS = "prescription_status"; // 处方状态
    String INQUIRY_WAY_TYPE = "inquiry_way_type"; // 问诊方式
    String INQUIRY_BIZ_TYPE = "inquiry_biz_type"; // 问诊业务类型
    String INQUIRY_AUDIT_TYPE = "inquiry_audit_type"; // 问诊审方类型
    String BIZ_CHANNEL_TYPE = "biz_channel_type"; // 业务渠道类型
    String CLIENT_CHANNEL_TYPE = "client_channel_type"; // 客户端渠道类型
    String PRESCRIPTION_PRINT_STATUS = "prescription_print_status"; // 处方打印状态
    String PRESCRIPTION_DATE_TYPE = "prescription_date_type"; // 处方日期类型
    String PRESCRIPTION_DRUGSTORE_AUDIT_TYPE = "prescription_drugstore_audit_type"; // 门店处方审方类型


    String TENANT_PACKAGE_RELATION_STATUS = "tenant_package_relation_status"; // 套餐包状态
    String TENANT_PACKAGE_RELATION_NATURE = "tenant_package_relation_nature"; // 套餐包性质
    String TENANT_PACKAGE_RELATION_PAYMENT_TYPE = "tenant_package_relation_payment_type"; // 套餐包收款方式
    String TENANT_PACKAGE_RELATION_COLLECT_ACCOUNT = "tenant_package_relation_collect_account"; // 套餐收款账户
    String TENANT_PACKAGE_RELATION_SIGN_CHANNEL = "tenant_package_relation_sign_channel"; // 套餐签约渠道
    String TENANT_PACKAGE_RELATION_REFUND_TYPE = "tenant_package_relation_refund_type"; // 套餐退款类型
    String TENANT_PACKAGE_EFFECTIVE_STATUS = "tenant_package_effective_status"; // 套餐服务生效状态

    String DIAGNOSIS_TYPE = "diagnosis_type"; // 诊断类型
    String DIAGNOSIS_SEX_LIMIT = "diagnosis_sex_limit"; // 诊断性别限制

    String MIGRATION_STATUS = "migration_status"; // 迁移状态
    String MIGRATION_POINT_STATUS = "migration_point_status"; // 迁移节点状态

}