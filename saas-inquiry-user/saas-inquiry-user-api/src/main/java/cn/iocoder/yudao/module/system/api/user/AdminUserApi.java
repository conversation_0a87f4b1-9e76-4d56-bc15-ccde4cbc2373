package cn.iocoder.yudao.module.system.api.user;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Admin 用户 API 接口
 *
 * <AUTHOR>
 */
public interface AdminUserApi {

    /**
     * 获取登录用户的信息
     *
     * @return 用户对象信息
     */
    AdminUserRespDTO getUser();

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserRespDTO getUser(@NotNull Long id);

    /**
     * 获取用户基础信息,无需tenantId
     *
     * @param id
     * @return
     */
    AdminUserRespDTO getUserBaseInfo(@NotNull Long id);


    /**
     * 通过用户 手机号 查询用户
     *
     * @param mobile 用户手机号
     * @return 用户对象信息
     */
    AdminUserRespDTO getUserByMobile(String mobile);

    /**
     * 校验手机号是否可以在当前门店下创建 手机号全局不存在，或者仅存在在当前门店下(关联上userId)
     *
     * @param mobile 用户手机号
     */
    void checkUserMobileCanCreateOnTenant(String mobile);

    /**
     * 保存或者修改用户
     *
     * @param userSaveDTO
     * @return userId
     */
    Long saveOrUpdateUserByMobile(@Valid AdminUserSaveDTO userSaveDTO);

    /**
     * 创建用户
     *
     * @param userSaveDTO
     * @return
     */
    Long createUser(AdminUserSaveDTO userSaveDTO);

    /**
     * 更新用户信息
     *
     * @param userSaveDTO
     * @return
     */
    Long updateUser(AdminUserSaveDTO userSaveDTO);

    /**
     * 通过用户 ID 查询用户下属
     *
     * @param id 用户编号
     * @return 用户下属用户列表
     */
    List<AdminUserRespDTO> getUserListBySubordinate(Long id);

    /**
     * 通过用户 ID 查询用户们
     *
     * @param ids 用户 ID 们
     * @return 用户对象信息
     */
    List<AdminUserRespDTO> getUserList(Collection<Long> ids);

    /**
     * 获得指定部门的用户数组
     *
     * @param deptIds 部门数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getUserListByDeptIds(Collection<Long> deptIds);

    /**
     * 获得指定岗位的用户数组
     *
     * @param postIds 岗位数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getUserListByPostIds(Collection<Long> postIds);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    default Map<Long, String> getUserNameMap(Collection<Long> ids) {
        return getUserList(ids).stream().collect(Collectors.toMap(AdminUserRespDTO::getId, AdminUserRespDTO::getNickname, (a, b) -> b));
    }

    /**
     * 校验用户是否有效。如下情况，视为无效： 1. 用户编号不存在 2. 用户被禁用
     *
     * @param id 用户编号
     */
    default void validateUser(Long id) {
        validateUserList(Collections.singleton(id));
    }

    /**
     * 校验用户们是否有效。如下情况，视为无效： 1. 用户编号不存在 2. 用户被禁用
     *
     * @param ids 用户编号数组
     */
    void validateUserList(Collection<Long> ids);

    /**
     * 重置用户guid
     *
     * @param userId
     */
    void resetUserGuid(Long userId);

    /**
     * 删除用户 - 系统
     *
     * @param id 用户编号
     */
    void deleteUserSystem(Long id);

    /**
     * 根据角色codes获取 当前门店用户列表
     *
     * @param roleCodes 角色Codes
     * @return
     */
    List<AdminUserRespDTO> getUserListByRoleCodes(List<String> roleCodes);

    /**
     * 获取当前租户下的user
     *
     * @return
     */
    AdminUserRespDTO getTenantUser();


    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    default Map<Long, AdminUserRespDTO> getUserMap(Collection<Long> ids) {
        List<AdminUserRespDTO> users = getUserList(ids);
        return CollectionUtils.convertMap(users, AdminUserRespDTO::getId);
    }

    /**
     * 根据名称模糊搜索用户
     *
     * @param nickname
     * @return
     */
    List<AdminUserRespDTO> getUserListByLikeNickName(String nickname);

    /**
     * 强制用户退出登录
     *
     * @param userId
     */
    void logout(Long userId);
}
