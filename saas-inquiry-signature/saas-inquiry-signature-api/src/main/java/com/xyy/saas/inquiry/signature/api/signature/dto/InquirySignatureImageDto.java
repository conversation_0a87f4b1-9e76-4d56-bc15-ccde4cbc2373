package com.xyy.saas.inquiry.signature.api.signature.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/04/22 20:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquirySignatureImageDto implements java.io.Serializable {

    /**
     * 原始图片
     */
    private String sourceUrl;

    /**
     * 水印 / 合并图片
     */
    private String mergeUrl;

    /**
     * x轴位置 0默认中间
     */
    private Integer x;

    /**
     * y轴位置 0默认中间
     */
    private Integer y;

    /**
     * 透明度 0.1 - 1.0 越低越透明
     */
    @Builder.Default
    private Float alpha = 0.5f;

    /**
     * 缩放宽
     */
    private Integer width;

    /**
     * 缩放高
     */
    private Integer height;

    /**
     * 偏移量,对于原始坐标y0开始，中间往下偏移多少
     */
    private Double offset;


}
