package com.xyy.saas.inquiry.signature.server.api.signature;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.util.ImageUtil;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.io.File;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.Cacheable;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/22 20:11
 */
@DubboService
public class InquirySignatureImageApiImpl implements InquirySignatureImageApi {

    @Resource
    private FileApi fileApi;


    @Resource
    private ConfigApi configApi;

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;


    private InquirySignatureImageApiImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    public static void main(String[] args) {

        File destFile = FileUtil.touch(System.getProperty("java.io.tmpdir") + File.separator + "signatureImage" + File.separator + IdUtil.fastSimpleUUID() + ".jpg");

        File file = ImageUtil.mergeImage(destFile, "https://files.test.ybm100.com/INVT/Lzinq/20250615/a16544a887f264b47c18463ee48802e3023c7ad7020cb9c738d2351eb4fe61ac.jpg"
            , "https://files.test.ybm100.com/INVT/Lzinq/20250615/8ab0d3afc5f647aab6e2106d22e92d0b4384d7057bd2c6bca986ee99d402963e.png"
            // , "https://files.test.ybm100.com/INVT/Lzinq/20250615/9f8ab019a236e9db3486a86f1f473e74f8a636550b2b6d483ee0dec8595dd04b.png"
            , 1f, 0, 705, null, null, 50);

        System.out.println(file);

    }

    @Override
    public String signatureImageMerge(InquirySignatureImageDto dto) {

        File destFile = FileUtil.touch(System.getProperty("java.io.tmpdir") + File.separator + "signatureImage" + File.separator + IdUtil.fastSimpleUUID() + ".jpg");
        try {
            File file = ImageUtil.mergeImage(destFile, dto.getSourceUrl(), dto.getMergeUrl(), dto.getAlpha(), dto.getX(), dto.getY(), dto.getOffset(), dto.getWidth(), dto.getHeight());
            if (file == null) {
                return null;
            }
            return FileApiUtil.createFile(FileUtil.readBytes(file));
        } finally {
            FileUtil.del(destFile);
        }
    }

    @Override
    public String getRemoteAuditSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum) {
        // 获取用户签名
        InquirySignatureCaAuthRespVO caAuthRespVO = inquirySignatureCaAuthService.getInquirySignatureCaInfo(new TemplateSignCheckedDto().setUserId(userId), signaturePlatformEnum);

        if (StringUtils.isBlank(caAuthRespVO.getRealSignatureUrl())) {
            return null;
        }
        // 获取文字水印
        String textUrl = configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_AUDIT_TEXT_SIGNATURE_URL);
        if (StringUtils.isBlank(textUrl)) {
            return caAuthRespVO.getRealSignatureUrl();
        }

        return getSelf().combinedUserRemoteSignImage(textUrl, caAuthRespVO.getRealSignatureUrl());
    }

    @Cacheable(value = RedisKeyConstants.SIGNATURE_AUDIT_REMOTE_PRESCRIPTION_SING_URL, key = "T(java.util.Objects).hash(#textUrl)+''+T(java.util.Objects).hash(#userSignUrl)", unless = "#result == null")
    public String combinedUserRemoteSignImage(String textUrl, String userSignUrl) {
        // 图片合并
        File destFile = FileUtil.touch(System.getProperty("java.io.tmpdir") + File.separator + "signatureTextImage" + File.separator + IdUtil.fastSimpleUUID() + ".png");
        try {
            File file = ImageUtil.combinedABImage(destFile, textUrl, userSignUrl);
            if (file == null) {
                return null;
            }
            return FileApiUtil.createFile(FileUtil.readBytes(file));
        } finally {
            FileUtil.del(destFile);
        }
    }

}
