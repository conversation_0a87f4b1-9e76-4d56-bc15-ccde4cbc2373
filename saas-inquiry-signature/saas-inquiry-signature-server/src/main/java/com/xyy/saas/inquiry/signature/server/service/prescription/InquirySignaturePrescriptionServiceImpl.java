package com.xyy.saas.inquiry.signature.server.service.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson2.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingEvent;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractAddParticipantVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.convert.prescription.InquiryPrescriptionSignatureConvert;
import com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate.PrescriptionTemplateConvert;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionExecutionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionExecutionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionExecutionSignatureProducer;
import com.xyy.saas.inquiry.signature.server.mq.producer.SignaturePassingProducer;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfServiceFactory;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import com.xyy.saas.inquiry.signature.server.util.FileUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 18:49
 */
@Service
@Slf4j
@Validated
public class InquirySignaturePrescriptionServiceImpl implements InquirySignaturePrescriptionService {

    @Autowired
    private TenantApi tenantApi;

    @Autowired
    private FileApi fileApi;

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @Resource
    private InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;

    @Resource
    protected InquirySignatureContractService inquirySignatureContractService;

    @Resource
    protected PdfServiceFactory pdfServiceFactory;
    // 处方签章通过事件Producer
    @Resource
    private SignaturePassingProducer signaturePassingProducer;
    // 处方执行签章事件Producer
    @Resource
    private PrescriptionExecutionSignatureProducer prescriptionExecutionSignatureProducer;
    @Autowired
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @Resource
    private InquirySignatureRemotePrescriptionService inquirySignatureRemotePrescriptionService;

    /**
     * 开方创建处方笺
     *
     * @param psInitDto 处方签章dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> issuePrescription(PrescriptionSignatureInitDto psInitDto) {
        log.info("【Signature】处方签章开具 prescriptionPref:{},psInitDto:{}", psInitDto.getPrescriptionPref(), JSONUtil.toJsonStr(psInitDto));

        // 校验并转换处方参数
        checkAndConvertIssueParam(psInitDto);

        // 获取或创建处方合同
        InquirySignatureContractSaveReqVO createReqVO = InquirySignatureContractConvert.INSTANCE.convertVoByIssuePrescription(psInitDto);
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.saveOrGetSignatureContractByCondition(createReqVO);
        log.info("【Signature】处方签章开具-创建合同, prescriptionPref:{},contractPref:{},isWaitingStatus:{}", psInitDto.getPrescriptionPref(), signatureContractDO.getPref(), signatureContractDO.isInitStatus());

        if (!signatureContractDO.isInitStatus()) {
            return CommonResult.success(null);
        }

        // 异步MQ 处方执行签章
        PrescriptionExecutionSignatureMessage message = PrescriptionExecutionSignatureMessage.builder().signatureInitDto(psInitDto).build();
        prescriptionExecutionSignatureProducer.sendMessage(PrescriptionExecutionSignatureEvent.builder().msg(message).build());
        return CommonResult.success(null);
    }

    /**
     * 检查并转换处方签名初始化参数
     *
     * @param psInitDto 处方签名初始化数据传输对象，包含签名初始化所需的信息
     */
    private void checkAndConvertIssueParam(PrescriptionSignatureInitDto psInitDto) {

        // 选取签章平台
        psInitDto.setSignaturePlatform(Optional.ofNullable(psInitDto.getSignaturePlatform()).orElse(inquirySignaturePlatformService.getMaster()));

        // 校验签名是否存在
        TemplateSignCheckedDto checkedDto = PrescriptionTemplateConvert.INSTANCE.convertPsInit(psInitDto);
        PrescriptionTemplateFieldDto nextTemplateField = inquiryPrescriptionTemplateService.getSignNextTemplateFieldValidUrl(checkedDto);
        InquiryPrescriptionSignatureConvert.INSTANCE.fillParticipant(psInitDto.getParticipantItem(), nextTemplateField);

        // 处理处方模板值
        InquiryPrescriptionTemplateRespVO prescriptionTemplate = inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplate(psInitDto.getTemplateId());
        if (prescriptionTemplate == null || CollectionUtils.isEmpty(prescriptionTemplate.getTemplateFields())) {
            return;
        }
        // 填充模板默认值
        Map<String, String> defaultValueMap = prescriptionTemplate.getTemplateFields().stream()
            .filter(f -> StringUtils.isNotBlank(f.getDefaultValue())).collect(Collectors.toMap(PrescriptionTemplateField::getField, PrescriptionTemplateField::getDefaultValue, (a, b) -> b));
        InquiryPrescriptionSignatureConvert.INSTANCE.convertParamMap(psInitDto, defaultValueMap, tenantApi.getTenant());
        // 填充电子签章
        fillElectronicSign(psInitDto.getParticipantItem(), prescriptionTemplate);

    }

    /**
     * 填充电子签章图片
     *
     * @param participantItem
     * @param prescriptionTemplate
     */
    private void fillElectronicSign(ParticipantItem participantItem, InquiryPrescriptionTemplateRespVO prescriptionTemplate) {

        List<String> pictureFields = prescriptionTemplate.getTemplateFields().stream().filter(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.PICTURE.code)).map(PrescriptionTemplateField::getField).toList();

        if (CollUtil.isNotEmpty(pictureFields) && pictureFields.contains(PrescriptionTemplateFieldEnum.doctorElectronicPicture.getField())) {
            String userElectronicSignatureUrl = inquiryUserSignatureInformationService.getInquiryUserSignature(participantItem.getUserId(), SignaturePlatformEnum.FDD, SignatureBizTypeEnum.USER_ELE_SIGN);
            if (StringUtils.isBlank(userElectronicSignatureUrl)) {
                return;
            }
            participantItem.setSignElectronicImgUrl(userElectronicSignatureUrl);
        }
    }

    /**
     * 审方追加处方参与方,加绘处方笺
     *
     * @param psAuditDto 审核dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto psAuditDto) {
        log.info("【Signature】处方签章审核 prescriptionPref:{},psAuditDto:{}", psAuditDto.getPrescriptionPref(), JSONUtil.toJsonStr(psAuditDto));

        // 带方审方
        if (Objects.equals(psAuditDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            return inquirySignatureRemotePrescriptionService.auditRemotePrescription(psAuditDto);
        }

        // 正常普通审方
        return normalAuditPrescription(psAuditDto);
    }


    private CommonResult<?> normalAuditPrescription(PrescriptionSignatureAuditDto psAuditDto) {
        // 追加合同参与方
        InquirySignatureContractAddParticipantVO addParticipantVO = InquiryPrescriptionSignatureConvert.INSTANCE.convertAddParticipantItem(psAuditDto);
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.appendParticipantItem(addParticipantVO);
        log.info("【Signature】处方签章审核-追加参与方 prescriptionPref:{},contractPref:{},参与方:{}", psAuditDto.getPrescriptionPref(), signatureContractDO.getPref(), signatureContractDO.getParticipants());
        if (signatureContractDO.isEndStatus()) {
            return CommonResult.success(null);
        }

        // 转换处方模板参数
        convertAuditParam(psAuditDto, signatureContractDO);

        // 异步MQ 处方执行审核
        prescriptionExecutionSignatureProducer.sendMessage(PrescriptionExecutionSignatureEvent.builder().msg(PrescriptionExecutionSignatureMessage.builder().psAuditDto(psAuditDto).build()).build());
        return CommonResult.success(null);
    }

    /**
     * 转换处方模板参数
     *
     * @param psAuditDto
     * @param signatureContractDO
     */
    private void convertAuditParam(PrescriptionSignatureAuditDto psAuditDto, InquirySignatureContractDO signatureContractDO) {
        Map<String, String> defaultValueMap = new HashMap<>();
        // 处理处方模板值
        InquiryPrescriptionTemplateRespVO prescriptionTemplate = inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplate(signatureContractDO.getTemplateIdLong());
        if (prescriptionTemplate != null && CollectionUtils.isNotEmpty(prescriptionTemplate.getTemplateFields())) {
            defaultValueMap = prescriptionTemplate.getTemplateFields().stream()
                .filter(f -> StringUtils.isNotBlank(f.getDefaultValue())).collect(Collectors.toMap(PrescriptionTemplateField::getField, PrescriptionTemplateField::getDefaultValue, (a, b) -> b));
            // 填充电子签章
            fillElectronicSign(psAuditDto.getParticipantItem(), prescriptionTemplate);
        }

        InquiryPrescriptionSignatureConvert.INSTANCE.convertContractWithAudit(signatureContractDO, defaultValueMap, psAuditDto, tenantApi.getTenant());

    }


    /**
     * 签章回调mq :自绘mq/三方平台-法大大mq
     * <p>
     * (如果是三方推单需要审核,后台加一个处方笺流程配置,数据推到这个签章回调mq)
     * <p>
     * {@link com.xyy.saas.inquiry.pharmacist.server.service.signature.InquiryPharmacistSignaturePassingServiceImpl}
     *
     * @param psMessageDto 处方签章消息回调dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "'" + RedisKeyConstants.SIGNATURE_PRESCRIPTION_CALLBACK_LOCK + "'.concat(#psMessageDto.contractPref)")
    public void signaturePrescriptionCallback(PrescriptionSignatureMessage psMessageDto) {
        log.info("【Signature】处方签章回调 contractPref:{},psMessageDto:{}", psMessageDto.getContractPref(), JSONUtil.toJsonStr(psMessageDto));

        // 1. 判断处方合同状态是否已经签署完毕
        InquirySignatureContractDO signatureContract = inquirySignatureContractService.getSignatureContractByPref(psMessageDto.getContractPref());
        ParticipantItem participantItem = signatureContract.getParticipants().getLast();
        if (signatureContract.isEndStatus()
            || !StringUtils.equals(participantItem.getActorField(), psMessageDto.getParticipantItem().getActorField())
            || Objects.equals(participantItem.getSignStatus(), ContractStatusEnum.COMPLETE.getCode())) {
            log.info("【Signature】处方签章回调-跳过 contractPref:{},participantItem:{},psMessageDto:{}", psMessageDto.getContractPref(), JSONUtil.toJsonStr(participantItem), JSONUtil.toJsonStr(psMessageDto));
            return; // 当前合同已完成 或者 回调参与方和合同当前参与方不一致 或 当前参与方已经签章
        }
        // 设置当前参与方签署完成
        participantItem.setSignStatus(ContractStatusEnum.COMPLETE.getCode());

        // 2.存储图片和pdf地址
        InquirySignatureContractStatusVO contractStatusVO = InquirySignatureContractConvert.INSTANCE.convertPsMessage(psMessageDto);
        Optional.ofNullable(StringUtils.isNotBlank(psMessageDto.getImgUrl()) ? null : FileUtils.downLoadFile(psMessageDto.getPdfUrl(), FileTypeEnum.PNG))
            .ifPresent(bytes -> contractStatusVO.setImgUrl(FileApiUtil.createFile(bytes)));

        // 3. 获取当前处方笺模板下一级节点字段
        PrescriptionTemplateField nextField = inquiryPrescriptionTemplateService.getSignNextTemplateField(signatureContract.getTemplateIdLong(), psMessageDto.getParticipantItem().getActorField());
        log.info("【Signature】处方签章回调获取到下一级节点, contractPref:{},bizId:{},nextField: {}", signatureContract.getPref(), signatureContract.getBizId(), nextField);
        // 更新合同参与方签署状态
        contractStatusVO.setBizId(signatureContract.getBizId())
            .setParticipants(signatureContract.getParticipants())
            .setContractStatus(nextField == null ? ContractStatusEnum.COMPLETE.getCode() : null);
        inquirySignatureContractService.updateSignatureContractStatus(contractStatusVO);

        // 4. 抛MQ到药师服务处理审方逻辑
        long totalLevel = signatureContract.getTemplateIdLong() == null ? 1 : inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplate(signatureContract.getTemplateIdLong()).getTemplateFields().stream()
            .filter(t -> Objects.equals(t.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code)).count();

        SignaturePassingMessage signaturePassingMessage = InquiryPrescriptionSignatureConvert.INSTANCE.convertSignPassingMessage(contractStatusVO, signatureContract.extGet().getPlatformConfigId(), psMessageDto, participantItem.getBizId(),
            nextField, totalLevel);
        signaturePassingProducer.sendMessage(SignaturePassingEvent.builder().msg(signaturePassingMessage).build());
    }


    @Override
    public String drawnPrescriptionSelf(String contractPref, boolean drawnSign) {
        log.info("【Signature】自绘处方笺, contractPref: {}", contractPref);
        InquirySignatureContractDO signatureContract = inquirySignatureContractService.getSignatureContractByPref(contractPref);
        if (signatureContract == null) {
            return null;
        }
        if (Objects.equals(signatureContract.extGet().getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            return signatureContract.extGet().getRemotePrescriptionUrl();
        }
        if (signatureContract.getTemplateIdLong() == null || !JSON.isValidObject(signatureContract.getParamDetail())) {
            return null;
        }
        return drawnContract(signatureContract, drawnSign);
    }


    public String drawnContract(InquirySignatureContractDO signatureContract, boolean drawnSign) {

        PrescriptionParamDto paramDto = JSONUtil.toBean(signatureContract.getParamDetail(), PrescriptionParamDto.class);
        if (paramDto == null) {
            return null;
        }
        // 转换填充处方模板参数
        Map<String, String> defaultValueMap = inquiryPrescriptionTemplateService.getPrescriptionTemplateDefaultValueFields(signatureContract.getTemplateIdLong());
        PrescriptionSignatureInitDto psDto = new PrescriptionSignatureInitDto().setParam(paramDto);
        InquiryPrescriptionSignatureConvert.INSTANCE.convertParamMap(psDto, defaultValueMap, TenantUtils.execute(paramDto.getTenantId(), () -> tenantApi.getTenant()));
        if (drawnSign) {
            signatureContract.getParticipants().forEach(p -> {
                psDto.getParamMap().put(p.getActorField(), p.getSignImgUrl());
                if (StringUtils.isNotBlank(p.getSignElectronicImgUrl())) {
                    psDto.getParamMap().put(PrescriptionTemplateFieldEnum.convertElectronicCode(p.getActorField()), p.getSignElectronicImgUrl());
                }
            });
        }
        CommonResult<String> commonResult = pdfServiceFactory.getInstance().generateAndUpload(inquiryPrescriptionTemplateService.getPrescriptionTemplate4Cache(signatureContract.getTemplateIdLong()).getContent(), psDto.getParamMap());
        log.info("【Signature】自绘处方笺, contractPref: {},commonResult:{}", signatureContract.getPref(), JSON.toJSONString(commonResult));
        if (commonResult.isError()) {
            return null;
        }
        return commonResult.getData();
    }
}
