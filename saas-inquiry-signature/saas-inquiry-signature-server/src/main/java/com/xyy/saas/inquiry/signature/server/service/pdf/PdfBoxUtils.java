package com.xyy.saas.inquiry.signature.server.service.pdf;

import cn.hutool.core.io.FileUtil;
import com.itextpdf.text.pdf.PdfReader;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.signature.server.util.HtmlParser;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.IntStream;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.util.CollectionUtils;


@Slf4j
public class PdfBoxUtils {

    // 经过测试,dpi为96,100,105,120,150,200中,105显示效果较为清晰,体积稳定,dpi越高图片体积越大,一般电脑显示分辨率为96
    public static final float DEFAULT_DPI = 105;
    // 默认转换的图片格式为jpg
    public static final String DEFAULT_FORMAT = "jpg";

    private static final String fontsPath = "/fonts";

    public static void main(String[] args) {
        List<InquiryImMessageDto> messages = new ArrayList<>();
        // messages.add(new InquiryImMessageDto("小明", "你好", "2019-09-09 12:12:12"));
        // messages.add(new InquiryImMessageDto("小刘", "我很好，你好么", "2019-09-09 12:12:15"));
        // messages.add(new InquiryImMessageDto("小明", "我也是", "2019-09-09 12:12:20"));
        // messages.add(new InquiryImMessageDto("小刘", "那真是太好了辅导时间开发环境康定斯基开发金卡生发剂发顺丰大杀四方发生", "2019-09-09 12:12:15"));
        File imPdf = FileUtil.createTempFile("im", ".pdf", true);
        writeMessagesToPdfWithHeader(messages, imPdf);
    }


    private static List<File> loadFontFromClasspath() {
        // 遍历classpath：fonts目录下的字体文件 添加到字体解析器
        String userDir = System.getProperty("user.dir");
        // 遍历classpath：fonts目录下的字体文件 添加到字体解析器
        File fontsDir = new File(userDir + fontsPath);
        if (!fontsDir.exists() || !fontsDir.isDirectory()) {
            return Collections.emptyList();
        }
        File[] files = fontsDir.listFiles(File::isFile);
        if (files == null || files.length == 0) {
            return Collections.emptyList();
        }
        // 优先级排序：从高到低
        return Arrays.stream(files).sorted(Comparator.comparingInt(f -> {
                int idx = Arrays.asList("msyh.ttf", "simple.ttf").indexOf(f.getName());
                return idx == -1 ? Integer.MAX_VALUE : idx;
            }))
            .toList();
    }


    /**
     * 将消息列表写入现有的PDF文件
     *
     * @param messages 消息列表
     * @param pdfFile  现有的PDF文件
     * @return 是否成功写入
     */
    public static Boolean writeMessagesToPdfWithHeader(List<InquiryImMessageDto> messages, File pdfFile) {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page);
                InputStream fontStream = new FileInputStream(loadFontFromClasspath().getFirst());
                InputStream fontStream2 = new FileInputStream(loadFontFromClasspath().get(1));) {
                // 加载fonts文件夹下的msyh.ttf字体

                PDType0Font font = PDType0Font.load(document, fontStream);
                contentStream.setFont(font, 13);
                contentStream.beginText();
                // 设置初始位置
                contentStream.newLineAtOffset(25, 750);
                // 写入表头
                contentStream.showText("发送方");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText("聊天内容");
                contentStream.newLineAtOffset(300, 0);
                contentStream.showText("发送时间");
                contentStream.newLineAtOffset(-400, -24); // 回到初始位置并换行

                PDType0Font font2 = PDType0Font.load(document, fontStream2);
                contentStream.setFont(font2, 12);

                // 写入消息列表
                for (InquiryImMessageDto message : messages) {
                    writeMessageRow(contentStream, message);
                }
                contentStream.endText();
            }
            // 保存PDF文件
            try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
                document.save(fos);
                return true;
            }
        } catch (Exception e) {
            log.error("pdf2png error", e);
            return false;
        }
    }

    private static void writeMessageRow(PDPageContentStream contentStream, InquiryImMessageDto message) {
        try {
            List<InquiryImMessageDto> list = splitMsg(message, 24);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            // 计算出list的中位下标
            int mid = list.size() == 2 ? 0 : list.size() / 2;
            for (int i = 0; i < list.size(); i++) {
                contentStream.showText(i == mid ? message.getUserName() : " ");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(list.get(i).getMsgContent());
                contentStream.newLineAtOffset(300, 0);
                contentStream.showText(i == mid ? message.getMsgTime() : " ");
                contentStream.newLineAtOffset(-400, i == list.size() - 1 ? -23 : -17); // 回到初始位置并换行
            }
        } catch (Exception e) {
            log.error("pdf2png error", e);
        }
    }

    public static List<InquiryImMessageDto> splitMsg(InquiryImMessageDto message, int length) {
        List<InquiryImMessageDto> result = new ArrayList<>();
        if (message.getMsgContent().contains("</p><p>")) {
            List<String> msgList = HtmlParser.parseHtmlParagraphs(message.getMsgContent(), "p");
            for (String msg : msgList) {
                result.addAll(getMsgList(message, msg, length));
            }
        } else {
            result = getMsgList(message, message.getMsgContent(), length);
        }
        return result;
    }

    public static List<InquiryImMessageDto> getMsgList(InquiryImMessageDto messageDto, String msg, int length) {
        List<InquiryImMessageDto> result = new ArrayList<>();
        int strLength = msg.length();
        // 计算需要的迭代次数
        IntStream.range(0, (strLength + length - 1) / length)
            .forEach(i -> {
                int startIndex = i * length;
                int endIndex = Math.min(startIndex + length, strLength);
                result.add(InquiryImMessageDto.builder().userName(messageDto.getUserName()).msgContent(msg.substring(startIndex, endIndex)).msgTime(messageDto.getMsgTime()).build());
            });
        return result;
    }

    public static Integer getPageNumber(InputStream inputStream) throws IOException {
        PdfReader pdfReader = new PdfReader(inputStream);
        int pages = pdfReader.getNumberOfPages();
        return pages;
    }

    public static InputStream pdf2png(byte[] bytes) {
        log.info("in.PdfBoxUtils.pdf2png.bytes.length:{};", null != bytes ? bytes.length : 0);
        try (PDDocument doc = Loader.loadPDF(bytes)) {

            PDFRenderer renderer = new PDFRenderer(doc);
            int pageCount = doc.getNumberOfPages();
            for (int i = 0; i < pageCount; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 144); // Windows native DPI
                // BufferedImage srcImage = resize(image, 240, 240);//产生缩略图
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {
                    ImageIO.write(image, "png", outputStream);
                    try (ByteArrayInputStream result = new ByteArrayInputStream(outputStream.toByteArray());) {
                        return result;
                    }
                }

            }
            doc.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    // public static byte[] pdf2png2(byte[] bytes) {
    //     log.info("in.PdfBoxUtils.pdf2png2.bytes.length:{};", null != bytes ? bytes.length : 0);
    //     ByteArrayOutputStream out = new ByteArrayOutputStream();
    //     BufferedImage image = null;
    //     try (PDDocument doc = Loader.loadPDF(bytes)) {
    //         PDFRenderer renderer = new PDFRenderer(doc);
    //         int pageCount = doc.getNumberOfPages();
    //         for (int i = 0; i < pageCount; i++) {
    //             image = renderer.renderImageWithDPI(i, 144); // Windows native DPI
    //             // BufferedImage srcImage = resize(image, 240, 240);//产生缩略图
    //             ImageIO.write(image, "png", out);
    //             return out.toByteArray();
    //         }
    //     } catch (IOException e) {
    //         log.error("pdf2png2 PDF转图片失败 e : ", e);
    //     } finally {
    //         IOUtils.closeQuietly(out);
    //         if (null != image) {
    //             image.flush();
    //         }
    //     }
    //     return null;
    // }

    public static byte[] pdf2png2Optimized(byte[] bytes) {
        log.info("in.PdfBoxUtils.pdf2png2.bytes.length:{};", null != bytes ? bytes.length : 0);

        try (PDDocument doc = Loader.loadPDF(bytes);
            ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            PDFRenderer renderer = new PDFRenderer(doc);
            // 只处理第一页，提高效率
            BufferedImage image = renderer.renderImageWithDPI(0, 144);

            // 可选：压缩图片质量以减少输出大小
            ImageIO.write(image, "png", out);
            image.flush(); // 立即释放图片内存

            return out.toByteArray();
        } catch (IOException e) {
            log.error("pdf2png2 PDF转图片失败 e : ", e);
            return null;
        }
    }


    public static InputStream pdf2Jpg(byte[] bytes) {
        log.info("in.PdfBoxUtils.pdf2Jpg.bytes.length:{};", null != bytes ? bytes.length : 0);
        try (PDDocument pdDocument = Loader.loadPDF(bytes);) {
            System.setProperty("sun.java2d.cmm", "sun.java2d.cmm.kcms.KcmsServiceProvider");
            // 图像合并使用参数
            // 总宽度
            int width = 0;
            // 保存一张图片中的RGB数据
            int[] singleImgRGB;
            int shiftHeight = 0;
            // 保存每张图片的像素值
            BufferedImage imageResult = null;
            // 利用PdfBox生成图像

            PDFRenderer renderer = new PDFRenderer(pdDocument);
            // 循环每个页码
            for (int i = 0, len = pdDocument.getNumberOfPages(); i < len; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, DEFAULT_DPI, ImageType.RGB);
                int imageHeight = image.getHeight();
                int imageWidth = image.getWidth();
                // 计算高度和偏移量
                if (i == 0) {
                    // 使用第一张图片宽度;
                    width = imageWidth;
                    // 保存每页图片的像素值
                    imageResult = new BufferedImage(width, imageHeight * len, BufferedImage.TYPE_INT_RGB);
                } else {
                    // 计算偏移高度
                    shiftHeight += imageHeight;
                }
                singleImgRGB = image.getRGB(0, 0, width, imageHeight, null, 0, width);
                // 写入流中
                imageResult.setRGB(0, shiftHeight, width, imageHeight, singleImgRGB, 0, width);
            }
            // 写图片
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {
                ImageIO.write(imageResult, DEFAULT_FORMAT, outputStream);
                try (ByteArrayInputStream result = new ByteArrayInputStream(outputStream.toByteArray());) {
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("PDF转图片失败");
            e.printStackTrace();
        }
        return null;
    }


    public static byte[] pdf2Jpg2(byte[] bytes) {
        log.info("in.PdfBoxUtils.pdf2Jpg2.bytes.length:{};", null != bytes ? bytes.length : 0);
        System.setProperty("sun.java2d.cmm", "sun.java2d.cmm.kcms.KcmsServiceProvider");

        int width = 0; // 总宽度
        int[] singleImgRGB; // 保存一张图片中的RGB数据
        int shiftHeight = 0;  // 保存每张图片的像素值
        BufferedImage image = null;
        BufferedImage imageResult = null;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PDDocument pdDocument = Loader.loadPDF(bytes)) {
            // 利用PdfBox生成图像
            PDFRenderer renderer = new PDFRenderer(pdDocument);

            for (int i = 0, len = pdDocument.getNumberOfPages(); i < len; i++) {
                image = renderer.renderImageWithDPI(i, DEFAULT_DPI, ImageType.RGB);
                int imageHeight = image.getHeight();
                int imageWidth = image.getWidth();
                // 计算高度和偏移量
                if (i == 0) {
                    // 使用第一张图片宽度,保存每页图片的像素值
                    width = imageWidth;
                    imageResult = new BufferedImage(width, imageHeight * len, BufferedImage.TYPE_INT_RGB);
                } else {
                    // 计算偏移高度
                    shiftHeight += imageHeight;
                }
                singleImgRGB = image.getRGB(0, 0, width, imageHeight, null, 0, width);
                imageResult.setRGB(0, shiftHeight, width, imageHeight, singleImgRGB, 0, width);
            }
            pdDocument.close();
            // 写图片
            ImageIO.write(imageResult, DEFAULT_FORMAT, outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("pdf2Jpg2 PDF转图片失败 e : ", e);
        } finally {
            IOUtils.closeQuietly(outputStream);
            if (null != imageResult) {
                imageResult.flush();
            }
            if (null != image) {
                image.flush();
            }
        }

        return null;
    }

    // public static byte[] pdf2Jpg3(byte[] bytes) {
    //     long start = System.currentTimeMillis();
    //     System.setProperty("sun.java2d.cmm", "sun.java2d.cmm.kcms.KcmsServiceProvider");
    //
    //     int width = 0; // 总宽度
    //     int[] singleImgRGB; // 保存一张图片中的RGB数据
    //     int shiftHeight = 0;  // 保存每张图片的像素值
    //     BufferedImage image = null;
    //     BufferedImage imageResult = null;
    //     ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    //
    //     try (PDDocument pdDocument = Loader.loadPDF(bytes)) {
    //         // 利用PdfBox生成图像
    //         PDFRenderer renderer = new PDFRenderer(pdDocument);
    //         for (int i = 0, len = pdDocument.getNumberOfPages(); i < len; i++) {
    //             image = renderer.renderImageWithDPI(i, DEFAULT_DPI, ImageType.RGB);
    //             int imageHeight = image.getHeight();
    //             int imageWidth = image.getWidth();
    //             // 计算高度和偏移量
    //             if (i == 0) {
    //                 // 使用第一张图片宽度,保存每页图片的像素值
    //                 width = imageWidth;
    //                 imageResult = new BufferedImage(width, imageHeight * len, BufferedImage.TYPE_INT_RGB);
    //             } else {
    //                 // 计算偏移高度
    //                 shiftHeight += imageHeight;
    //             }
    //             singleImgRGB = image.getRGB(0, 0, width, imageHeight, null, 0, width);
    //             imageResult.setRGB(0, shiftHeight, width, imageHeight, singleImgRGB, 0, width);
    //         }
    //
    //         pdDocument.close();
    //         // 写图片
    //         ImageIO.write(imageResult, DEFAULT_FORMAT, outputStream);
    //     } catch (Exception e) {
    //         log.error("pdf2Jpg3 PDF转图片失败 e : ", e);
    //     } finally {
    //         IOUtils.closeQuietly(outputStream);
    //         if (null != imageResult) {
    //             imageResult.flush();
    //         }
    //         if (null != image) {
    //             image.flush();
    //         }
    //         Long end = System.currentTimeMillis();
    //         Long times = end - start;
    //
    //         if (times > 1000) {
    //             log.info("pdf转jpg时间过长.times:{}", times);
    //         }
    //     }
    //
    //     return outputStream.toByteArray();
    // }

    public static byte[] pdf2Jpg3Optimized(byte[] bytes) {
        long start = System.currentTimeMillis();
        // 移除全局System.setProperty

        try (PDDocument pdDocument = Loader.loadPDF(bytes);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            PDFRenderer renderer = new PDFRenderer(pdDocument);
            int pageCount = pdDocument.getNumberOfPages();

            // 限制最大页数，避免内存爆炸
            int maxPages = Math.min(pageCount, 10);

            List<BufferedImage> images = new ArrayList<>();
            int totalHeight = 0;
            int maxWidth = 0;

            // 分步处理：先收集所有页面信息
            for (int i = 0; i < maxPages; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, DEFAULT_DPI, ImageType.RGB);
                images.add(image);
                totalHeight += image.getHeight();
                maxWidth = Math.max(maxWidth, image.getWidth());
            }

            // 创建合并图片
            BufferedImage result = new BufferedImage(maxWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = result.createGraphics();

            int yOffset = 0;
            for (BufferedImage img : images) {
                g2d.drawImage(img, 0, yOffset, null);
                yOffset += img.getHeight();
                img.flush(); // 立即释放
            }
            g2d.dispose();

            ImageIO.write(result, DEFAULT_FORMAT, outputStream);
            result.flush();

            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("pdf2Jpg3 PDF转图片失败 e : ", e);
            return null;
        }
    }


    /**
     * 使用pdfbox将jpg转成pdf
     *
     * @param jpgStream jpg输入流
     * @throws IOException IOException
     */
    public static byte[] jpgToPdf(InputStream jpgStream) throws IOException {
        log.info("in.PdfBoxUtils.jpgToPdf.jpgStream.length:{};", null != jpgStream ? jpgStream.available() : 0);
        try {
            PDDocument pdDocument = new PDDocument();
            BufferedImage image = ImageIO.read(jpgStream);
            PDPage pdPage = new PDPage(new PDRectangle(image.getWidth(), image.getHeight()));
            pdDocument.addPage(pdPage);
            PDImageXObject pdImageXObject = LosslessFactory.createFromImage(pdDocument, image);
            PDPageContentStream contentStream = new PDPageContentStream(pdDocument, pdPage);
            contentStream.drawImage(pdImageXObject, 0, 0, image.getWidth(), image.getHeight());
            contentStream.close();

            try (ByteArrayOutputStream out = new ByteArrayOutputStream();) {
                pdDocument.save(out);
                return out.toByteArray();
            }
        } catch (Exception e) {
            log.info("in.PdfBoxUtils.jpgToPdf.jpgStream.length:{};e: ", null != jpgStream ? jpgStream.available() : 0, e);
        }
        return null;
    }


}
