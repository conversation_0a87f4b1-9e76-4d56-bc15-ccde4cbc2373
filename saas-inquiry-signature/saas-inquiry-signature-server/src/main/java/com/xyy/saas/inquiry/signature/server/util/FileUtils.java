package com.xyy.saas.inquiry.signature.server.util;

import cn.hutool.core.codec.Base64Decoder;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfBoxUtils;
import com.xyy.saas.inquiry.util.UrlConUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import javax.imageio.stream.FileImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 主要stream，byte[],pdf,jpg 等的转换
 */
@Slf4j
public class FileUtils {


    public static ByteArrayInputStream out2in(ByteArrayOutputStream bout) throws Exception {
        byte[] buff = bout.toByteArray();
        ByteArrayInputStream bin = new ByteArrayInputStream(buff);
        return bin;
    }

    public static ByteArrayOutputStream in2out(InputStream inputStream) throws Exception {
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        int ch;
        while ((ch = inputStream.read()) != -1) {
            swapStream.write(ch);
        }
        return swapStream;
    }

    /**
     * 将InputStream写入本地文件
     *
     * @param destination 写入本地目录
     * @param input       输入流
     * @throws IOException IOException
     */
    public static void writeToLocal(String destination, InputStream input)
        throws IOException {
        int index;
        byte[] bytes = new byte[1024];
        FileOutputStream downloadFile = new FileOutputStream(destination);
        while ((index = input.read(bytes)) != -1) {
            downloadFile.write(bytes, 0, index);
            downloadFile.flush();
        }
        input.close();
        downloadFile.close();

    }

    public static byte[] inputStream2byte(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inputStream.read(buff, 0, 100)) > 0) {
            byteArrayOutputStream.write(buff, 0, rc);
        }
        return byteArrayOutputStream.toByteArray();
    }

    public static InputStream byte2inputStream(byte[] bytes) {
        InputStream input = new ByteArrayInputStream(bytes);
        return input;
    }

    public static byte[] outputStream2byte(ByteArrayOutputStream out) throws Exception {
        return out.toByteArray();
    }


    /**
     * outputstream
     */
    public static ByteArrayOutputStream outputStream2byte(byte[] bytes) throws Exception {
        InputStream inputStream = byte2inputStream(bytes);
        ByteArrayOutputStream byteArrayOutputStream = in2out(inputStream);
        return byteArrayOutputStream;
    }

    /**
     * byte生成文件
     */
    public static void byte2File(byte[] bytes, String filePath, String fileName) throws IOException {
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        BufferedImage bi1 = ImageIO.read(bais);
        File files = new File(filePath + fileName);// 可以是jpg,png,gif格式
        ImageIO.write(bi1, "jpg", files);// 不管输出什么格式图片，此处不需改动
    }

    /**
     * byte生成文件
     */
    public static void byte2File2(byte[] bytes, String filePath, String fileName) throws IOException {
        FileImageOutputStream imageOutput = new FileImageOutputStream(new File(filePath + fileName));
        imageOutput.write(bytes, 0, bytes.length);
        imageOutput.close();
    }

    public static byte[] file2bytes(File file) throws Exception {

        FileInputStream fis = new FileInputStream(file);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] bytes = new byte[1000];
        int n;
        while ((n = fis.read(bytes)) != -1) {
            bos.write(bytes, 0, 1);
        }
        fis.close();
        byte[] data = bos.toByteArray();
        bos.close();
        return data;

    }


    public static void closeInputStream(InputStream inputStream) {
        try {
            if (null != inputStream) {
                inputStream.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建文件夹
     */
    public static void makeDir(String dirPath) {
        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * 创建文件
     */
    public static void makeFile(String dirPath) {
        File file = new File(dirPath);
        // 如果文件夹不存在则创建
        if (!file.exists() && !file.isDirectory()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {

        }
    }

    public static byte[] toByteArray(InputStream input) {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while (true) {
            try {
                if (!(-1 != (n = input.read(buffer)))) {
                    break;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }


    public static InputStream imageTogeter(String addimageUrl, String originimageUrl) {
        URL originurl = null;
        BufferedImage originImg = null;
        try {
            originurl = new URL(originimageUrl);
            originImg = ImageIO.read(originurl);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        URL addurl = null;
        BufferedImage addImg = null;
        try {
            addurl = new URL(addimageUrl);
            addImg = ImageIO.read(addurl);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        Graphics2D g = originImg.createGraphics();
        g.drawImage(addImg, 593, 1875, 335, 335, null);
        g.dispose();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(originImg, "png", outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ByteArrayInputStream result = new ByteArrayInputStream(outputStream.toByteArray());
        return result;

    }


    /**
     * 图片合成 list 传图片全路径
     *
     * @throws Exception
     */
    public static InputStream imageJion(List<String> imgUrls) throws Exception {
        int width = 0;
        int totalHeight = 0;
        for (int i = 0; i < imgUrls.size(); i++) {
            Image src1 = ImageIO.read(new URL(imgUrls.get(i)));
            // 获取图片的宽度
            if (i == 0) {
                width = src1.getWidth(null);
            }
            // 将图片的高度相加
            totalHeight = totalHeight + src1.getHeight(null);
        }
        // 构造一个类型为预定义图像类型之一的 BufferedImage。 宽度为第一只的宽度，高度为各个图片高度之和
        BufferedImage tag = new BufferedImage(width, totalHeight, BufferedImage.TYPE_INT_RGB);
        // 绘制合成图像
        Graphics g = tag.createGraphics();
        int heightThree = 0;
        for (int i = 0; i < imgUrls.size(); i++) {
            Image src1 = ImageIO.read(new URL(imgUrls.get(i)));
            int height = src1.getHeight(null);
            // 绘制合成图像
            if (i == 0) {
                heightThree = height;
                g.drawImage(src1, 0, 0, width, height, null);
            } else {
                g.drawImage(src1, 0, heightThree, width, height, null);
                heightThree = heightThree + height;
            }
        }
        // 释放此图形的上下文以及它使用的所有系统资源。
        g.dispose();
        // 创建输出流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // 将绘制的图像生成至输出流
        // JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
        // encoder.encode(tag);

        ByteArrayInputStream result = new ByteArrayInputStream(out.toByteArray());
        return result;
    }


    /**
     * 获取多张图片的二进制
     *
     * @param imgUrls 多张图片URL地址数组
     * @return
     * @throws Exception
     */
    public static InputStream imageZip(List<String> imgUrls) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);

        for (String imgUrl : imgUrls) {
            // 获取图片名称
            String imgName = imgUrl.substring(imgUrl.lastIndexOf("/") + 1);
            String imageName = imgName.substring(0, imgName.lastIndexOf("."));

            // 将当前的图片放到zip流中传出去
            downLoadImage(imgUrl, imageName, zipOutputStream);
        }
        IOUtils.closeQuietly(zipOutputStream);
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        return inputStream;
    }

    /**
     * 将当前的图片放到zip流中传出去
     *
     * @param imageUrl        图片URL地址
     * @param imageName       图片名称
     * @param zipOutputStream zip输出流
     */
    public static void downLoadImage(String imageUrl, String imageName, ZipOutputStream zipOutputStream) {
        String imgArray[] = {"jpg", "png", "gif", "bmp", "jpeg"};
        List suffixList = Arrays.asList(imgArray);
        String suffix = imageUrl.substring(imageUrl.lastIndexOf(".") + 1);

        if (StringUtils.isNotBlank(imageUrl)) {
            BufferedInputStream in = null;
            try {
                // 校验读取到文件
                if (!suffixList.contains(suffix)) {
                    // 文件格式不对
                    throw new Exception("不是图片");
                }

                imageName += "." + suffix;

                URL url = new URL(imageUrl);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(5 * 1000);
                conn.setRequestMethod("GET");
                conn.setRequestProperty(
                    "Accept", "image/gif, image/jpeg, image/pjpeg, image/pjpeg, "
                        + "application/x-shockwave-flash, application/xaml+xml, "
                        + "application/vnd.ms-xpsdocument, application/x-ms-xbap, "
                        + "application/x-ms-application, application/vnd.ms-excel, "
                        + "application/vnd.ms-powerpoint, application/msword, */*");
                conn.setRequestProperty("Accept-Language", "zh-CN");
                conn.setRequestProperty("Charset", "UTF-8");

                InputStream inStream = conn.getInputStream();
                if (inStream == null) {
                    throw new Exception("获取压缩的数据项失败! 图片名为：" + imageName);
                } else {
                    in = new BufferedInputStream(inStream);
                }

                // 压缩条目不是具体独立的文件，而是压缩包文件列表中的列表项，称为条目，就像索引一样
                // ZipEntry zipEntry = new ZipEntry("图片/" + imageName);

                ZipEntry zipEntry = new ZipEntry(imageName);
                // 定位到该压缩条目位置，开始写入文件到压缩包中
                zipOutputStream.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024 * 5]; // 读写缓冲区
                int read = 0;
                while ((read = in.read(bytes)) != -1) {
                    zipOutputStream.write(bytes, 0, read);
                }
                IOUtils.closeQuietly(inStream); // 关掉输入流
                IOUtils.closeQuietly(in); // 关掉缓冲输入流
                zipOutputStream.closeEntry();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void filesZip(InputStream inputStream, String imageName, ZipOutputStream zipOutputStream) {
        BufferedInputStream in = null;
        try {
            if (inputStream == null) {
                throw new Exception("获取压缩的数据项失败! 图片名为：" + imageName);
            } else {
                in = new BufferedInputStream(inputStream);
            }
            ZipEntry zipEntry = new ZipEntry(imageName);
            // 定位到该压缩条目位置，开始写入文件到压缩包中
            zipOutputStream.putNextEntry(zipEntry);
            byte[] bytes = new byte[1024 * 5]; // 读写缓冲区
            int read = 0;
            while ((read = in.read(bytes)) != -1) {
                zipOutputStream.write(bytes, 0, read);
            }

            IOUtils.closeQuietly(in); // 关掉输入流
            IOUtils.closeQuietly(inputStream); // 关掉缓冲输入流
            zipOutputStream.closeEntry();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Boolean checkoutImg(String fileUrl) {
        Boolean result = false;
        InputStream stream = null;
        try {
            stream = new URL(fileUrl).openStream();
            log.info("in.SignaturesApiImpl.checkoutImg.fileUrl:{};size:{}", fileUrl, Objects.nonNull(stream) ? stream.available() : 0);
            if (Objects.nonNull(stream) && stream.available() > 256) {
                result = true;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * base64转byte数组
     *
     * @param base64str base64
     * @return byte数组
     */
    public static byte[] base642byte(String base64str) {
        if (StringUtils.isEmpty(base64str)) {
            return null;
        }
        // 过滤前缀 eg : data:image/png;base64,
        if (base64str.contains(",")) {
            base64str = base64str.split(",")[1];
        }
        byte[] ret = Base64Decoder.decode(base64str);
        for (int i = 0; i < ret.length; ++i) {
            if (ret[i] < 0) {// 调整异常数据
                ret[i] += 256;
            }
        }
        return ret;
    }

    /**
     * 下载远端资源文件
     *
     * @param url          远端资源url
     * @param fileTypeEnum 下载的文件类型
     * @return 字节数组
     */
    public static byte[] downLoadFile(String url, FileTypeEnum fileTypeEnum) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try (InputStream inputStream = UrlConUtil.getStreamRetry("GET", url, 5000)) {
            if (FileTypeEnum.PDF.equals(fileTypeEnum)) {
                return IOUtils.toByteArray(inputStream);
            }
            if (FileTypeEnum.JPG.equals(fileTypeEnum)) {
                return PdfBoxUtils.pdf2Jpg3Optimized(IOUtils.toByteArray(inputStream));
            }
            if (FileTypeEnum.PNG.equals(fileTypeEnum)) {
                return PdfBoxUtils.pdf2png2Optimized(IOUtils.toByteArray(inputStream));
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


}
