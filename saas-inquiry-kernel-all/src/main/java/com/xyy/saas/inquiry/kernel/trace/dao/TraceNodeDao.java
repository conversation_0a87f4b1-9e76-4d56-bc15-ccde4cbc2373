package com.xyy.saas.inquiry.kernel.trace.dao;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.kernel.trace.dto.TraceNodeQuery;
import com.xyy.saas.inquiry.kernel.trace.util.ElasticsearchQueryUtils;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import com.xyy.saas.inquiry.trace.service.TraceNodeService.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 链路追踪数据访问对象
 * 
 * 🔥 ES版本兼容性支持：
 * - ES 6.8.6 (测试环境)：使用 new IndexRequest(index, type, id) 构造方法
 * - ES 7.17.3 (正式环境)：使用 new IndexRequest(index).id(id) 构造方法
 * - 自动检测ES版本并使用相应的API，确保在两个环境中都能正常工作
 * 
 * 批量写入特性：
 * - 使用ES BulkRequest实现真正的批量写入
 * - 一次HTTP请求写入多条记录，而非循环单条写入
 * - 智能错误处理，部分失败不影响整体功能
 */
@Repository
@Slf4j
public class TraceNodeDao {

    private static final String INDEX_NAME = "inquiry_trace_node";
    private static final String TYPE_NAME = "doc";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * ES版本号，用于兼容性处理
     */
    @Value("${elasticsearch.version:7.17.3}")
    private String esVersion;
    
    /**
     * 是否是ES 7.x版本
     */
    private boolean isES7() {
        return esVersion.startsWith("7.");
    }
    
    /**
     * 保存链路追踪数据
     *
     * @param traceNodeData 链路追踪数据
     * @return 是否保存成功
     */
    public boolean save(TraceNodeData traceNodeData) {
        try {
            // 如果ID为空，生成一个UUID
            if (traceNodeData.getId() == null || traceNodeData.getId().isEmpty()) {
                traceNodeData.setId(UUID.randomUUID().toString().replace("-", ""));
            }
            
            // 将对象转换为JSON
            String json = objectMapper.writeValueAsString(traceNodeData);
            
            // 🔥 ES版本兼容性处理：根据版本使用不同的构造方法
            IndexRequest indexRequest;
            if (isES7()) {
                // ES 7.x 方式：index + id
                indexRequest = new IndexRequest(INDEX_NAME).id(traceNodeData.getId());
            } else {
                // ES 6.x 方式：index + type + id
                indexRequest = new IndexRequest(INDEX_NAME, TYPE_NAME, traceNodeData.getId());
            }
            
            // 设置文档内容
            indexRequest.source(json, XContentType.JSON);
            
            // 执行请求
            IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            
            return response.status().getStatus() >= 200 && response.status().getStatus() < 300;
        } catch (Exception e) {
            log.error("保存链路追踪数据到ES失败", e);
            return false;
        }
    }

    /**
     * 批量保存链路追踪数据
     * 🔥 核心优化：使用ES BulkRequest，一次HTTP请求写入多条记录
     *
     * @param traceNodeDataList 链路追踪数据列表
     * @return 成功保存的数量
     */
    public int batchSave(List<TraceNodeData> traceNodeDataList) {
        if (traceNodeDataList == null || traceNodeDataList.isEmpty()) {
            return 0;
        }

        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            // 构建批量请求
            for (TraceNodeData traceNodeData : traceNodeDataList) {
                // 如果ID为空，生成一个UUID
                if (traceNodeData.getId() == null || traceNodeData.getId().isEmpty()) {
                    traceNodeData.setId(UUID.randomUUID().toString().replace("-", ""));
                }
                
                // 将对象转换为JSON
                String json = objectMapper.writeValueAsString(traceNodeData);
                
                // 🔥 ES版本兼容性处理：根据版本使用不同的构造方法
                IndexRequest indexRequest;
                if (isES7()) {
                    // ES 7.x 方式：index + id
                    indexRequest = new IndexRequest(INDEX_NAME).id(traceNodeData.getId());
                } else {
                    // ES 6.x 方式：index + type + id
                    indexRequest = new IndexRequest(INDEX_NAME, TYPE_NAME, traceNodeData.getId());
                }
                
                // 设置文档内容
                indexRequest.source(json, XContentType.JSON);
                
                // 添加到批量请求中
                bulkRequest.add(indexRequest);
            }
            
            // 执行批量请求
            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            
            // 统计成功保存的数量
            int successCount = 0;
            if (bulkResponse.hasFailures()) {
                // 部分失败，统计成功的数量
                for (org.elasticsearch.action.bulk.BulkItemResponse item : bulkResponse.getItems()) {
                    if (!item.isFailed()) {
                        successCount++;
                    } else {
                        // 记录失败的详细信息（调试时使用）
                        if (log.isDebugEnabled()) {
                            log.debug("批量保存单条失败: id={}, 原因={}", item.getId(), item.getFailureMessage());
                        }
                    }
                }
                
                log.warn("批量保存链路追踪数据部分失败: 总数={}, 成功={}, 失败={}", 
                    traceNodeDataList.size(), successCount, traceNodeDataList.size() - successCount);
            } else {
                // 全部成功
                successCount = traceNodeDataList.size();
            }
            
            if (log.isDebugEnabled()) {
                log.debug("批量保存链路追踪数据完成: 请求数={}, 成功数={}", traceNodeDataList.size(), successCount);
            }
            
            return successCount;
            
        } catch (Exception e) {
            log.error("批量保存链路追踪数据到ES失败", e);
            return 0;
        }
    }

    public PageResult<TraceNodeData> findByCondition(TraceNodeQuery query) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 添加查询条件
            if (query.getBusinessNo() != null && !query.getBusinessNo().isEmpty()) {
                boolQuery.must(QueryBuilders.termQuery("businessNo", query.getBusinessNo()));
            }
            if (StringUtils.isNotBlank(query.getKeywords())) {
                boolQuery.must(QueryBuilders.wildcardQuery("businessData", "*" + query.getKeywords() + "*"));
            }
            if (query.getNodeCode() != null && !query.getNodeCode().isEmpty()) {
                boolQuery.must(QueryBuilders.termQuery("nodeCode", query.getNodeCode()));
            }
            if (query.getNodeName() != null && !query.getNodeName().isEmpty()) {
                boolQuery.must(QueryBuilders.wildcardQuery("nodeName", "*" + query.getNodeName() + "*"));
            }
            if (query.getTenantId() != null) {
                boolQuery.must(QueryBuilders.termQuery("tenantId", query.getTenantId()));
            }
            if (query.getEnvTag() != null && !query.getEnvTag().isEmpty()) {
                boolQuery.must(QueryBuilders.termQuery("envTag", query.getEnvTag()));
            }
            if (query.getSuccess() != null) {
                boolQuery.must(QueryBuilders.termQuery("success", query.getSuccess()));
            }

            // 添加时间范围查询 - 使用毫秒时间戳避免日期格式解析问题
            if (query.getStartTimeBegin() != null && query.getStartTimeEnd() != null) {
                long beginTimestamp = query.getStartTimeBegin().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = query.getStartTimeEnd().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                ElasticsearchQueryUtils.addTimeRangeQuery(boolQuery, "startTime", beginTimestamp, endTimestamp);
            }

            // 添加执行时长范围查询
            ElasticsearchQueryUtils.addRangeQuery(boolQuery, "duration",
                    query.getDurationMin(), query.getDurationMax());

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
            
            // ES 6.x需要设置type
            if (!isES7()) {
                searchRequest.types(TYPE_NAME);
            }
            
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            
            // 根据ES版本设置trackTotalHits - ES 6.x不支持此方法
            if (isES7()) {
                // ES 7.x 设置track_total_hits为true，确保返回准确的总记录数（不受10000限制）
                searchSourceBuilder.trackTotalHits(true);
            }

            // 添加排序
            if (StringUtils.isNotBlank(query.getOrderBy())) {
                SortOrder sortOrder = "desc".equalsIgnoreCase(query.getOrderDirection()) ? SortOrder.DESC : SortOrder.ASC;
                searchSourceBuilder.sort(query.getOrderBy(), sortOrder);
            }

            // 添加分页
            searchSourceBuilder.from((query.getPageNo() - 1) * query.getPageSize());
            searchSourceBuilder.size(query.getPageSize());

            searchRequest.source(searchSourceBuilder);

            // 执行搜索
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 处理结果
            List<TraceNodeData> resultList = new ArrayList<>();
            for (SearchHit hit : response.getHits().getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                TraceNodeData traceNodeData = convertToTraceNodeData(sourceAsMap);
                resultList.add(traceNodeData);
            }
            return new PageResult<>(resultList, Objects.requireNonNull(response.getHits().getTotalHits()).value);
        } catch (Exception e) {
            log.error("查询链路追踪数据失败", e);
            return new PageResult<>(new ArrayList<>(), 0);
        }
    }

    /**
     * 将ES文档转换为TraceNodeData对象
     */
    private TraceNodeData convertToTraceNodeData(Map<String, Object> source) {
        TraceNodeData data = new TraceNodeData();
        data.setId((String) source.get("id"));
        data.setBusinessNo((String) source.get("businessNo"));
        data.setNodeCode((String) source.get("nodeCode"));
        data.setNodeName((String) source.get("nodeName"));

        // 处理时间字段
        Long startTime = (Long) source.get("startTime");
        Long endTime = (Long)source.get("endTime");
        Long createTime = (Long)source.get("createTime");

        if (startTime != null) {
            data.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault()));
        }
        if (endTime != null) {
            data.setEndTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault()));
        }
        if (createTime != null) {
            data.setCreateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(createTime), ZoneId.systemDefault()));
        }

        data.setDuration(ElasticsearchQueryUtils.convertToLong(source.get("duration")));
        data.setSuccess((Boolean) source.get("success"));
        data.setErrorMsg((String) source.get("errorMsg"));
        data.setBusinessData((String) source.get("businessData"));
        data.setTenantId(ElasticsearchQueryUtils.convertToLong(source.get("tenantId")));
        data.setEnvTag((String) source.get("envTag"));
        return data;
    }
} 