package com.xyy.saas.inquiry.kernel.trace.parser;

import com.alibaba.fastjson2.JSONObject;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.function.Function;

/**
 * 链路追踪内容解析器接口
 * 用于根据节点类型解析和填充业务数据
 */
@Component
@Slf4j
public abstract class TraceContentParser {

    /**
     * 超长数据占位符 - 与SimpleTraceBufferService中的常量保持一致
     */
    private static final String OVERSIZED_DATA_PLACEHOLDER = "采集到的链路数据超长";

    /**
     * 解析内容，填充业务数据
     *
     * @param traceNodeData 链路追踪数据
     * @return 解析后的链路追踪数据
     */
    public abstract TraceNodeData parserContent(TraceNodeData traceNodeData);

    public TraceNodeData baseParserContent(TraceNodeData traceNodeData, Map<String, Function<TraceNodeData, TraceNodeData>> contentParserMap){
        if (traceNodeData == null || traceNodeData.getNodeCode() == null || traceNodeData.getBusinessData() == null) {
            return traceNodeData;
        }

        if (OVERSIZED_DATA_PLACEHOLDER.equals(traceNodeData.getBusinessData())) {
            return traceNodeData;
        }
        
        if(!traceNodeData.getSuccess()){
            traceNodeData.setBusinessData(traceNodeData.getErrorMsg());
            return traceNodeData;
        }
        Function<TraceNodeData, TraceNodeData> parserFunction = contentParserMap.get(traceNodeData.getNodeCode());
        if (parserFunction != null) {
            try {
                return parserFunction.apply(traceNodeData);
            } catch (Exception e) {
                log.error("解析问诊节点内容失败，nodeCode: {}, businessNo: {}",
                    traceNodeData.getNodeCode(), traceNodeData.getBusinessNo(), e);
                return traceNodeData;
            }
        }

        // 如果没有找到对应的解析函数，返回原始数据
        return traceNodeData;
    }

    /**
     * 从业务参数中解析问诊单对象
     * 🔥 增强异常处理：兼容超长数据和异常格式
     * @param traceNodeData
     * @return
     */
    public InquiryRecordDto getInquiryByBusinessData(TraceNodeData traceNodeData ,String argName) {
        try {
            // 🔥 兼容性检查：如果是超长数据占位符，返回null
            if (OVERSIZED_DATA_PLACEHOLDER.equals(traceNodeData.getBusinessData())) {
                return null;
            }
            
            JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
            JSONObject requestParam = josnParam.getJSONObject("request");
            return JSONObject.parseObject(requestParam.getString(argName), InquiryRecordDto.class);
        } catch (Exception e) {
            // 🔥 解析异常时返回null，避免影响整体链路查询
            log.debug("解析问诊数据失败，可能是数据格式异常或超长数据 - 业务号：{}，节点：{}", 
                traceNodeData.getBusinessNo(), traceNodeData.getNodeCode());
            return null;
        }
    }
    
    /**
     * 检查是否为超长数据占位符
     * @param businessData 业务数据
     * @return true表示是超长数据占位符
     */
    protected boolean isOversizedDataPlaceholder(String businessData) {
        return OVERSIZED_DATA_PLACEHOLDER.equals(businessData);
    }
} 