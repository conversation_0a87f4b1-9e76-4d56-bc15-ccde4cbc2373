package com.xyy.saas.inquiry.kernel.trace.parser;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.DispatchDeptTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.api.dept.InquiryDeptApi;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryDeptDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.PatientVO;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 问诊业务链路追踪内容解析器
 * 专门处理问诊相关业务节点的内容解析和业务数据填充
 */
@Component
@Slf4j
public class InquiryTraceContentParser extends TraceContentParser {

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private InquiryDeptApi inquiryDeptApi;

    private final Map<String, Function<TraceNodeData, TraceNodeData>> contentParserMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化问诊相关节点的解析函数
        contentParserMap.put(TraceNodeEnum.CREATE_INQUIRY.getCode(), this::parseCreateInquiry);
        contentParserMap.put(TraceNodeEnum.PRE_CHECK.getCode(), this::preCheck);
        contentParserMap.put(TraceNodeEnum.ASSIGN_HOSPITAL.getCode(), this::assignHospital);
        contentParserMap.put(TraceNodeEnum.ASSIGN_DEPT.getCode(), this::assignDept);
        contentParserMap.put(TraceNodeEnum.ASSIGN_TYPE.getCode(), this::assignType);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_ALLERGIC.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_LIVERKIDNEY.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_PREGNANCYLACTATION.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_FORCETOREAL.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_SPECIALAGE.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_PRODUCT_USAGEDOSAGE.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_MULTIDIAGNOSE.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_MANYAUTOVIDEO.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.AUOTO_INQUIRY_AUTOVIDEOWEIGHT.getCode(), this::assignTypeDetail);
        contentParserMap.put(TraceNodeEnum.SAVE_INQUIRY_AND_PUSH_RECEPTIONAREA.getCode(), this::saveInquiry);
        contentParserMap.put(TraceNodeEnum.TIMEOUT_CANCEL_INQUIRY.getCode(), this::cancelInquiry);
        contentParserMap.put(TraceNodeEnum.PATIENT_CANCEL_INQUIRY.getCode(), this::cancelInquiry);
    }

    @Override
    public TraceNodeData parserContent(TraceNodeData traceNodeData) {
       return super.baseParserContent(traceNodeData, contentParserMap);
    }

    /**
     * 解析创建问诊节点
     */
    private TraceNodeData parseCreateInquiry(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                traceNodeData.setBusinessData("问诊创建 - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            TenantDto tenant = tenantApi.getTenant(traceNodeData.getTenantId());
            DrugstoreInquiryReqVO reqVO = JSONObject.parseObject(traceNodeData.getBusinessData(), DrugstoreInquiryReqVO.class);
            BaseInquiryReqVO baseInquiryReqVO = reqVO.getBaseInquiryReqVO();
            PatientVO patient = baseInquiryReqVO.getPatient();
            List<InquiryProductDetailDto> productInfos = baseInquiryReqVO.getInquiryProductInfo().getInquiryProductInfos();
            String content = "门店: " + tenant.getName() + " 通过"
                + Objects.requireNonNull(ClientChannelTypeEnum.fromCode(reqVO.getClientChannelType())).getDescription()
                + " 发起了 " + MedicineTypeEnum.getMedicineTypeName(baseInquiryReqVO.getMedicineType())+" "+ InquiryWayTypeEnum.fromCode(reqVO.getInquiryWayType()).getDesc()
                + " ,患者姓名: " + patient.getPatientName() + " ,手机号: " + patient.getPatientMobile()
                + " ,所选诊断: " + baseInquiryReqVO.getDiagnosis().stream().map(diagnosisItemVO -> {
                    return diagnosisItemVO.getDiagnosisName()+"("+diagnosisItemVO.getDiagnosisCode()+")";}).collect(Collectors.joining(","))
                +" ,预购药品: "+productInfos.stream().map(productInfo -> {
                    return productInfo.getCommonName()+"("+productInfo.getStandardId()+")";
                }).collect(Collectors.joining(","));
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析创建问诊节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("问诊创建 - 数据解析异常");
            return traceNodeData;
        }
    }



    /**
     * 问诊参数预检
     */
    private TraceNodeData preCheck(TraceNodeData traceNodeData) {
        InquiryRecordDto recordDto = getInquiryByBusinessData(traceNodeData,"inquiryDto");
        // 获取关联科室
        List<String> deptPrefList = recordDto.getChoiceDeptList();
        List<InquiryDeptDto> relevantDept = new ArrayList<>();
        if(!CollectionUtils.isEmpty(deptPrefList)){
            relevantDept =  inquiryDeptApi.listInquiryDeptByPref(deptPrefList);
        }
        String msg =  (CollectionUtils.isEmpty(deptPrefList) ? "空" : relevantDept.stream().map(InquiryDeptDto::getDeptName).collect(Collectors.joining(",")));
        String content = "问诊单: "+traceNodeData.getBusinessNo()+" ,根据诊断获取的关联科室为: " + msg;
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 医院选取节点
     */
    private TraceNodeData assignHospital(TraceNodeData traceNodeData) {
        InquiryRecordDto recordDto = getInquiryByBusinessData(traceNodeData,"inquiryDto");
        String content = "问诊单: "+traceNodeData.getBusinessNo()+" 调度本次接诊的医院为: "
            + recordDto.getHospitalName()+"("+recordDto.getHospitalName()+")"+" ,本次问诊在此医院的兜底接诊科室为: "
            + recordDto.getHospitalDeptDto().getInquiryDeptList().stream().filter(dept -> ObjectUtil.equals(dept.getDispatchDeptType(), DispatchDeptTypeEnum.DEFAULT_DEPARTMENT.getCode())).map(
                dept -> {return dept.getDeptName()+"("+dept.getDeptPref()+")";}
        ).collect(Collectors.joining(","));
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 分配科室节点
     */
    private TraceNodeData assignDept(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                traceNodeData.setBusinessData("科室分配 - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            InquiryRecordDto recordDto = getInquiryByBusinessData(traceNodeData,"inquiryDto");
            if (recordDto == null || recordDto.getHospitalDeptDto() == null) {
                traceNodeData.setBusinessData("科室分配 - 问诊数据不完整");
                return traceNodeData;
            }
            
            List<Dept> depts = recordDto.getHospitalDeptDto().getInquiryDeptList();
            String content = "问诊单: "+traceNodeData.getBusinessNo()+" ,可接诊关联科室: "
                        + depts.stream().filter(dept -> ObjectUtil.equals(dept.getDispatchDeptType(), DispatchDeptTypeEnum.DIAGNOSIS_DEPARTMENT.getCode())).map(
                dept -> {return dept.getDeptName()+"("+dept.getDeptPref()+")";}
            ).collect(Collectors.joining(","))
                +" ,可接诊兜底科室: "+ depts.stream().filter(dept -> ObjectUtil.equals(dept.getDispatchDeptType(), DispatchDeptTypeEnum.DEFAULT_DEPARTMENT.getCode())).map(
                dept -> {return dept.getDeptName()+"("+dept.getDeptPref()+")";}
            ).collect(Collectors.joining(","));
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析科室分配节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("科室分配 - 数据解析异常");
            return traceNodeData;
        }
    }

    /**
     * 自动开方判定节点
     */
    private TraceNodeData assignType(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                traceNodeData.setBusinessData("自动开方判定 - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            InquiryRecordDto recordDto = getInquiryByBusinessData(traceNodeData,"inquiryDto");
            if (recordDto == null) {
                traceNodeData.setBusinessData("自动开方判定 - 问诊数据不完整");
                return traceNodeData;
            }
            
            String content = "问诊单: "+traceNodeData.getBusinessNo()+" ,最终判定走："+
                (ObjectUtil.equals(recordDto.getAutoInquiry(), AutoInquiryEnum.YES.getCode()) ? "自动开方" : "真人问诊");
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析自动开方判定节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("自动开方判定 - 数据解析异常");
            return traceNodeData;
        }
    }

    /**
     * 自动开方判定节点
     */
    private TraceNodeData assignTypeDetail(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                traceNodeData.setBusinessData("判定条件详情 - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            JSONObject obj = JSONObject.parseObject(traceNodeData.getBusinessData());
            String content = "当前条件: "+ (obj.getBoolean("autoInquiry") ? "满足,走AI" : "不满足,走真人");
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析判定条件详情节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("判定条件详情 - 数据解析异常");
            return traceNodeData;
        }
    }

    /**
     * 持久化问诊单
     */
    private TraceNodeData saveInquiry(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                traceNodeData.setBusinessData("持久化问诊单 - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            String content = "问诊单: "+traceNodeData.getBusinessNo()+" ,相关患者信息、问诊单信息已写入库";
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析持久化问诊单节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("持久化问诊单 - 数据解析异常");
            return traceNodeData;
        }
    }

    /**
     * 患者取消问诊
     */
    private TraceNodeData cancelInquiry(TraceNodeData traceNodeData) {
        try {
            // 🔥 兼容性检查：如果是超长数据，直接返回占位符说明
            if (isOversizedDataPlaceholder(traceNodeData.getBusinessData())) {
                String action = ObjectUtil.equals(TraceNodeEnum.TIMEOUT_CANCEL_INQUIRY.getCode(), traceNodeData.getNodeCode()) 
                    ? "超时取消问诊" : "患者取消问诊";
                traceNodeData.setBusinessData(action + " - " + traceNodeData.getBusinessData());
                return traceNodeData;
            }
            
            String msg  = " ,患者取消问诊";
            if(ObjectUtil.equals(TraceNodeEnum.TIMEOUT_CANCEL_INQUIRY.getCode(), traceNodeData.getNodeCode())){
                msg = " ,问诊超时无医生接诊，系统取消";
            }
            String content = "问诊单: "+traceNodeData.getBusinessNo()+msg;
            traceNodeData.setBusinessData(content);
            return traceNodeData;
        } catch (Exception e) {
            // 🔥 解析异常时，返回友好的提示信息
            log.debug("解析取消问诊节点失败 - 业务号：{}，可能是数据格式异常", traceNodeData.getBusinessNo());
            traceNodeData.setBusinessData("取消问诊 - 数据解析异常");
            return traceNodeData;
        }
    }


    /**
     * 解析问诊超时取消节点
     */
    private TraceNodeData parseTimeoutCancelInquiry(TraceNodeData traceNodeData) {
        try {
            Map<String, Object> businessInfo = new HashMap<>();
            businessInfo.put("inquiryPref", traceNodeData.getBusinessNo());
            businessInfo.put("action", "超时取消问诊");
            businessInfo.put("cancelReason", "问诊超时未接诊");
            businessInfo.put("description", "问诊单在规定时间内未被医生接诊，系统自动取消");
            
            // TODO: 可以查询超时时长、配置的超时阈值等
            
            traceNodeData.setBusinessData(JSON.toJSONString(businessInfo));
            log.debug("解析问诊超时取消节点成功，businessNo: {}", traceNodeData.getBusinessNo());
        } catch (Exception e) {
            log.error("解析问诊超时取消节点失败", e);
        }
        return traceNodeData;
    }

} 