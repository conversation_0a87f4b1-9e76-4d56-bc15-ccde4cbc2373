package com.xyy.saas.inquiry.kernel.trace.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 链路追踪配置
 * 优化版本 - 确保高并发下不影响主业务性能
 */
@Configuration
@EnableAsync
@Slf4j
public class TraceNodeConfig {

    /**
     * 链路追踪数据异步保存线程池
     * 【关键优化】拒绝策略改为DiscardPolicy，确保绝不影响主业务线程
     */
    @Bean("traceNodeExecutor")
    public Executor traceNodeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数 - 根据CPU核心数动态调整，最小2个
        int coreSize = Math.max(2, Runtime.getRuntime().availableProcessors() / 4);
        executor.setCorePoolSize(coreSize);
        
        // 最大线程数 - 适度增加但不过多
        executor.setMaxPoolSize(coreSize * 3);
        
        // 队列大小 - 优化内存使用，避免过度占用内存（每个任务约1-5KB）
        executor.setQueueCapacity(500);
        
        // 线程前缀名
        executor.setThreadNamePrefix("trace-node-");
        
        // 线程空闲时间 - 延长生存时间，减少创建销毁开销
        executor.setKeepAliveSeconds(300);
        
        // 🔥 关键修改：拒绝策略改为DiscardPolicy
        // CallerRunsPolicy -> DiscardPolicy：队列满时直接丢弃，绝不影响调用线程
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                // 静默丢弃但记录统计信息，用于监控
                log.warn("链路追踪任务被丢弃：队列已满，当前队列大小：{}，活跃线程：{}", 
                    e.getQueue().size(), e.getActiveCount());
                super.rejectedExecution(r, e);
            }
        });
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待任务完成的超时时间
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化
        executor.initialize();
        
        log.info("链路追踪线程池初始化完成 - 核心线程数：{}，最大线程数：{}，队列容量：{}，拒绝策略：DiscardPolicy", 
            coreSize, coreSize * 3, 500);
            
        return executor;
    }
} 