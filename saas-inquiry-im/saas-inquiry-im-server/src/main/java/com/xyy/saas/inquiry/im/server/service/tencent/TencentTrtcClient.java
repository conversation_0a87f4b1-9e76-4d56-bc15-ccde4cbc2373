package com.xyy.saas.inquiry.im.server.service.tencent;

import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;

/**
 * @Author: xucao
 * @Date: 2024/12/10 14:16
 * @Description: 调腾讯TRTC相关接口
 */
public interface TencentTrtcClient {

    /**
     * 输入在线媒体流
     *
     * @param streamUrl 推流视频url
     * @param roomId    房间号
     * @param userId    用户id
     * @return 调用结果
     */
    Boolean startInputOnlineStream(String streamUrl, String roomId, String userId);

    /**
     * 混流转推到CDN
     *
     * @param inquiryRecordDto 问诊信息
     * @return taskId
     */
    String startPublishStreamCdn(InquiryRecordDto inquiryRecordDto, String userId,String streamId);

    /**
     * 停止混流转推到CDN
     *
     * @param taskId 转推任务id
     * @return 调用结果
     */
    void stopPublishStreamCdn(String taskId);

    /**
     * 获取文件id
     *
     * @param streamId 推流id
     * @return 文件id
     */
    TencentTrtcBaseRespDO getFileIdByStreamId(String streamId);

    /**
     * 开启转码
     *
     * @param fileId 文件id
     * @return taskId
     */
    TencentTrtcBaseRespDO openTranscoding(String fileId);


    /**
     * 根据转码任务taskId 查询转码结果
     * @param taskId 转码任务id
     * @return 调用结果
     */
    TencentTrtcBaseRespDO getTranscodingResult(String taskId);

    /**
     * 销毁直播间
     */
    void destroyRoom(String roomId);
}
