package com.xyy.saas.inquiry.im.server.service.trtc;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_CALL_BACK_MESSAGE_ERROR;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.im.server.service.trtc.strategy.trtccallback.TencentTrtcCallBackHandleStrategy;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/09 15:23
 * @Description: TRTC服务层
 */
@Service
@Slf4j
public class InquiryTrtcServiceImpl implements InquiryTrtcService {

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    /**
     * 回调处理策略
     */
    private static final Map<Integer, TencentTrtcCallBackHandleStrategy> callBackHandleStrategyHashMap = new HashMap();

    @Resource
    public void initHandler(List<TencentTrtcCallBackHandleStrategy> strategies) {
        strategies.forEach(strategy -> callBackHandleStrategyHashMap.put(strategy.getEvent().getEventType(), strategy));
    }




    /**
     * 房间与媒体流回调
     *
     * @param params 请求参数
     * @return 成功标识
     */
    @Override
    public Boolean tencentTrtcCallBack(TencentTrtcCallBackReqVO params) {
        if (ObjectUtils.isEmpty(params) || ObjectUtils.isEmpty(params.getEventType())) {
            throw exception(INQUIRY_TRTC_CALL_BACK_MESSAGE_ERROR);
        }
        TencentTrtcCallBackHandleStrategy strategy = callBackHandleStrategyHashMap.get(params.getEventType());
        if (ObjectUtils.isEmpty(strategy)) {
            log.info("TencentTrtcCallBackHandleStrategy is null,params:{}", JSON.toJSONString(params));
            return false;
        }
        return strategy.execute(params);
    }

    /**
     * 销毁房间
     *
     * @param roomId
     */
    @Override
    public void destroyRoom(String roomId) {
        tencentTrtcClient.destroyRoom(roomId);
    }
}
