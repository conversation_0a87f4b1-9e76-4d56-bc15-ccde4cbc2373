package com.xyy.saas.inquiry.im.server.service.trtc;

import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;

/**
 * @Author: xucao
 * @Date: 2024/12/09 15:21
 * @Description: TRTC服务
 */
public interface InquiryTrtcService {

    /**
     * TRTC事件回调
     *
     * @param params 请求参数
     * @return 成功标识
     */
    Boolean tencentTrtcCallBack(TencentTrtcCallBackReqVO params);

    /**
     * 销毁房间
     */
    void destroyRoom(String roomId);
}
