package com.xyy.saas.inquiry.im.server.api.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_USER_PARAM_ERROR;

import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserRespVO;
import com.xyy.saas.inquiry.im.server.service.user.InquiryImUserService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/20 16:13
 * @Description: IM用户接口实现类
 */
@DubboService
@Slf4j
public class InquiryImUserApiImpl implements InquiryImUserApi {

    @Resource
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private InquiryImUserService inquiryImUserService;

    /**
     * 根据医生编码获取医生IM账号
     *
     * @param doctorPref 医生编码
     * @return IM账号
     */
    @Override
    public String getDoctorImAccountByDoctorPref(String doctorPref) {
        log.info("根据医生编码获取医生IM账号 doctorPref:{}", doctorPref != null ? doctorPref : "null");
        InquiryDoctorDto doctorDto = inquiryDoctorApi.getInquiryDoctorByDoctorPref(doctorPref);
        log.info("根据医生编码获取医生IM账号 doctorDto:{}", doctorDto);
        // 后续考虑在此统一处理医生IM账号不存在的情况 ，暂时放再自动开方抢单处
        // InquiryImUserRespVO inquiryImUser = inquiryImUserService.getInquiryImUser(doctorDto.getUserId(), ClientChannelTypeEnum.APP);
        // return inquiryImUser.getAccountId();
        return inquiryImUserService.queryUserImAccountId(doctorDto.getUserId(), ClientChannelTypeEnum.APP);
    }

    @Override
    public List<String> getDoctorImAccountListByDoctorPrefList(List<String> doctorPrefList) {
        List<InquiryDoctorDto> doctorDtos = inquiryDoctorApi.getInquiryDoctorByPrefList(doctorPrefList);
        if (CollectionUtils.isEmpty(doctorDtos)) {
            return List.of();
        }
        return inquiryImUserService.queryUserImAccountList(doctorDtos.stream().map(InquiryDoctorDto::getUserId).distinct().toList(), ClientChannelTypeEnum.APP);
    }

    /**
     * 根据userId + 客户端类型 获取IM账号
     *
     * @param userId            用户id
     * @param clientChannelType 客户端类型
     * @return IM账号
     */
    @Override
    public String getImAccountByUserIdAndClientType(Long userId, Integer clientChannelType) {
        if (ObjectUtils.isEmpty(userId) || ObjectUtils.isEmpty(clientChannelType)) {
            throw exception(INQUIRY_IM_USER_PARAM_ERROR);
        }
        return inquiryImUserService.queryUserImAccountId(userId, ClientChannelTypeEnum.fromCode(clientChannelType));
    }

    @Override
    public String getAndCreateInquiryImUser(Long userId, ClientChannelTypeEnum clientChannelType) {
        InquiryImUserRespVO imUser = inquiryImUserService.getInquiryImUser(userId, clientChannelType, null);
        return imUser.getAccountId();
    }
}
