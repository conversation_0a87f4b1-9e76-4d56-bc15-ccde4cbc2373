package com.xyy.saas.inquiry.im.server.service.tencent.impl;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.trtc.v20190722.TrtcClient;
import com.tencentcloudapi.trtc.v20190722.models.AgentParams;
import com.tencentcloudapi.trtc.v20190722.models.AudioEncode;
import com.tencentcloudapi.trtc.v20190722.models.DismissRoomRequest;
import com.tencentcloudapi.trtc.v20190722.models.McuAudioParams;
import com.tencentcloudapi.trtc.v20190722.models.McuLayout;
import com.tencentcloudapi.trtc.v20190722.models.McuLayoutParams;
import com.tencentcloudapi.trtc.v20190722.models.McuPublishCdnParam;
import com.tencentcloudapi.trtc.v20190722.models.McuVideoParams;
import com.tencentcloudapi.trtc.v20190722.models.MixUserInfo;
import com.tencentcloudapi.trtc.v20190722.models.StartPublishCdnStreamRequest;
import com.tencentcloudapi.trtc.v20190722.models.StartStreamIngestRequest;
import com.tencentcloudapi.trtc.v20190722.models.StopPublishCdnStreamRequest;
import com.tencentcloudapi.trtc.v20190722.models.UserMediaStream;
import com.tencentcloudapi.trtc.v20190722.models.VideoEncode;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DescribeTaskDetailRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeTaskDetailResponse;
import com.tencentcloudapi.vod.v20180717.models.MediaInfo;
import com.tencentcloudapi.vod.v20180717.models.MediaProcessTaskInput;
import com.tencentcloudapi.vod.v20180717.models.ProcessMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.ProcessMediaResponse;
import com.tencentcloudapi.vod.v20180717.models.SearchMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.SearchMediaResponse;
import com.tencentcloudapi.vod.v20180717.models.TranscodeTaskInput;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.server.config.TencentTrtcConfig;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.im.server.util.tencent.TrtcUtil;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.function.ThrowingFunction;

/**
 * @Author: xucao
 * @Date: 2024/12/10 14:18
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Service
@Slf4j
public class TencentTrtcClientImpl implements TencentTrtcClient {

    @Autowired
    private TencentTrtcConfig tencentTrtcConfig;

    /**
     * 输入在线媒体流
     *
     * @param streamUrl 直播流地址
     * @param roomId    房间号
     * @param userId    用户id
     * @return 调用结果
     */
    @Override
    public Boolean startInputOnlineStream(String streamUrl, String roomId, String userId) {
        log.info("执行拉流转推:roomId:{} ,userId:{},streamUrl:{}", roomId, userId, streamUrl);
        // 获取请求client
        TrtcClient client = getTrtcClient();
        // 获取请求参数
        StartStreamIngestRequest req = getStartStreamIngestRequest(streamUrl, roomId, userId);
        // 调用腾讯云sdk,返回任务ID
        String taskId = callTencentTrtcFunction(req, client::StartStreamIngest);
        return StringUtils.isNotBlank(taskId);
    }

    /**
     * 混流转推到CDN
     *
     * @param inquiryRecordDto 问诊信息
     * @return 调用结果
     */
    @Override
    public String startPublishStreamCdn(InquiryRecordDto inquiryRecordDto , String userId,String streamId) {
        log.info("trtc,执行混流转推请求:roomId:{} ,userId:{},doctorUserId:{}", inquiryRecordDto.getPref(), inquiryRecordDto.getCreator(), userId);
        // 获取请求client
        TrtcClient client = getTrtcClient();
        // 获取请求参数
        StartPublishCdnStreamRequest req = getStartPublishCdnStreamRequest(inquiryRecordDto,userId,streamId);
        // 调用腾讯云sdk,返回任务ID
        return callTencentTrtcFunction(req, client::StartPublishCdnStream);
    }

    /**
     * 停止混流转推到CDN
     *
     * @param taskId 转推任务id
     */
    @Override
    public void stopPublishStreamCdn(String taskId) {
        log.info("trtc,停止云端混流，taskId:{}", taskId);
        // 获取请求client
        TrtcClient client = getTrtcClient();
        // 获取请求参数
        StopPublishCdnStreamRequest req = getStopPublishCdnStreamRequest(taskId);
        // 调用腾讯云sdk,返回任务ID
        callTencentTrtcFunction(req, client::StopPublishCdnStream);
    }

    /**
     * 获取文件id
     *
     * @param streamId 推流id
     * @return 文件id
     */
    @Override
    public TencentTrtcBaseRespDO getFileIdByStreamId(String streamId) {
        try{
            //获取点播的client
            VodClient client = getVodClient();
            //构造请求sdk参数
            SearchMediaRequest req = new SearchMediaRequest();
            req.setStreamId(streamId);
            SearchMediaResponse resp = client.SearchMedia(req);
            log.info("SearchMedia#doSearchMedia -> 查询混流，获取fileId入参：{}，响应： {}",JSON.toJSONString(req),JSON.toJSONString(resp));
            MediaInfo[] mediaInfoSet = resp.getMediaInfoSet();
            if(mediaInfoSet.length > 0){
               return TencentTrtcBaseRespDO.builder().fileId(mediaInfoSet[0].getFileId()).build();
            }
            return TencentTrtcBaseRespDO.builder().errorInfo("根据streamId未查询到流文件").build();
        } catch (TencentCloudSDKException e) {
            log.error("ERROR::getFileIdByStreamId#SearchMedia -> 查询混流，获取fileId异常 ",e);
            return TencentTrtcBaseRespDO.builder().errorInfo(e.getErrorCode()+"-"+e.getMessage()).build();
        }
    }

    /**
     * 开启转码
     *
     * @param fileId 文件id
     * @return TencentTrtcBaseRespDO
     */
    @Override
    public TencentTrtcBaseRespDO openTranscoding(String fileId) {
        try{
            //获取点播的client
            VodClient client = getVodClient();
            //构造请求sdk参数
            MediaProcessTaskInput mediaProcessTaskInput = new MediaProcessTaskInput();
            TranscodeTaskInput[] transcodeTaskSet = new TranscodeTaskInput[1];
            transcodeTaskSet[0] = new TranscodeTaskInput();
            transcodeTaskSet[0].setDefinition(tencentTrtcConfig.getVod().getTranscodingTemplateId());
            mediaProcessTaskInput.setTranscodeTaskSet(transcodeTaskSet);
            ProcessMediaRequest req = new ProcessMediaRequest();
            req.setFileId(fileId);
            req.setMediaProcessTask(mediaProcessTaskInput);
            ProcessMediaResponse resp = client.ProcessMedia(req);
            return TencentTrtcBaseRespDO.builder().taskId(resp.getTaskId()).build();
        } catch (TencentCloudSDKException e) {
            log.error("ERROR::openTranscoding#ProcessMedia -> 开启混流转码失败",e);
            return TencentTrtcBaseRespDO.builder().errorInfo(e.getErrorCode()+"-"+e.getMessage()).build();
        }
    }

    /**
     * 根据转码任务taskId 查询转码结果
     *
     * @param taskId 转码任务id
     * @return 调用结果
     */
    @Override
    public TencentTrtcBaseRespDO getTranscodingResult(String taskId) {
        try{
            //获取点播的client
            VodClient client = getVodClient();
            //构造请求sdk参数
            DescribeTaskDetailRequest req = new DescribeTaskDetailRequest();
            req.setTaskId(taskId);
            DescribeTaskDetailResponse resp = client.DescribeTaskDetail(req);
            return TencentTrtcBaseRespDO.builder().mp4Url(Optional.ofNullable(resp.getProcedureTask().getFileUrl()).orElse("")).build();
        } catch (TencentCloudSDKException e) {
            log.error("ERROR::getTranscodingResult#DescribeTaskDetail -> 获取转码结果失败",e);
            return TencentTrtcBaseRespDO.builder().errorInfo(e.getErrorCode()+"-"+e.getMessage()).build();
        }
    }

    /**
     * 销毁直播间
     *
     * @param roomId
     */
    @Override
    public void destroyRoom(String roomId) {
        log.info("trtc,销毁直播间，roomId:{}", roomId);
        if(StringUtils.isBlank(roomId)){
            return;
        }
        try {
            // 获取请求client
            TrtcClient client = getTrtcClient();
            // 获取请求参数
            DismissRoomRequest req = getDismissRoomRequest(roomId);
            // 调用腾讯云sdk,返回任务ID
            callTencentTrtcFunction(req, client::DismissRoom);
        }catch (Exception e){
            log.error("ERROR::destroyRoom#DismissRoom -> 销毁直播间失败",e);
        }
    }

    /**
     * 调用腾讯云sdk通用方法
     *
     * @param req      请求对象
     * @param function 函数
     */
    public <Req extends AbstractModel, Resp extends AbstractModel> String callTencentTrtcFunction(
        Req req,
        ThrowingFunction<Req, Resp> function) {
        log.info("trtc,请求腾讯云入参: {}", JSON.toJSONString(req));
        Resp resp;
        // 调用传入的函数并获取响应
        resp = function.apply(req);
        log.info("trtc,腾讯云返参: {}", JSON.toJSONString(resp));
        // 检查响应是否为空或任务ID是否无效
        return getRespParam(resp);
    }

    private String getRespParam(Object resp) {
        try {
            Field taskIdField = getField(resp, "TaskId");
            if (taskIdField != null) {
                taskIdField.setAccessible(true);
                return (String) taskIdField.get(resp);
            }
            // 如果 TaskId 不存在，则检查 RequestId
            Field requestIdField = getField(resp, "RequestId");
            if (requestIdField != null) {
                requestIdField.setAccessible(true);
                return (String) requestIdField.get(resp);
            }
            return null;
        } catch (IllegalAccessException e) {
            log.error("Failed to access field in resp object", e);
            return null;
        }
    }

    private Field getField(Object obj, String fieldName) {
        try {
            return obj.getClass().getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }

    /**
     * 获取拉流转推请求参数
     *
     * @param streamUrl 直播流地址
     * @param roomId    房间id
     * @param userId    用户id
     * @return 拉流转推请求对象
     */
    private StartStreamIngestRequest getStartStreamIngestRequest(String streamUrl, String roomId, String userId) {
        StartStreamIngestRequest req = new StartStreamIngestRequest();
        req.setSdkAppId(tencentTrtcConfig.getRav().getSdkAppId());
        req.setStreamUrl(streamUrl);
        req.setRoomId(roomId);
        //这里1表示整形
        req.setRoomIdType(1L);
        req.setUserId(userId);
        req.setUserSig(TrtcUtil.getSign(userId));
        return req;
    }

    /**
     * 获取停止转推请求参数
     *
     * @param taskId 转推任务id
     * @return 返回参数
     */
    private StopPublishCdnStreamRequest getStopPublishCdnStreamRequest(String taskId) {
        StopPublishCdnStreamRequest req = new StopPublishCdnStreamRequest();
        req.setSdkAppId(tencentTrtcConfig.getRav().getSdkAppId());
        req.setTaskId(taskId);
        return req;
    }

    /**
     * 获取销毁直播间的参数
     *
     * @param roomId 房间id
     * @return 返回参数
     */
    private DismissRoomRequest getDismissRoomRequest(String roomId) {
        DismissRoomRequest req = new DismissRoomRequest();
        req.setRoomId(Long.parseLong(roomId));
        req.setSdkAppId(tencentTrtcConfig.getRav().getSdkAppId());
        return req;
    }

    /**
     * 获取混流转推请求参数
     *
     * @param inquiryDto 问诊单信息
     * @return 混流转推请求参数
     */
    private StartPublishCdnStreamRequest getStartPublishCdnStreamRequest(InquiryRecordDto inquiryDto , String userId,String streamId) {
        //创建请求对象
        StartPublishCdnStreamRequest request = new StartPublishCdnStreamRequest();
        request.setSdkAppId(tencentTrtcConfig.getRav().getSdkAppId());
        request.setRoomId(inquiryDto.getPref());
        //这里0表示整形
        request.setRoomIdType(0L);
        //转推服务加入TRTC房间的机器人参数
        AgentParams agentParams = new AgentParams();
        String agentUserId = "rtmp_" + inquiryDto.getPref() + "_" + inquiryDto.getCreator();
        agentParams.setUserId(agentUserId);
        agentParams.setUserSig(TrtcUtil.getSign(agentUserId));
        //设置机器人信息
        request.setAgentParams(agentParams);
        //开启混流转推
        request.setWithTranscoding(1L);
        //音频编码参数
        McuAudioParams mcuAudioParams = new McuAudioParams();
        AudioEncode audioEncode = new AudioEncode();
        audioEncode.setSampleRate(48000L);
        audioEncode.setChannel(2L);
        audioEncode.setBitRate(64L);
        mcuAudioParams.setAudioEncode(audioEncode);
        //设置音频编码参数
        request.setAudioParams(mcuAudioParams);
        //视频相关参数
        McuVideoParams videoParams = new McuVideoParams();
        //视频编码参数
        VideoEncode videoEncode = new VideoEncode();
        videoEncode.setBitRate(512L);
        videoEncode.setFps(15L);
        videoEncode.setGop(2L);
        videoEncode.setWidth(368L);
        videoEncode.setHeight(640L);
        videoParams.setVideoEncode(videoEncode);
        //混流布局参数
        McuLayoutParams layoutParams = new McuLayoutParams();
        layoutParams.setMixLayoutMode(4L);
        layoutParams.setMixLayoutList(getMixLayoutList(inquiryDto,userId));
        videoParams.setLayoutParams(layoutParams);
        //设置视频编码参数
        request.setVideoParams(videoParams);
        //设置推到cdn的参数
        request.setPublishCdnParams(getPublishCdnParams(streamId));
        return request;
    }

    /**
     * 获取推到cdn的参数
     *
     * @return 转推参数
     */
    private McuPublishCdnParam[] getPublishCdnParams(String streamId) {
        McuPublishCdnParam[] publishCdns = new McuPublishCdnParam[1];
        McuPublishCdnParam publish1 = new McuPublishCdnParam();
        publish1.setIsTencentCdn(1L);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 将当前时间加上24小时
        LocalDateTime after24Hours = now.plusHours(24);
        long txTime = after24Hours.atZone(ZoneId.systemDefault()).toEpochSecond();
        String safeUrl = getSafeUrl(tencentTrtcConfig.getLive().getApiKey(), streamId, txTime);
        String publishUrl = "rtmp://" + tencentTrtcConfig.getLive().getPushDomain() + "/trtc_" + tencentTrtcConfig.getRav().getSdkAppId() + "/" + streamId + safeUrl;
        publish1.setPublishCdnUrl(publishUrl);
        publishCdns[0] = publish1;
        return publishCdns;
    }

    private static String byteArrayToHexString(byte[] data) {
        char[] out = new char[data.length << 1];

        for (int i = 0, j = 0; i < data.length; i++) {
            out[j++] = DIGITS_LOWER[(0xF0 & data[i]) >>> 4];
            out[j++] = DIGITS_LOWER[0x0F & data[i]];
        }
        return new String(out);
    }

    private static final char[] DIGITS_LOWER =
        {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /*
     * 生成防盗链参数
     * KEY+ streamName + txTime
     */
    private String getSafeUrl(String key, String streamName, long txTime) {
        String input = key
            + streamName
            + Long.toHexString(txTime).toUpperCase();

        String txSecret = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            txSecret = byteArrayToHexString(
                messageDigest.digest(input.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return txSecret == null ? "" :
            "?txSecret="
                + txSecret
                + "&"
                + "txTime="
                + Long.toHexString(txTime).toUpperCase();
    }



    /**
     * 获取混流布局参数
     *
     * @param  inquiryDto 问诊单信息
     * @return 混流布局参数数组
     */
    private static McuLayout[] getMixLayoutList(InquiryRecordDto inquiryDto,String userId) {
        McuLayout[] layouts = new McuLayout[2];
        McuLayout layout1 = new McuLayout();
        layout1.setImageHeight(640L);
        layout1.setImageWidth(368L);
        UserMediaStream phyMediaStream = new UserMediaStream();
        phyMediaStream.setStreamType(0L);
        MixUserInfo phyUser = new MixUserInfo();
        phyUser.setRoomId(inquiryDto.getPref());
        phyUser.setRoomIdType(0L);
        String phyId = userId;
        if(inquiryDto.isVideoAutoInquiry()){
            //视频自动开方取拉流转推机器人的userId
            phyId = inquiryDto.getPref()+"@"+userId;
        }
        phyUser.setUserId(phyId);
        phyMediaStream.setUserInfo(phyUser);
        layout1.setUserMediaStream(phyMediaStream);
        layouts[0] = layout1;

        McuLayout layout2 = new McuLayout();
        layout2.setImageHeight(160L);
        layout2.setImageWidth(90L);
        layout2.setLocationY(430L);
        layout2.setLocationX(278L);
        UserMediaStream patMediaStream = new UserMediaStream();
        patMediaStream.setStreamType(0L);
        MixUserInfo patUser = new MixUserInfo();
        patUser.setRoomId(inquiryDto.getPref());
        patUser.setRoomIdType(0L);
        patUser.setUserId(inquiryDto.getCreator());
        patMediaStream.setUserInfo(patUser);
        layout2.setUserMediaStream(patMediaStream);
        layouts[1] = layout2;
        return layouts;
    }



    /**
     * 获取云点播client
     *
     * @return VodClient
     */
    public VodClient getVodClient() {
        Credential cred = new Credential(tencentTrtcConfig.getVod().getSecretId(), tencentTrtcConfig.getVod().getSecretKey());
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(tencentTrtcConfig.getVod().getEndPoint());
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        return new VodClient(cred, "", clientProfile);
    }


    /**
     * 获取请求client
     *
     * @return TrtcClient
     */
    public TrtcClient getTrtcClient() {
        Credential cred = new Credential(tencentTrtcConfig.getVod().getSecretId(), tencentTrtcConfig.getVod().getSecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(tencentTrtcConfig.getRav().getEndPoint());
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        return new TrtcClient(cred, tencentTrtcConfig.getRav().getRegion(), clientProfile);
    }
}
