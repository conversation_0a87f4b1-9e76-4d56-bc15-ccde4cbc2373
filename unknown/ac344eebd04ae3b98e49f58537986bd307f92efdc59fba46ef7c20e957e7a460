package com.xyy.saas.inquiry.product.server.controller.admin.gsp;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 质量变更申请操作记录")
@RestController
@RequestMapping("/product/quality-change")
@Validated
public class ProductQualityChangeRecordController {

    @Resource
    private ProductQualityChangeRecordService productQualityChangeRecordService;

    @PostMapping("/save")
    @Operation(summary = "暂存/提交质量变更申请")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:create')")
    public CommonResult<Long> submitQualityChange(@Valid @RequestBody ProductQualityChangeRecordSaveReqVO reqVO) {
        return success(productQualityChangeRecordService.saveOrUpdateQualityChange(reqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除质量变更申请操作记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:delete')")
    public CommonResult<Boolean> deleteProductQualityChangeRecord(@RequestParam("id") Long id) {
        productQualityChangeRecordService.deleteProductQualityChangeRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得质量变更申请操作记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:query')")
    public CommonResult<ProductQualityChangeRecordRespVO> getProductQualityChangeRecord(@RequestParam("id") Long id) {
        ProductQualityChangeRecordDO productQualityChangeRecord = productQualityChangeRecordService.getProductQualityChangeRecord(id);
        return success(BeanUtils.toBean(productQualityChangeRecord, ProductQualityChangeRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得质量变更申请操作记录分页")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:query')")
    public CommonResult<PageResult<ProductQualityChangeRecordRespVO>> getProductQualityChangeRecordPage(@Valid ProductQualityChangeRecordPageReqVO pageReqVO) {
        PageResult<ProductQualityChangeRecordDO> pageResult = productQualityChangeRecordService.getProductQualityChangeRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductQualityChangeRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出质量变更申请操作记录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductQualityChangeRecordExcel(@Valid ProductQualityChangeRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductQualityChangeRecordDO> list = productQualityChangeRecordService.getProductQualityChangeRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "质量变更申请操作记录.xls", "数据", ProductQualityChangeRecordRespVO.class,
                        BeanUtils.toBean(list, ProductQualityChangeRecordRespVO.class));
    }

    // ==================== 子表（质量变更申请明细记录） ====================

    @GetMapping("/product-quality-change-detail/page")
    @Operation(summary = "获得质量变更申请明细记录分页")
    @Parameter(name = "recordId", description = "单据ID")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:query')")
    public CommonResult<PageResult<ProductQualityChangeDetailDO>> getProductQualityChangeDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("recordPref") String recordPref) {
        return success(productQualityChangeRecordService.getProductQualityChangeDetailPage(pageReqVO, recordPref));
    }

    @PostMapping("/product-quality-change-detail/create")
    @Operation(summary = "创建质量变更申请明细记录")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:create')")
    public CommonResult<Long> createProductQualityChangeDetail(@Valid @RequestBody ProductQualityChangeDetailDO productQualityChangeDetail) {
        return success(productQualityChangeRecordService.createProductQualityChangeDetail(productQualityChangeDetail));
    }

    @PutMapping("/product-quality-change-detail/update")
    @Operation(summary = "更新质量变更申请明细记录")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:update')")
    public CommonResult<Boolean> updateProductQualityChangeDetail(@Valid @RequestBody ProductQualityChangeDetailDO productQualityChangeDetail) {
        productQualityChangeRecordService.updateProductQualityChangeDetail(productQualityChangeDetail);
        return success(true);
    }

    @DeleteMapping("/product-quality-change-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除质量变更申请明细记录")
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:delete')")
    public CommonResult<Boolean> deleteProductQualityChangeDetail(@RequestParam("id") Long id) {
        productQualityChangeRecordService.deleteProductQualityChangeDetail(id);
        return success(true);
    }

	@GetMapping("/product-quality-change-detail/get")
	@Operation(summary = "获得质量变更申请明细记录")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:quality-change:query')")
	public CommonResult<ProductQualityChangeDetailDO> getProductQualityChangeDetail(@RequestParam("id") Long id) {
	    return success(productQualityChangeRecordService.getProductQualityChangeDetail(id));
	}


}