package com.xyy.saas.inquiry.product.api.search.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/25 20:41
 */
@Schema(description = "App - 问诊商品诊断搜索 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryDiagnosticsSearchReqDto extends PageParam {

    @Schema(description = "商品搜索信息集合")
    private List<InquiryProductSearchReqDto> productSearchList;

}
