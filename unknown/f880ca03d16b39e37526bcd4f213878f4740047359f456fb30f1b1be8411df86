package com.xyy.saas.inquiry.pojo.prescription;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/02/11 18:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "外配处方扩展信息ext")
public class PrescriptionExternalExtDto implements Serializable {

    @Schema(description = "电子处方审核意见")
    private String rxChkOpinions;


}
