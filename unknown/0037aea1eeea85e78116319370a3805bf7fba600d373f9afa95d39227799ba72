package com.xyy.saas.inquiry.product.server.controller.admin.transfer;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 商品流转记录")
@RestController
@RequestMapping("/product/transfer")
@Validated
public class ProductTransferRecordController {

    @Resource
    private ProductTransferRecordService productTransferRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建商品流转记录")
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:create')")
    public CommonResult<Long> createProductTransferRecord(@Valid @RequestBody ProductTransferRecordSaveReqVO createReqVO) {
        return success(productTransferRecordService.createProductTransferRecord(createReqVO).getId());
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品流转记录")
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:update')")
    public CommonResult<Boolean> updateProductTransferRecord(@Valid @RequestBody ProductTransferRecordSaveReqVO updateReqVO) {
        productTransferRecordService.updateProductTransferRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品流转记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:delete')")
    public CommonResult<Boolean> deleteProductTransferRecord(@RequestParam("id") Long id) {
        productTransferRecordService.deleteProductTransferRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品流转记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:query')")
    public CommonResult<ProductTransferRecordRespVO> getProductTransferRecord(@RequestParam("id") Long id) {
        ProductTransferRecordDO productTransferRecord = productTransferRecordService.getProductTransferRecord(id);
        return success(BeanUtils.toBean(productTransferRecord, ProductTransferRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品流转记录分页")
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:query')")
    public CommonResult<PageResult<ProductTransferRecordRespVO>> getProductTransferRecordPage(@Valid ProductTransferRecordPageReqVO pageReqVO) {
        PageResult<ProductTransferRecordDO> pageResult = productTransferRecordService.getProductTransferRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductTransferRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品流转记录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:transfer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductTransferRecordExcel(@Valid ProductTransferRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductTransferRecordDO> list = productTransferRecordService.getProductTransferRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品流转记录.xls", "数据", ProductTransferRecordRespVO.class,
                        BeanUtils.toBean(list, ProductTransferRecordRespVO.class));
    }

}