# Skywalking 参数优化建议

## 当前问题分析

通过对比正常状态和 CPU 100% 状态的线程栈日志，发现以下问题：

1. Skywalking 参数配置过于严格：
   - `-Dskywalking.agent.sample_n_per_3_secs=3` 过低
   - `-Dskywalking.agent.trace_segment_ref_limit_per_span=3` 过低

2. ThreadPoolManager 使用不当，在 RedisUtils 中可能创建过多线程任务

## 优化建议

### 1. Skywalking Agent 参数调整

建议将以下参数添加到 JVM 启动参数中：

```bash
-Dskywalking.agent.sample_n_per_3_secs=5
-Dskywalking.agent.trace_segment_ref_limit_per_span=100
```

参数说明：
- `sample_n_per_3_secs`：每3秒采样5个trace，平衡监控数据完整性和系统性能
- `trace_segment_ref_limit_per_span`：每个span最多引用100个trace segment，避免因限制过严导致的数据丢失和重试

### 2. ThreadPoolManager 使用优化

在 RedisUtils 中，需要确保线程任务的合理控制，避免创建过多的续期线程。

### 3. 监控建议

1. 定期检查 Skywalking Agent 日志，观察是否有 trace 数据丢失
2. 监控 ThreadPoolManager 的线程使用情况
3. 观察系统 CPU 和内存使用情况，确保参数调整后系统稳定