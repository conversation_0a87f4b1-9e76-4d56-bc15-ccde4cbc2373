###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.查门店
GET {{baseAdminSystemUrl}}/system/tenant/page?pageNo=1&pageSize=10&pref=MD104714
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}


> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("未查询到门店信息");
  }

  console.log(new Date('2025-01-01').getTime())

  client.global.set("openTenantId", response.body.data.list[0].id);

%}


### 3.查套餐
GET {{baseAdminSystemUrl}}/system/tenant-package/page?pageNo=1&pageSize=10&pref=TC110034
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("未查询到套餐信息");
  }
  client.global.set("openPackageId", response.body.data.list[0].id);
  client.global.set("startTime", new Date('2025-01-01').getTime());
  client.global.set("endTime", new Date('2026-01-01').getTime());
  client.global.set("signTime", new Date().getTime());

%}

### 4.开通套餐
POST {{baseAdminSystemUrl}}/system/tenant-package-relation/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}


{
  "tenantId": {{openTenantId}},
  "packageId": {{openPackageId}},
  "packageNature": 1,
  "startTime": {{startTime}},
  "endTime": {{endTime}},
  "signTime": {{signTime}},
  "paymentType": 0,
  "signChannel": 100,
  "payVoucherUrls": [],
  "hospitalPrefs": [],
  "platformReview": 1,
  "inquiryPackageItems": [],
  "item": null
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

