
### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "15926351112",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}

### 问诊单配置-超适应症审查-区域-保存 （武汉市）
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "optionType": 10001,
  "area": 420100
}


### 问诊单配置-超适应症审查-区域-分页查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/page?pageNo=1&pageSize=500&optionType=30009
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 问诊流程配置-问诊合规配置-区域-保存 （武汉市东西湖区）
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "optionType": 30009,
  "areas": [
    500102
  ],
  "presAllRealPeopleInquiryHospitalPrefs": [
    "H100008",
    "H000002"
  ]
}


### 问诊流程配置-问诊合规配置-区域-分页查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/page?pageNo=1&pageSize=10&optionType=20004&provinceCode=420000&cityCode=420100&areaCode=420112
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  if (response.body.data.list.length > 0) {
    client.global.set("id", response.body.data.list[0].id);
  }
%}


### 问诊流程配置-问诊合规配置-区域-批量删除
DELETE {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/delete?ids={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 问诊流程配置-问诊合规配置-区域-查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/area/get?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 查询门店列表
GET {{baseAdminKernelUrl}}/system/tenant/page
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  if (response.body.data.list.length > 0) {
    client.global.set("tenantId", "" + response.body.data.list[0].id);
  }
%}


### 问诊单配置-超适应症审查-门店-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "optionType": 10001,
  "tenantId": {{tenantId}}
}


### 问诊单配置-超适应症审查-门店-分页查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/page?pageNo=1&pageSize=10&optionType=10001
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 问诊流程配置-医生接诊默认页面-门店-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "optionType": 20009,
  "tenantId": {{tenantId}},
  "procDoctorAdmissionDefaultPage": "/doctor/check/check-result"
}


### 问诊流程配置-医生接诊默认页面-门店-分页查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/page?pageNo=1&pageSize=10&optionType=20009
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  if (response.body.data.list.length > 0) {
    client.global.set("id", response.body.data.list[0].id);
  }
%}


### 问诊流程配置-医生接诊默认页面-门店-查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/get?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}


### 问诊流程配置-医生接诊默认页面-门店-单个删除
DELETE {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/store/delete?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 全局配置查询
GET {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/query
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}



### 问诊流程配置-问诊合规配置-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "procInquiryCompliance": true,
  "procInquiryComplianceForPatientAgeGe": 15,
  "procInquiryComplianceForPatientAgeLt": 60,
  "procInquiryComplianceAllowForPregnancyLactation": false
}



### 问诊流程配置-图文交互对话弹出速度-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "procTeletextInteractionDialogPopupSpeed": 500
}



### 问诊流程配置-问诊服务开关-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "procInquiryServiceSwitch": true
}


### 问诊流程配置-医生接诊默认页面-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "procDoctorAdmissionDefaultPage": "/act/check/check-result"
}



### 医生开方配置-开方基础属性配置-保存
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "presGlobalDateFormat": "yyyyMMdd HH:mm:ss",
  "presTeletextInquiryDoctorOrderMax": 5,
  "presDoctor2ndConfirmDialog": true,
  "presSignatureUseInterval": 30,
  "presDoctorCannotInquiryArea": "420100",
  "presAutoInquiryToRealForAllergic": true,
  "presAutoInquiryToRealForLiverRenalDysfunction": true,
  "presAutoInquiryToRealForPregnancyLactation": true,
  "presAutoInquiryToRealForMultiDiagnose": true,
  "presAutoInquiryToRealForMultiDiagnoseRatio": 50,
  "presAutoInquiryToRealForMultiDiagnoseConditions": [
    {
      "and": true,
      "conditions": [
        {
          "and": true,
          "rules": [
            {
              "type": "age",
              "op": "ge",
              "value": "18"
            },
            {
              "type": "age",
              "op": "le",
              "value": "65"
            },
            {
              "type": "area",
              "op": "eq",
              "value": "420100"
            }
          ]
        }
      ]
    }
  ],
  "presAutoInquiryToRealForSpecialAgeRange": true,
  "presAutoInquiryToRealForSpecialAgeRangeRatio": 50,
  "presAutoInquiryToRealForSpecialAgeRangeConditions": [
    {
      "and": true,
      "conditions": [
        {
          "and": true,
          "rules": [
            {
              "type": "age",
              "op": "ge",
              "value": "18"
            },
            {
              "type": "age",
              "op": "le",
              "value": "65"
            }
          ]
        }
      ]
    }
  ],
  "presAutoInquiryReturnToReal": true,
  "presAutoInquiryReturnToRealBacklogNum": 700,
  "presAutoInquiryReturnToRealRatio": 50
}

### 医生开方配置-开方基础属性配置-保存（单个属性）
POST {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/global/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "presAutoInquiryToRealForMultiDiagnose": true
}

### 医生开方配置-开方基础属性配置
GET  {{baseAdminKernelUrl}}/kernel/drugstore/base-info/get-inquiry-rule-config?tenantId=1927570194348843010
Accept: application/json
Authorization: Bearer {{token}}



###
GET  {{baseAdminKernelUrl}}/kernel/drugstore/inquiry-option-config/page-tenant?pageNo=1&pageSize=10&optionType=20012&name=北京荷叶大药房1店
Accept: application/json
Authorization: Bearer {{token}}
