package com.xyy.saas.inquiry.product.api.product.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.xyy.saas.inquiry.annotation.FieldCompare;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProductInfoDto implements Serializable {


    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21893")
    private Long id;

    @Schema(description = "多租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21893")
    private Long tenantId;

    @Schema(description = "多租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21893")
    private Long headTenantId;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "商品外码（自动生成或填写，机构唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String showPref;

    @Schema(description = "源商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceProductPref;

    @Schema(description = "源商品外码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceProductShowPref;

    @Schema(description = "中台标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    @FieldCompare(description = "中台标准库ID")
    private Long midStdlibId;

    @Schema(description = "标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    @FieldCompare(description = "标准库ID")
    private Long stdlibId;

    @Schema(description = "助记码", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "助记码")
    private String mnemonicCode;

    @Schema(description = "通用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @FieldCompare(description = "通用名")
    private String commonName;

    @Schema(description = "品牌名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @FieldCompare(description = "品牌名")
    private String brandName;

    @Schema(description = "规格/型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "规格/型号")
    private String spec;

    @Schema(description = "条形码（基本单位条形码）", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "条形码")
    private String barcode;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "商品封面图片链接")
    @FieldCompare(description = "商品封面图")
    private List<String> coverImages;

    @Schema(description = "商品外包装图片链接")
    @FieldCompare(description = "商品外包装图")
    private List<String> outerPackageImages;

    @Schema(description = "商品说明书图片链接")
    @FieldCompare(description = "商品说明书图")
    private List<String> instructionImages;

    @Schema(description = "最小包装数量")
    @FieldCompare(description = "最小包装数量")
    private BigDecimal minPackageNum;

    @Schema(description = "商品大类")
    @FieldCompare(description = "商品大类")
    private String spuCategory;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "23148")
    @FieldCompare(description = "单位")
    private String unit;

    @Schema(description = "剂型", requiredMode = Schema.RequiredMode.REQUIRED, example = "21146")
    @FieldCompare(description = "剂型")
    private String dosageForm;

    @Schema(description = "所属范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "6843")
    @FieldCompare(description = "所属范围")
    private String businessScope;

    @Schema(description = "处方分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "8689")
    @FieldCompare(description = "处方分类")
    private String presCategory;

    @Schema(description = "储存条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1588")
    @FieldCompare(description = "储存条件")
    private String storageWay;

    @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "产地")
    private String origin;

    @Schema(description = "生产企业社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "生产企业社会信用代码")
    private String manufacturerUscc;

    @Schema(description = "有效期,例12个月,36个月", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "有效期")
    private String productValidity;

    @Schema(description = "批准文号有效期")
    @FieldCompare(description = "批准文号有效期")
    private LocalDate approvalValidityPeriod;

    @Schema(description = "上市许可持有人", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "上市许可持有人")
    private String marketingAuthorityHolder;

    @Schema(description = "药品标识码", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "药品标识码")
    private String drugIdentCode;

    @Schema(description = "用药方式")
    @FieldCompare(description = "用药方式")
    private String usageMethod;
    @Schema(description = "用药频次")
    @FieldCompare(description = "用药频次")
    private String usageFrequency;
    @Schema(description = "单次使用剂量")
    @FieldCompare(description = "单次使用剂量")
    private String singleDosage;
    @Schema(description = "单次使用剂量单位")
    @FieldCompare(description = "单次使用剂量单位")
    private String singleDosageUnit;

    @Schema(description = "一级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "一级分类")
    private String firstCategory;
    @Schema(description = "二级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "二级分类")
    private String secondCategory;
    @Schema(description = "三级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "六级分类")
    private String sixCategory;

    @Schema(description = "进项税率")
    @FieldCompare(description = "进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    @FieldCompare(description = "销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    private String remark;

    @Schema(description = "拆零数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer unbundledQuantity;
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;
    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Boolean disable;
    @Schema(description = "删除时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    private LocalDateTime deletedAt;
    @Schema(description = "删除类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer deleteType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    private LocalDateTime createTime;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    private LocalDateTime updateTime;
    @Schema(description = "创建人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long creator;
    @Schema(description = "创建人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creatorName;
    @Schema(description = "更新人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long updater;
    @Schema(description = "更新人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updaterName;


    @Schema(description = "多属性标志")
    private Long multiFlag;

    // 多属性标志
    @Schema(description = "多属性标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "多属性标志", recursive = true)
    private ProductFlag productFlag;


    // 标准库信息
    // @Schema(description = "标准库信息", requiredMode = RequiredMode.REQUIRED, example = "{}")
    // @FieldCompare(description = "标准库信息", recursive = true)
    // private ProductStdlibDto stdlib;

    // 使用信息（价格，医保匹配信息）
    @Schema(description = "使用信息（价格，医保匹配信息）", requiredMode = RequiredMode.REQUIRED, example = "{}")
    @FieldCompare(description = "使用信息", recursive = true)
    private ProductUseInfoDto useInfo;

    // 资质信息
    @Schema(description = "资质信息", requiredMode = RequiredMode.REQUIRED, example = "{}")
    // @FieldCompare(description = "资质信息", recursive = true)
    private ProductQualificationInfoDto qualificationInfo;

    private TenantDto tenant;

    public TenantDto getTenant() {
        if (this.tenant == null) {
            this.tenant = TenantDto.builder()
                .id(this.tenantId)
                .headTenantId(this.headTenantId)
                .build();
        }
        return this.tenant;
    }
    public void setTenant(TenantDto tenant) {
        this.tenant = tenant;
        this.tenantId = tenant.getId();
        this.headTenantId = tenant.getHeadTenantId();
    }

}