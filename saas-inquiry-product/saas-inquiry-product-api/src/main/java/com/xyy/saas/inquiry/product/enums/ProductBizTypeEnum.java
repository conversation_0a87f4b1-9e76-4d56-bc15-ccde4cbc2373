package com.xyy.saas.inquiry.product.enums;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import jakarta.annotation.Nonnull;
import java.util.Objects;
import java.util.Optional;

/**
 * desc 商品业务类型：
 *    1. 新建标品
 *    2. 门店建品
 *    3. 首营审批
 *    4. 商品拆零
 *    9. 编辑商品
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductBizTypeEnum {

    // 新建标品
    MID_STDLIB_ADD(1, "新建标品"),
    // 门店建品
    CHAIN_STORE_ADD(2, "门店建品"),
    // 首营审批
    FIRST_PRODUCT(3, "首营审批"),
    // 商品拆零
    UNBUNDLED_PRODUCT(4, "商品拆零"),

    // 编辑商品
    EDIT_PRODUCT(9, "编辑商品"),

    // 问诊临时建品
    INQUIRY_ADD_TEMPORARY(11, "问诊临时建品"),

    // 问诊临时建品
    INQUIRY_PRESENT(21, "问诊提报商品"),

    ;

    public final int code;
    public final String desc;

    ProductBizTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取业务类型
     * @param dto
     * @param bizType
     * @return
     */
    public static ProductBizTypeEnum checkIfNullBizType(ProductInfoDto dto, ProductBizTypeEnum bizType) {
        if (bizType != null) {
            return bizType;
        }
        // 1. 判断是否新品提报
        if (ObjectUtil.isNull(dto.getMidStdlibId())) {
            return ProductBizTypeEnum.MID_STDLIB_ADD;
        }
        // 2. 智慧脸业务走审批，其他的（比如移动端提报商品，审批通过后直接变为使用中） 1是关闭！！！
        if (dto.getTenant().getZhlBizTypeStatus() == 1) {
            return ProductBizTypeEnum.EDIT_PRODUCT;
        }
        // 3. 判断是否门店提报
        if (dto.getHeadTenantId() != null && !Objects.equals(dto.getTenantId(), dto.getHeadTenantId())) {
            return ProductBizTypeEnum.CHAIN_STORE_ADD;
        }
        // 默认：首营审批
        return ProductBizTypeEnum.FIRST_PRODUCT;
    }


    /**
     * 处理商品状态和标识
     * @param dto
     */
    public void handleProductStatusOrFlag(@Nonnull ProductInfoDto dto) {
        switch (this) {
            // 1. 判断是否新品提报
            case MID_STDLIB_ADD, INQUIRY_PRESENT -> dto.setStatus(ProductStatusEnum.MID_AUDITING.code);
            // 2. 判断是否连锁门店建品
            case CHAIN_STORE_ADD -> dto.setStatus(ProductStatusEnum.HEAD_AUDITING.code);
            // 3. 走首营审批
            case FIRST_PRODUCT -> dto.setStatus(ProductStatusEnum.FIRST_AUDITING.code);
            // 4. 走商品拆零: 设置拆零标识
            case UNBUNDLED_PRODUCT -> dto.setProductFlag(Optional.ofNullable(dto.getProductFlag()).orElseGet(ProductFlag::new).setUnbundled(true));
            // 5. 问诊临时建品: 暂存状态
            case INQUIRY_ADD_TEMPORARY -> dto.setStatus(ProductStatusEnum.TEMP.code);
            // 6. 默认：使用中（前提：不指定状态）
            default -> dto.setStatus(Optional.ofNullable(dto.getStatus()).orElse(ProductStatusEnum.USING.code));
        }
    }

    /**
     * 判断是否不需要处理标准库数据
     * @return
     */
    public boolean ignoreHandleStdlib() {
        // 暂时 编辑商品 也会处理标准库数据，后续需要优化
        return switch (this) {
            case MID_STDLIB_ADD, INQUIRY_PRESENT, CHAIN_STORE_ADD, FIRST_PRODUCT, EDIT_PRODUCT -> false;
            // 问诊临时建品（添加标准库数据）
            case INQUIRY_ADD_TEMPORARY -> false;
            default -> true;
        };
    }

    /**
     * 判断如果存在标准库id重复数据，是否抛异常，如果为false则变成修改数据
     * @return
     */
    public boolean throwExceptionIfStdlibUnique() {
        return false;
    }
}
