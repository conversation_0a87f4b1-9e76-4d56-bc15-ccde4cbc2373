package com.xyy.saas.inquiry.product.api.product.dto;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.annotation.FieldCompare;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * desc 商品属性标志：是否拆零品、是否停售、是否总部禁采、是否门店禁采、是否进口（非国产）、是否特价商品、是否积分商品、是否含特殊药品复方制剂、
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ProductFlag implements Serializable {
    // 基础信息属性（门店）
    @Schema(description = "是否拆零品", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否拆零品")
    private Boolean unbundled;
    @Schema(description = "是否停售", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否停售")
    private Boolean stopSale;
    @Schema(description = "是否总部禁采", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否总部禁采")
    private Boolean headPurchaseDisabled;
    @Schema(description = "是否门店禁采", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否门店禁采")
    private Boolean storePurchaseDisabled;
    @Schema(description = "是否特价商品", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否特价商品")
    private Boolean specialPrice;
    @Schema(description = "是否积分商品", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否积分商品")
    private Boolean integral;

    // 标准库商品属性（全局）
    @Schema(description = "是否进口（非国产）", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否进口（非国产）")
    private Boolean imported;
    @Schema(description = "是否含特殊药品复方制剂", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "是否含特殊药品复方制剂")
    private Boolean hasSpecialDrugCompound;
    @Schema(description = "中台已停用", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "中台已停用")
    private Boolean midDeactivated;
    @Schema(description = "中台同步是否跳过（不覆盖）", requiredMode = RequiredMode.REQUIRED, example = "true")
    @FieldCompare(description = "中台同步是否跳过（不覆盖）")
    private Boolean midSyncSkipped;

    private static final long BIT_MASK = 1L;
    /**
     * 更新属性对象（覆盖）
     * @param oldFlag
     * @param newFlag
     * @return
     */
    public static ProductFlag cover(ProductFlag oldFlag, ProductFlag newFlag) {
        if (oldFlag == null) {
            return newFlag;
        }
        if (newFlag == null) {
            return oldFlag;
        }

        // 复制 oldFlag 对象（防止原数据修改）
        ProductFlag flag = BeanUtil.copyProperties(oldFlag, ProductFlag.class);
        // 遍历 newFlag 的属性，如果不为空则更新 flag 的属性
        for (FlagEnum f : FlagEnum.values()) {
            Boolean value = f.getter.apply(newFlag);
            if (value != null) {
                f.setter.accept(flag, value);
            }
        }
        return flag;
    }

    /**
     * 属性对象转为long数值（门店商品属性）
     * @param oldMultiFlag
     * @param newFlag
     * @return
     */
    public static long toFlag(Long oldMultiFlag, ProductFlag newFlag) {
        ProductFlag oldFlag = ProductFlag.of(oldMultiFlag);
        ProductFlag covered = cover(oldFlag, newFlag);
        return toFlag(covered, false);
    }

    /**
     * 属性对象转为long数值（标准库商品属性）
     * @param oldMultiFlag
     * @param newFlag
     * @return
     */
    public static long toStdlibFlag(Long oldMultiFlag, ProductFlag newFlag) {
        ProductFlag oldFlag = ProductFlag.ofStdlib(oldMultiFlag);
        ProductFlag covered = cover(oldFlag, newFlag);
        return toFlag(covered, true);
    }

    /**
     * 属性对象转为long数值
     * @param pf
     * @param stdlib
     * @return
     */
    private static long toFlag(ProductFlag pf, boolean stdlib) {
        if (pf == null) {
            pf = new ProductFlag();
        }
        long flag = 0;
        for (FlagEnum f : FlagEnum.values()) {
            if (f.stdlib != stdlib) {
                continue;
            }
            if (Boolean.TRUE.equals(f.getter.apply(pf))) {
                // 设置对应比特位为 1
                flag |= (BIT_MASK << f.index);
            } else if (Boolean.FALSE.equals(f.getter.apply(pf))) {
                // 设置对应比特位为 0
                flag &= ~(BIT_MASK << f.index);
            }
        }
        return flag;
    }

    /**
     * long数值转为属性对象
     * @param flag
     * @return
     */
    public static ProductFlag of(Long flag) {
        return of(null, flag, false);
    }
    public static ProductFlag of(ProductFlag dto, Long flag) {
        return of(dto, flag, false);
    }
    public static ProductFlag ofStdlib(Long flag) {
        return of(null, flag, true);
    }
    public static ProductFlag ofStdlib(ProductFlag dto, Long flag) {
        return of(dto, flag, true);
    }

    /**
     * long数值转为属性对象（基于原对象赋值）
     * @param dto
     * @param flag
     * @param stdlib
     * @return
     */
    private static ProductFlag of(ProductFlag dto, Long flag, boolean stdlib) {
        if (flag == null) {
            return dto;
        }
        dto = dto == null ? new ProductFlag() : dto;
        for (FlagEnum f : FlagEnum.values()) {
            if (f.stdlib != stdlib) {
                continue;
            }
            boolean value = (flag & (BIT_MASK << f.index)) != 0;
            // 根据比特位设置属性
            f.setter.accept(dto, value);
        }
        return dto;
    }

    /**
     * 属性对象转为sql语句（提供给mybatis xml使用，必须是静态方法）
     * {@link com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper#selectPage(Page, com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO)}
     * @param column  字段名（联表需要带上前缀表别名）
     * @return
     */
    public static String toFlagSql(ProductFlag productFlag, String column) {
        return toFlagWhereSql(productFlag, column, false);
    }
    public static String toStdlibFlagSql(ProductFlag productFlag, String column) {
        return toFlagWhereSql(productFlag, column, true);
    }

    /**
     * 属性对象转为sql where语句
     * @param productFlag
     * @param column
     * @param stdlib
     * @return
     */
    private static String toFlagWhereSql(ProductFlag productFlag, String column, boolean stdlib) {
        if (productFlag == null) {
            return "";
        }
        List<String> flagSql = new ArrayList<>();
        for (FlagEnum f : FlagEnum.values()) {
            if (f.stdlib != stdlib) {
                continue;
            }
            Boolean bool = f.getter.apply(productFlag);
            if (bool == null) {
                continue;
            }
            // 拼接sql 与操作判断某一位上是0还是1
            flagSql.add(MessageFormat.format("({0} & {1}) {2} 0", column, BIT_MASK << f.index, bool ? "!=" : "="));
        }
        String where = String.join(" AND ", flagSql);
        return StringUtils.defaultIfBlank(where, "");
    }


    private static String toFlagUpdateSql(ProductFlag productFlag, String column, boolean stdlib) {
        if (productFlag == null) {
            return "";
        }
        List<String> flagSql = new ArrayList<>();
        for (FlagEnum f : FlagEnum.values()) {
            if (f.stdlib != stdlib) {
                continue;
            }
            Boolean bool = f.getter.apply(productFlag);
            if (bool == null) {
                continue;
            }
            // 拼接位运算 sql，true做或（|）运算，false做与取反（&～）运算
            if (bool) {
                flagSql.add(MessageFormat.format("| {0}", BIT_MASK << f.index));
            } else {
                flagSql.add(MessageFormat.format("& ~{0}", BIT_MASK << f.index));
            }
        }
        if (flagSql.isEmpty()) {
            return "";
        }
        // 拼接sql update 语句: multi_flag = multi_flag | 1 & ~2 | 4
        return column + " = " + column +  String.join(" ", flagSql);
    }

    /**
     * desc 商品多属性标志：是否拆零品、是否停售、是否总部禁采、是否门店禁采、是否进口（非国产）、是否特价商品、是否积分商品、是否含特殊药品复方制剂
     */
    public enum FlagEnum {

        // 基础信息属性（门店）
        UNBUNDLED(false, 0, "拆零品", ProductFlag::getUnbundled, ProductFlag::setUnbundled),
        STOP_SALE(false, 1, "停售", ProductFlag::getStopSale, ProductFlag::setStopSale),
        HEAD_PURCHASE_DISABLED(false, 2, "总部禁采", ProductFlag::getHeadPurchaseDisabled, ProductFlag::setHeadPurchaseDisabled),
        STORE_PURCHASE_DISABLED(false, 3, "门店禁采", ProductFlag::getStorePurchaseDisabled, ProductFlag::setStorePurchaseDisabled),
        SPECIAL_PRICE(false, 4, "特价商品", ProductFlag::getSpecialPrice, ProductFlag::setSpecialPrice),
        INTEGRAL(false, 5, "积分商品", ProductFlag::getIntegral, ProductFlag::setIntegral),

        // 标准库商品属性（全局）
        IMPORTED(true, 0, "进口（非国产）", ProductFlag::getImported, ProductFlag::setImported),
        HAS_SPECIAL_DRUG_COMPOUND(true, 1, "含特殊药品复方制剂", ProductFlag::getHasSpecialDrugCompound, ProductFlag::setHasSpecialDrugCompound),
        MID_DEACTIVATED(true, 2, "中台已停用", ProductFlag::getMidDeactivated, ProductFlag::setMidDeactivated),
        MID_SYNC_SKIPPED(true, 3, "中台同步是否覆盖", ProductFlag::getMidSyncSkipped, ProductFlag::setMidSyncSkipped),

        ;

        public final boolean stdlib;
        public final int index;
        public final String desc;
        public final Function<ProductFlag, Boolean> getter;
        public final BiConsumer<ProductFlag, Boolean> setter;

        FlagEnum(boolean stdlib, int index, String desc, Function<ProductFlag, Boolean> getter, BiConsumer<ProductFlag, Boolean> setter) {
            this.stdlib = stdlib;
            this.index = index;
            this.desc = desc;
            this.getter = getter;
            this.setter = setter;
        }

    }

}
