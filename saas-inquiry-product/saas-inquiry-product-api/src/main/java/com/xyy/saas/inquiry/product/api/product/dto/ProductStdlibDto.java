package com.xyy.saas.inquiry.product.api.product.dto;

import com.xyy.saas.inquiry.annotation.FieldCompare;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商品标准库 DTO
 */
@Schema(description = "商品标准库 DTO")
@Data
@Accessors(chain = true)
public class ProductStdlibDto implements Serializable {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "中台标准库ID")
    private Long midStdlibId;

    @Schema(description = "通用名")
    private String commonName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "规格/型号")
    private String spec;

    @Schema(description = "条形码")
    private String barcode;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "要素字段hash值")
    private String keyPointHash;

    @Schema(description = "助记码")
    private String mnemonicCode;

    @Schema(description = "最小包装数量")
    @FieldCompare(description = "最小包装数量")
    private BigDecimal minPackageNum;

    @Schema(description = "商品封面图片链接")
    @FieldCompare(description = "商品封面图")
    private List<String> coverImages;

    @Schema(description = "商品外包装图片链接")
    @FieldCompare(description = "商品外包装图")
    private List<String> outerPackageImages;

    @Schema(description = "商品说明书图片链接")
    @FieldCompare(description = "商品说明书图")
    private List<String> instructionImages;

    @Schema(description = "商品大类")
    @FieldCompare(description = "商品大类")
    private String spuCategory;

    @Schema(description = "单位")
    @FieldCompare(description = "单位")
    private String unit;

    @Schema(description = "剂型")
    @FieldCompare(description = "剂型")
    private String dosageForm;

    @Schema(description = "所属范围")
    @FieldCompare(description = "所属范围")
    private String businessScope;

    @Schema(description = "处方分类")
    @FieldCompare(description = "处方分类")
    private String presCategory;

    @Schema(description = "储存条件")
    @FieldCompare(description = "储存条件")
    private String storageWay;

    @Schema(description = "产地")
    @FieldCompare(description = "产地")
    private String origin;

    @Schema(description = "生产企业社会信用代码")
    @FieldCompare(description = "生产企业社会信用代码")
    private String manufacturerUscc;

    @Schema(description = "有效期")
    @FieldCompare(description = "有效期")
    private String productValidity;

    @Schema(description = "批准文号有效期")
    @FieldCompare(description = "批准文号有效期")
    private LocalDate approvalValidityPeriod;

    @Schema(description = "上市许可持有人")
    @FieldCompare(description = "上市许可持有人")
    private String marketingAuthorityHolder;

    @Schema(description = "药品标识码")
    @FieldCompare(description = "药品标识码")
    private String drugIdentCode;

    @Schema(description = "用药方式")
    @FieldCompare(description = "用药方式")
    private String usageMethod;
    @Schema(description = "用药频次")
    @FieldCompare(description = "用药频次")
    private String usageFrequency;
    @Schema(description = "单次使用剂量")
    @FieldCompare(description = "单次使用剂量")
    private String singleDosage;
    @Schema(description = "单次使用剂量单位")
    @FieldCompare(description = "单次使用剂量单位")
    private String singleDosageUnit;

    @Schema(description = "一级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "一级分类")
    private String firstCategory;
    @Schema(description = "二级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "二级分类")
    private String secondCategory;
    @Schema(description = "三级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "六级分类")
    private String sixCategory;

    @Schema(description = "多属性标志")
    private Long multiFlag;

    @Schema(description = "多属性标志")
    @FieldCompare(description = "多属性标志", recursive = true)
    private ProductFlag productFlag;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}