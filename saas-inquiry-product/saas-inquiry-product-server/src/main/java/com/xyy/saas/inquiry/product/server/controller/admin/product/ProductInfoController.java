package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecycleSaveReqVO;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductRecycleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 商品基本信息")
@RestController
@RequestMapping("/product/info")
@Validated
public class ProductInfoController {

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductRecycleService productRecycleService;

    @PostMapping("/create")
    @Operation(summary = "创建商品基本信息")
    @PreAuthorize("@ss.hasPermission('saas:product:info:create')")
    public CommonResult<Long> createProductInfo(@Valid @RequestBody ProductInfoSaveReqVO createReqVO) {
        ProductInfoDto dto = BeanUtils.toBean(createReqVO, ProductInfoDto.class);
        return success(productInfoService.saveOrUpdateProduct(dto, null));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品基本信息")
    @PreAuthorize("@ss.hasPermission('saas:product:info:update')")
    public CommonResult<Long> updateProductInfo(@Valid @RequestBody ProductInfoSaveReqVO updateReqVO) {
        ProductInfoDto dto = BeanUtils.toBean(updateReqVO, ProductInfoDto.class);
        return success(productInfoService.saveOrUpdateProduct(dto, ProductBizTypeEnum.EDIT_PRODUCT));
    }

    // @DeleteMapping("/delete")
    // @Operation(summary = "删除商品基本信息")
    // @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('saas:product:info:delete')")
    // public CommonResult<Boolean> deleteProductInfo(@RequestParam("id") Long id) {
    //     productInfoService.deleteProductInfo(id);
    //     return success(true);
    // }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品")
    @PreAuthorize("@ss.hasPermission('saas:product:info:delete')")
    public CommonResult<Boolean> deleteProduct(@Valid @RequestBody ProductRecycleSaveReqVO reqVO) {
        productRecycleService.softDeleteProduct(reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品基本信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:info:query')")
    public CommonResult<ProductInfoRespVO> getProductInfo(@RequestParam("id") Long id) {
        ProductInfoDto productInfo = productInfoService.getProductInfo(id);
        return success(BeanUtils.toBean(productInfo, ProductInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品基本信息分页")
    @PreAuthorize("@ss.hasPermission('saas:product:info:query')")
    public CommonResult<PageResult<ProductInfoRespVO>> getProductInfoPage(@Valid ProductInfoPageReqVO pageReqVO) {
        PageResult<ProductInfoDto> pageResult = productInfoService.getProductInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品基本信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductInfoExcel(@Valid ProductInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductInfoDto> list = productInfoService.getProductInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品基本信息.xls", "数据", ProductInfoRespVO.class,
                        BeanUtils.toBean(list, ProductInfoRespVO.class));
    }

}