package com.xyy.saas.inquiry.product.server.service.productcategory;

import java.util.*;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import jakarta.validation.*;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;

/**
 * 商品六级分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCategoryService {

    /**
     * 批量保存或更新商品六级分类
     * @param productCategories
     */
    void batchSaveOrUpdate(List<ProductCategoryDO> productCategories);

    /**
     * 创建商品六级分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductCategory(@Valid ProductCategorySaveReqVO createReqVO);

    /**
     * 更新商品六级分类
     *
     * @param updateReqVO 更新信息
     */
    int updateProductCategory(@Valid ProductCategorySaveReqVO updateReqVO);

    /**
     * 删除商品六级分类
     *
     * @param id 编号
     */
    void deleteProductCategory(Long id);

    /**
     * 获得商品六级分类
     *
     * @param id 编号
     * @return 商品六级分类
     */
    ProductCategoryDO getProductCategory(Long id);

    /**
     * 获得商品六级分类列表
     *
     * @param listReqVO 查询条件
     * @return 商品六级分类列表
     */
    List<ProductCategoryDO> getProductCategoryList(ProductCategoryListReqVO listReqVO);

    /**
     * 清空缓存
     */
    void clearProductCategoryTreeCache();
    /**
     * 获得商品六级分类树
     *
     * @return 商品六级分类树
     */
    List<ProductCategoryRespVO> getProductCategoryTree();

}