package com.xyy.saas.inquiry.product.server.dal.mysql.bpm;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 审批流关联业务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmBusinessRelationMapper extends BaseMapperX<BpmBusinessRelationDO> {

    default PageResult<BpmBusinessRelationDO> selectPage(BpmBusinessRelationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmBusinessRelationDO>()
                .eqIfPresent(BpmBusinessRelationDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(BpmBusinessRelationDO::getHeadTenantId, reqVO.getHeadTenantId())
                .eqIfPresent(BpmBusinessRelationDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(BpmBusinessRelationDO::getBusinessPref, reqVO.getBusinessPref())
                .eqIfPresent(BpmBusinessRelationDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(BpmBusinessRelationDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(BpmBusinessRelationDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(BpmBusinessRelationDO::getApplicant, reqVO.getApplicant())
                .eqIfPresent(BpmBusinessRelationDO::getApprovalStatus, reqVO.getApprovalStatus())
                .eqIfPresent(BpmBusinessRelationDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BpmBusinessRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BpmBusinessRelationDO::getId));
    }

}