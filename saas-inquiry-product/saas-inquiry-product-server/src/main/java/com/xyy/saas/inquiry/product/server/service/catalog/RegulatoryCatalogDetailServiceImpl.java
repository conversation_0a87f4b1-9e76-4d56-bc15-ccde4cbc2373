package com.xyy.saas.inquiry.product.server.service.catalog;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.REGULATORY_CATALOG_DETAIL_DATA_IS_NULL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.RegulatoryCatalogDetailMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 监管目录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class RegulatoryCatalogDetailServiceImpl implements RegulatoryCatalogDetailService {

    @Resource
    private FileApi fileApi;

    @Resource
    private RegulatoryCatalogDetailMapper regulatoryCatalogDetailMapper;

    @Resource
    private ProductStdlibMapper productStdlibMapper;

    @Resource
    private CatalogMapper catalogMapper;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    /**
     * 获得监管目录明细列表
     *
     * @param catalogId 目录ID
     * @return
     */
    @Override
    public List<RegulatoryCatalogDetailDO> getRegulatoryCatalogDetailListByCatalogId(Long catalogId) {
        return regulatoryCatalogDetailMapper.selectListByCatalogId(catalogId);
    }

    /**
     * 分页查询监管目录商品明细
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<RegulatoryCatalogDetailDO> getRegulatoryCatalogDetailPage(RegulatoryCatalogDetailPageReqVO pageReqVO) {

        return regulatoryCatalogDetailMapper.selectPage(pageReqVO);
    }

    /**
     * 解析目录版本药品excel
     *
     * @param createReqVO
     * @return
     */
    @Override
    public CatalogRespVO analysisCatalogExcel(CatalogSaveReqVO createReqVO) {

        // 解析和校验excel(校验表头是否匹配和最大条数是否超限)
        List<RegulatoryCatalogDetailExcelVO> regulatoryCatalogDetailExcelVOList = easyExcelUtil.analysisAndCheckExcel(createReqVO.getUploadUrl(), RegulatoryCatalogDetailExcelVO.class, 20000);

        if (CollUtil.isEmpty(regulatoryCatalogDetailExcelVOList)) {
            throw exception(REGULATORY_CATALOG_DETAIL_DATA_IS_NULL);
        }

        // excel解析正确的数据
        List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOList = new ArrayList<>();
        // excel解析错误的数据
        List<RegulatoryCatalogDetailExcelVO> errorDataList = new ArrayList<>();

        // 全部标准库id集合 , 用来判断重复数据
        Set<String> allStandardId = new HashSet<>();

        // 已经存在该目录下数据库的标准库id集合
        List<Long> existProjectCodeList = new ArrayList<>();
        if (createReqVO.getId() != null) {
            List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOS = regulatoryCatalogDetailMapper.selectListByCatalogId(createReqVO.getId());
            existProjectCodeList = regulatoryCatalogDetailDOS.stream().map(RegulatoryCatalogDetailDO::getProjectCode).filter(Objects::nonNull).distinct().toList();
        }

        for (List<RegulatoryCatalogDetailExcelVO> regulatoryCatalogDetailExcelVOS : Lists.partition(regulatoryCatalogDetailExcelVOList, 500)) {

            List<Long> standardIdList = regulatoryCatalogDetailExcelVOS.stream()
                .filter(item -> Convert.toLong(item.getStandardId()) != null)
                .map(item -> Convert.toLong(item.getStandardId())).distinct().toList();

            Map<Long, ProductStdlibDO> productStdlibDOMap = new HashMap<>();

            if (CollUtil.isNotEmpty(standardIdList)) {
                List<ProductStdlibDO> productStdlibDOList = productStdlibMapper.listByMidStdlibId(standardIdList);
                if (CollUtil.isNotEmpty(productStdlibDOList)) {
                    productStdlibDOMap = productStdlibDOList.stream().collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (v1, v2) -> v2));
                }
            }

            for (RegulatoryCatalogDetailExcelVO regulatoryCatalogDetailExcelVO : regulatoryCatalogDetailExcelVOS) {

                StringBuilder errorMsgBuilder = new StringBuilder();

                // 判空
                if (StringUtils.isBlank(regulatoryCatalogDetailExcelVO.getStandardId())) {
                    errorMsgBuilder.append("院内药品唯一码（L）为空;");
                } else {

                    // 判断格式是否为数字格式,String转Long
                    Long standardLongId = Convert.toLong(regulatoryCatalogDetailExcelVO.getStandardId());

                    if (standardLongId == null) {
                        errorMsgBuilder.append("院内药品唯一码（L）格式错误;");
                    }

                    // 判断是否已添加
                    if (existProjectCodeList.contains(standardLongId)) {
                        errorMsgBuilder.append("院内药品唯一码（L）已添加;");
                    }

                    // 判断是否重复
                    if (allStandardId.contains(regulatoryCatalogDetailExcelVO.getStandardId())) {
                        errorMsgBuilder.append("院内药品唯一码（L）在excel中重复;");
                    }
                    allStandardId.add(regulatoryCatalogDetailExcelVO.getStandardId());

                    // 判断是否存在于商品中台
                    if (CollUtil.isEmpty(productStdlibDOMap) || !productStdlibDOMap.containsKey(standardLongId)) {
                        errorMsgBuilder.append("院内药品唯一码（L）该编码在系统不存在;");
                    }
                }

                if (StringUtils.isNotBlank(errorMsgBuilder.toString())) {
                    regulatoryCatalogDetailExcelVO.setErrorMsg(errorMsgBuilder.toString());
                    errorDataList.add(regulatoryCatalogDetailExcelVO);
                    continue;
                }

                ProductStdlibDO productStdlibDO = productStdlibDOMap.get(Convert.toLong(regulatoryCatalogDetailExcelVO.getStandardId()));

                regulatoryCatalogDetailDOList.add(RegulatoryCatalogDetailDO.builder()
                    .projectCode(Convert.toLong(regulatoryCatalogDetailExcelVO.getStandardId()))
                    .commonName(productStdlibDO.getCommonName())
                    .brandName(productStdlibDO.getBrandName())
                    .spec(productStdlibDO.getSpec())
                    .barcode(productStdlibDO.getBarcode())
                    .manufacturer(productStdlibDO.getManufacturer())
                    .approvalNumber(productStdlibDO.getApprovalNumber())
                    .disable(false)
                    .remark(productStdlibDO.getRemark()).build());
            }
        }

        // 失败数据excel
        String failExcelUrl = "";

        if (CollUtil.isNotEmpty(errorDataList)) {
            // 写入excel并上传得到文件url
            failExcelUrl = easyExcelUtil.writeExcelAndUpload("监管目录明细导入失败记录", RegulatoryCatalogDetailExcelVO.class, errorDataList);
        }

        return new CatalogRespVO()
            .setExportSuccessCount(regulatoryCatalogDetailDOList.size())
            .setRegulatoryCatalogDetailList(regulatoryCatalogDetailDOList)
            .setExportFailCount(errorDataList.size())
            .setFailExcelUrl(failExcelUrl);
    }

    /**
     * 修改状态
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public Boolean updateStatus(RegulatoryCatalogDetailPageReqVO pageReqVO) {

        if (pageReqVO.getId() == null || pageReqVO.getDisable() == null) {
            throw exception(PARAM_INVALID, "id | disable", "不能为空");
        }

        return regulatoryCatalogDetailMapper.updateById(RegulatoryCatalogDetailDO.builder().id(pageReqVO.getId()).disable(pageReqVO.getDisable()).build()) > 0;
    }

    /**
     * 导入监管目录药品excel
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public CatalogRespVO importExcel(CatalogSaveReqVO pageReqVO) {

        if (pageReqVO.getId() == null || StringUtils.isBlank(pageReqVO.getUploadUrl())) {
            throw exception(PARAM_INVALID, "id | uploadUrl", "不能为空");
        }

        // 解析目录版本药品excel
        CatalogRespVO catalogRespVO = this.analysisCatalogExcel(pageReqVO);

        if (CollUtil.isEmpty(catalogRespVO.getRegulatoryCatalogDetailList())) {
            return catalogRespVO;
        }

        for (RegulatoryCatalogDetailDO item : catalogRespVO.getRegulatoryCatalogDetailList()) {
            item.setCatalogId(pageReqVO.getId());
        }

        // 新增目录药品详情记录
        List<List<RegulatoryCatalogDetailDO>> partition = Lists.partition(catalogRespVO.getRegulatoryCatalogDetailList(), 500);
        for (List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOS : partition) {
            regulatoryCatalogDetailMapper.batchInsertByXml(regulatoryCatalogDetailDOS);
        }

        // 累加目录的记录总数
        catalogMapper.accumulationCatalogTotalCount(pageReqVO.getId(), catalogRespVO.getRegulatoryCatalogDetailList().size());

        // 不需要展示给前端
        catalogRespVO.setRegulatoryCatalogDetailList(null);
        return catalogRespVO;
    }
}
