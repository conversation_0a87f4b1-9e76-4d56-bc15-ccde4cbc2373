package com.xyy.saas.inquiry.product.server.service.bpm;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_BUSINESS_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_BUSINESS_TYPE_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;
import static com.xyy.saas.inquiry.product.consts.ProductConstant.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCancelReqVO;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationCancelReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 审批流关联业务 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class BpmBusinessRelationServiceImpl implements BpmBusinessRelationService {

    @Resource
    private Validator validator;

    @Resource
    private BpmBusinessRelationMapper bpmBusinessRelationMapper;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;
    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private TenantApi tenantApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProcessInstance(Long userId, BpmBusinessRelationDto businessDto) {
        if (userId == null || businessDto == null) {
            throw exception(PARAM_INVALID, "userId | businessDto", "不能为空");
        }
        ValidationUtils.validate(validator, businessDto);

        // 校验业务类型
        Integer businessType = businessDto.getBusinessType();
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByCode(businessType);
        if (businessTypeEnum == null) {
            throw exception(BPM_BUSINESS_TYPE_NOT_EXISTS, businessType);
        }

        // 校验业务单据是否已存在审批流
        boolean exists = bpmBusinessRelationMapper.exists(new LambdaQueryWrapperX<BpmBusinessRelationDO>()
            .eq(BpmBusinessRelationDO::getBusinessPref, businessDto.getBusinessPref())
            .eq(BpmBusinessRelationDO::getBusinessType, businessType)
            .eq(BpmBusinessRelationDO::getApprovalStatus, ApprovalStatus.RUNNING));
        if (exists) {
            return null;
        }

        // 插入 业务关联表
        BpmBusinessRelationDO businessRelationDO = BeanUtils.toBean(businessDto, BpmBusinessRelationDO.class)
            .setStartTime(LocalDateTime.now())
            .setApplicant("" + userId)
            .setApprovalStatus(ApprovalStatus.RUNNING);
        bpmBusinessRelationMapper.insert(businessRelationDO);
        // 业务关联表id
        Long brId = businessRelationDO.getId();

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        // processInstanceVariables.put("day", day);
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
            new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(businessTypeEnum.processDefinitionKey)
                .setVariables(processInstanceVariables)
                .setBusinessKey(String.valueOf(brId))
                // .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())
            );

        // 将工作流的编号，更新到 业务关联表中
        bpmBusinessRelationMapper.updateById(new BpmBusinessRelationDO().setId(brId).setProcessInstanceId(processInstanceId));
        return brId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BpmBusinessRelationDO updateProcessInstanceStatus(BpmBusinessRelationDto businessDto) {
        Long id = businessDto.getId();
        // 校验存在
        BpmBusinessRelationDO businessRelationDO = validateBpmBusinessRelationExists(id);
        // 更新审批关联表状态
        BpmBusinessRelationDO updateObj = new BpmBusinessRelationDO()
            .setId(id)
            .setApprovalStatus(businessDto.getApprovalStatus());
        bpmBusinessRelationMapper.updateById(updateObj);

        return businessRelationDO.setApprovalStatus(updateObj.getApprovalStatus());
    }

    @Override
    public void deleteProcessInstance(BpmBusinessRelationCancelReqVO cancelReqVO) {
        ValidationUtils.validate(validator, cancelReqVO);

        // 查询审批中中的审批流
        List<BpmBusinessRelationDO> runningBprList = bpmBusinessRelationMapper.selectList(new LambdaQueryWrapperX<BpmBusinessRelationDO>()
            .in(BpmBusinessRelationDO::getBusinessPref, cancelReqVO.getBusinessPrefList())
            .eqIfPresent(BpmBusinessRelationDO::getBusinessType, cancelReqVO.getBusinessType())
            .eq(BpmBusinessRelationDO::getApprovalStatus, ApprovalStatus.RUNNING));

        // 获取当前管理员
        Long tenantId = cancelReqVO.getTenantId();
        TenantDto tenantDto = tenantId == null ? tenantApi.getTenant() : tenantApi.getTenant(tenantId);
        Long userId = tenantDto.getContactUserId();
        for (BpmBusinessRelationDO runningBpr : runningBprList) {
            // 取消审批流
            processInstanceService.cancelProcessInstanceByAdmin(userId, new BpmProcessInstanceCancelReqVO()
                .setId(runningBpr.getProcessInstanceId())
                .setReason(cancelReqVO.getReason()));
            log.info("[deleteProcessInstance][取消审批流] userId({}) tenantId({}) processInstanceId({})",
                userId, tenantId, runningBpr.getProcessInstanceId());

            // 删除审批关联表
            bpmBusinessRelationMapper.deleteById(runningBpr);
            log.info("[deleteProcessInstance][删除审批业务关联表] userId({}) tenantId({}) businessRelationId({})",
                userId, tenantId, runningBpr.getId());
        }

    }

    private BpmBusinessRelationDO validateBpmBusinessRelationExists(Long id) {
        BpmBusinessRelationDO businessRelationDO = bpmBusinessRelationMapper.selectById(id);
        if (businessRelationDO == null) {
            throw exception(BPM_BUSINESS_RELATION_NOT_EXISTS);
        }
        return businessRelationDO;
    }

    @Override
    public BpmBusinessRelationDO getBpmBusinessRelation(Long id) {
        return bpmBusinessRelationMapper.selectById(id);
    }

    @Override
    public PageResult<BpmBusinessRelationDO> getBpmBusinessRelationPage(BpmBusinessRelationPageReqVO pageReqVO) {
        return bpmBusinessRelationMapper.selectPage(pageReqVO);
    }

}