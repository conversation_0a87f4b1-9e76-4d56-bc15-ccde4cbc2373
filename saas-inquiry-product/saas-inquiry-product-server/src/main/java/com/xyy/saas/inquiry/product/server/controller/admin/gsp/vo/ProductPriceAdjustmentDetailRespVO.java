package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 售价调整单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductPriceAdjustmentDetailRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13165")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16932")
    @ExcelProperty("单据编号")
    private String recordPref;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED, example = "2585")
    private Long applicableTenantId;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED, example = "2585")
    @ExcelProperty("适用门店")
    private String applicableTenantName;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "22067")
    @ExcelProperty("商品编码")
    private String productPref;

    @Schema(description = "原零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "14358")
    @ExcelProperty("原零售价")
    private BigDecimal oldRetailPrice;

    @Schema(description = "原会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "27197")
    @ExcelProperty("原会员价")
    private BigDecimal oldMemberPrice;

    @Schema(description = "新零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3727")
    @ExcelProperty("新零售价")
    private BigDecimal newRetailPrice;

    @Schema(description = "新会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "31530")
    @ExcelProperty("新会员价")
    private BigDecimal newMemberPrice;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}