package com.xyy.saas.inquiry.product.server.service.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecyclePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecycleSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品回收站 Service 接口
 */
public interface ProductRecycleService {

    /**
     * 逻辑删除商品
     *
     * @param saveReqVO 更新信息
     */
    void softDeleteProduct(@Valid ProductRecycleSaveReqVO saveReqVO);

    /**
     * 恢复商品
     *
     * @param idList 编号
     */
    void restoreProduct(List<Long> idList);

    /**
     * 永久删除商品
     *
     * @param idList 编号
     */
    void hardDeleteProduct(List<Long> idList);

    /**
     * 获得商品回收站分页
     *
     * @param pageReqVO 分页查询
     * @return 商品回收站分页
     */
    PageResult<ProductInfoDO> getRecyclePage(@Valid ProductRecyclePageReqVO pageReqVO);

    /**
     * 清理过期的回收站数据
     * 
     * @param retainDays 保留天数
     * @return 清理的数据量
     */
    int cleanExpiredRecycleData(Integer retainDays);

} 