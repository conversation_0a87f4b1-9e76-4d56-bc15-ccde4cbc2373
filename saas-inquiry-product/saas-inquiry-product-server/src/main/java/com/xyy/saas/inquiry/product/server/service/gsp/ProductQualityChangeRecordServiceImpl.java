package com.xyy.saas.inquiry.product.server.service.gsp;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_QUALITY_CHANGE_DETAIL_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS;

import java.util.Objects;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.consts.ProductConstant.ApprovalStatus;
import com.xyy.saas.inquiry.product.enums.QualityChangeTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeDetailSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductQualityChangeDetailMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductQualityChangeRecordMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质量变更申请操作记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductQualityChangeRecordServiceImpl implements ProductQualityChangeRecordService {

    @Resource
    private ProductQualityChangeRecordMapper productQualityChangeRecordMapper;
    @Resource
    private ProductQualityChangeDetailMapper productQualityChangeDetailMapper;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateQualityChange(ProductQualityChangeRecordSaveReqVO reqVO) {
        // 新增 or 更新
        Long id = reqVO.getId();
        ProductQualityChangeRecordDO recordDO = id == null
            ? createProductQualityChangeRecordAndDetail(reqVO)
            : updateProductQualityChangeRecordAndDetail(reqVO);

        // 如果是提交，则发起工作流
        // if (Boolean.TRUE.equals(reqVO.getSubmit())) {
        //     BpmBusinessRelationDto businessDto = new BpmBusinessRelationDto()
        //         .setBusinessType(BpmBusinessTypeEnum.PRODUCT_QUALITY_CHANGE_APPROVE.code)
        //         .setBusinessPref(recordDO.getPref());
        //     bpmBusinessRelationService.createProcessInstance(SecurityFrameworkUtils.getLoginUserId(), businessDto);
        // }

        return recordDO.getId();
    }

    /**
     * 新增主单和明细
     * @param createReqVO
     * @return
     */
    private ProductQualityChangeRecordDO createProductQualityChangeRecordAndDetail(ProductQualityChangeRecordSaveReqVO createReqVO) {
        Long tenantId = Optional.ofNullable(createReqVO.getTenantId()).orElseGet(TenantContextHolder::getRequiredTenantId);
        // 插入
        ProductQualityChangeRecordDO record = BeanUtils.toBean(createReqVO, ProductQualityChangeRecordDO.class);
        record.setTenantId(tenantId);
        record.setPref(PrefUtil.getProductQualityChangePref())
            .setApplicationTime(LocalDateTime.now())
            .setType(QualityChangeTypeEnum.PRODUCT.code)
            // 提交：记录提交人，时间，状态为已提交
            .setApprovalStatus(Optional.ofNullable(createReqVO.getApprovalStatus()).orElse(ApprovalStatus.RUNNING))
            .setApplicant(Optional.ofNullable(createReqVO.getApplicant()).orElseGet(() -> "" + WebFrameworkUtils.getLoginUserId()))
            .setApplicant(createReqVO.getCurrentHandler());

        // 插入主表
        productQualityChangeRecordMapper.insert(record);
        // 保存明细
        saveAndUpdateDetail(record, createReqVO.getDetails());
        // 返回
        return record;
    }

    /**
     * 更新主单和明细
     * @param updateReqVO
     */
    private ProductQualityChangeRecordDO updateProductQualityChangeRecordAndDetail(ProductQualityChangeRecordSaveReqVO updateReqVO) {
        // 校验存在
        ProductQualityChangeRecordDO record = validateProductQualityChangeRecordExists(updateReqVO.getId());
        // 提交：记录提交人，时间，状态为已提交（防止重复提交）
        if (Objects.equals(ApprovalStatus.NOT_START, record.getApprovalStatus())) {
            record.setApplicationTime(LocalDateTime.now())
                .setApprovalStatus(ApprovalStatus.RUNNING);
            Optional.ofNullable(updateReqVO.getApplicant()).ifPresent(record::setApplicant);
            Optional.ofNullable(updateReqVO.getCurrentHandler()).ifPresent(record::setCurrentHandler);
        }
        // 更新主表
        ProductQualityChangeRecordDO updateObj = BeanUtils.toBean(updateReqVO, ProductQualityChangeRecordDO.class);
        productQualityChangeRecordMapper.updateById(updateObj);
        // 更新明细
        saveAndUpdateDetail(record, updateReqVO.getDetails());
        return updateObj;
    }

    /**
     * 保存或更新明细
     * @param record
     * @param details
     */
    private void saveAndUpdateDetail(ProductQualityChangeRecordDO record, List<ProductQualityChangeDetailSaveReqVO> details) {
        if (record == null || CollUtil.isEmpty(details)) {
            return;
        }
        Long tenantId = record.getTenantId();
        List<ProductQualityChangeDetailDO> insertList = new ArrayList<>();
        List<ProductQualityChangeDetailDO> updateList = new ArrayList<>();
        for (ProductQualityChangeDetailSaveReqVO detail : details) {
            ProductQualityChangeDetailDO detailDO = BeanUtils.toBean(detail, ProductQualityChangeDetailDO.class);
            if (detail.getId() == null) {
                insertList.add(detailDO);
            } else {
                updateList.add(detailDO);
            }
        }
        // 查询商品信息
        List<String> itemPrefList = insertList.stream().map(ProductQualityChangeDetailDO::getItemPref).distinct().toList();
        Map<String, ProductInfoDO> productMap = CollUtil.isEmpty(itemPrefList) ? Map.of() :
            productInfoMapper.listByPref(itemPrefList).stream().collect(Collectors.toMap(ProductInfoDO::getPref, Function.identity(), (a, b) -> a));

        // 组装主单信息 + 商品信息
        insertList.forEach(detail -> {
            detail.setType(record.getType());
            detail.setRecordPref(record.getPref());
            detail.setTenantId(tenantId);
            Optional.ofNullable(productMap.get(detail.getItemPref())).ifPresent(p -> {
                detail.setItemCode(p.getShowPref());
                detail.setItemName(ProductUtil.getShowName(BeanUtil.toBean(p, ProductInfoDto.class)));
            });
        });
        if (CollUtil.isNotEmpty(insertList)) {
            productQualityChangeDetailMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            productQualityChangeDetailMapper.updateBatch(updateList);
        }
    }

    /**
     * 生成质量变更记录（商品修改）
     * @param dto
     * @param origin
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQualityChangeRecord(ProductInfoDto dto, ProductInfoDto origin) {
        if (dto == null || origin == null) {
            return;
        }
        String diffContent = ProductUtil.getQualityChangeDetailContent(dto, origin);
        if (StringUtils.isBlank(diffContent)) {
            return;
        }

        ProductQualityChangeDetailSaveReqVO detail = new ProductQualityChangeDetailSaveReqVO()
            .setItemPref(origin.getPref())
            .setContent(diffContent);

        ProductQualityChangeRecordSaveReqVO reqVO = new ProductQualityChangeRecordSaveReqVO()
            .setApprovalStatus(ApprovalStatus.UN_NEED_TODO)
            .setTenantId(origin.getTenantId())
            .setApplicant("" + Optional.ofNullable(dto.getUpdater()).orElseGet(WebFrameworkUtils::getLoginUserId))
            .setDetails(List.of(detail));

        this.saveOrUpdateQualityChange(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductQualityChangeRecord(Long id) {
        // 校验存在
        ProductQualityChangeRecordDO exist = validateProductQualityChangeRecordExists(id);
        // 删除
        productQualityChangeRecordMapper.deleteById(id);

        // 删除子表
        deleteProductQualityChangeDetailByRecordPref(exist.getPref());

        // TODO 取消审批流
    }

    private ProductQualityChangeRecordDO validateProductQualityChangeRecordExists(Long id) {
        ProductQualityChangeRecordDO record = productQualityChangeRecordMapper.selectById(id);
        if (record == null) {
            throw exception(PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS);
        }
        return record;
    }

    @Override
    public ProductQualityChangeRecordDO getProductQualityChangeRecord(Long id) {
        return productQualityChangeRecordMapper.selectById(id);
    }

    @Override
    public PageResult<ProductQualityChangeRecordDO> getProductQualityChangeRecordPage(ProductQualityChangeRecordPageReqVO pageReqVO) {
        return productQualityChangeRecordMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（质量变更申请明细记录） ====================

    @Override
    public PageResult<ProductQualityChangeDetailDO> getProductQualityChangeDetailPage(PageParam pageReqVO, String recordPref) {
        return productQualityChangeDetailMapper.selectPage(pageReqVO, recordPref);
    }

    @Override
    public Long createProductQualityChangeDetail(ProductQualityChangeDetailDO productQualityChangeDetail) {
        productQualityChangeDetailMapper.insert(productQualityChangeDetail);
        return productQualityChangeDetail.getId();
    }

    @Override
    public void updateProductQualityChangeDetail(ProductQualityChangeDetailDO productQualityChangeDetail) {
        // 校验存在
        validateProductQualityChangeDetailExists(productQualityChangeDetail.getId());
        // 更新
        productQualityChangeDetail.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        productQualityChangeDetailMapper.updateById(productQualityChangeDetail);
    }

    @Override
    public void deleteProductQualityChangeDetail(Long id) {
        // 校验存在
        validateProductQualityChangeDetailExists(id);
        // 删除
        productQualityChangeDetailMapper.deleteById(id);
    }

    @Override
    public ProductQualityChangeDetailDO getProductQualityChangeDetail(Long id) {
        return productQualityChangeDetailMapper.selectById(id);
    }

    private void validateProductQualityChangeDetailExists(Long id) {
        if (productQualityChangeDetailMapper.selectById(id) == null) {
            throw exception(PRODUCT_QUALITY_CHANGE_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteProductQualityChangeDetailByRecordPref(String recordPref) {
        productQualityChangeDetailMapper.deleteByRecordPref(recordPref);
    }

}