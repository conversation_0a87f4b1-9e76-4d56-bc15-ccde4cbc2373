package com.xyy.saas.inquiry.product.server.dal.redis;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    /**
     * 商品六级分类树结构缓存
     * <p>
     */
    String PRODUCT_CATEGORY_TREE_KEY = "product:category_tree";

    /**
     * 商品标准库同步锁
     * <p>
     * KEY 格式：product:stdlib_sync:lock:{guid} VALUE 数据格式：String
     */
    String PRODUCT_STDLIB_SYNC_LOCK_KEY = "product:stdlib_sync:lock:";
}
