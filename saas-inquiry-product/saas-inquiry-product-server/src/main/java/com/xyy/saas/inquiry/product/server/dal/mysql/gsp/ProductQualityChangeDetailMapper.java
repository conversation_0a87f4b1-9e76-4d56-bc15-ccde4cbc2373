package com.xyy.saas.inquiry.product.server.dal.mysql.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 质量变更申请明细记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductQualityChangeDetailMapper extends BaseMapperX<ProductQualityChangeDetailDO> {

    default PageResult<ProductQualityChangeDetailDO> selectPage(ProductQualityChangeDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductQualityChangeDetailDO>()
            .eqIfPresent(ProductQualityChangeDetailDO::getRecordPref, reqVO.getRecordPref())
            .eqIfPresent(ProductQualityChangeDetailDO::getType, reqVO.getType())
            .eqIfPresent(ProductQualityChangeDetailDO::getItemPref, reqVO.getItemPref())
            .eqIfPresent(ProductQualityChangeDetailDO::getItemCode, reqVO.getItemCode())
            .likeIfPresent(ProductQualityChangeDetailDO::getItemName, reqVO.getItemName())
            .eqIfPresent(ProductQualityChangeDetailDO::getContent, reqVO.getContent())
            .eqIfPresent(ProductQualityChangeDetailDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(ProductQualityChangeDetailDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(ProductQualityChangeDetailDO::getId));
    }

    default PageResult<ProductQualityChangeDetailDO> selectPage(PageParam reqVO, String recordPref) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductQualityChangeDetailDO>()
            .eq(ProductQualityChangeDetailDO::getRecordPref, recordPref)
            .orderByDesc(ProductQualityChangeDetailDO::getId));
    }

    default int deleteByRecordPref(String recordPref) {
        return delete(ProductQualityChangeDetailDO::getRecordPref, recordPref);
    }

}