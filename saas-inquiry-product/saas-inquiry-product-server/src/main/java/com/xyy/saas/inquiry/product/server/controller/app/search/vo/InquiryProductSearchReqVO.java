package com.xyy.saas.inquiry.product.server.controller.app.search.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/25 20:41
 */
@Schema(description = "App - 问诊商品搜索 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryProductSearchReqVO extends PageParam {

    @Schema(description = "商品编码")
    private String pref;

    @Schema(description = "商品类型0西药 1中药", requiredMode = Schema.RequiredMode.REQUIRED)
    @InEnum(value = MedicineTypeEnum.class)
    private Integer medicineType;

    @Schema(description = "品牌名")
    private String productName;

    @Schema(description = "规格")
    private String attributeSpecification;

    @Schema(description = "条形码")
    private String barCode;

}
