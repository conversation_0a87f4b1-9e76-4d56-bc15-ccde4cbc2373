package com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 商品流转记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductTransferRecordPageReqVO extends PageParam {

    @Schema(description = "商品编号", example = "9472")
    private String productPref;

    @Schema(description = "同步类型", example = "1")
    private Integer type;

    @Schema(description = "源租户编号", example = "24064")
    private Long sourceTenantId;

    @Schema(description = "目标租户编号", example = "14352")
    private Long targetTenantId;

    @Schema(description = "同步状态", example = "1")
    private Integer status;

    @Schema(description = "同步结果")
    private String result;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}