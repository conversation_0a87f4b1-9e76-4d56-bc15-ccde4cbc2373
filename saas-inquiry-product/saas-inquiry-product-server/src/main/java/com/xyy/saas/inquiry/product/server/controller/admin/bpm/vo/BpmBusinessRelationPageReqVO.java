package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 审批流关联业务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmBusinessRelationPageReqVO extends PageParam {

    @Schema(description = "租户编号", example = "21079")
    private Long tenantId;
    @Schema(description = "租户编号（总部）", example = "21079")
    private Long headTenantId;

    @Schema(description = "业务类型", example = "1")
    private Integer businessType;

    @Schema(description = "业务单据编号", example = "22519")
    private String businessPref;

    @Schema(description = "流程实例的编号", example = "4711")
    private String processInstanceId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "申请人")
    private String applicant;

    @Schema(description = "审批状态", example = "1")
    private Integer approvalStatus;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}