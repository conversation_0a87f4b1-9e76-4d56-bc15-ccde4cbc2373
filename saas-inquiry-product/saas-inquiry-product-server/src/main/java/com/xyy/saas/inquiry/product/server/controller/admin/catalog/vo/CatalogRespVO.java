package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 目录 Response VO")
@Data
@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class CatalogRespVO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5280")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "目录编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目录编码")
    private String pref;

    @Schema(description = "目录名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("目录名称")
    private String name;

    @Schema(description = "业务类型（1:医保 3:互联网监管）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型（1:医保 3:互联网监管）")
    private Integer type;

    @Schema(description = "项目编码类型（11:医保项目编码 31:自建标准库ID 32:中台标准库ID）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("项目编码类型（11:医保项目编码 31:自建标准库ID 32:中台标准库ID）")
    private Integer projectCodeType;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Integer version;

    @Schema(description = "版本编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本编码")
    private String versionCode;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "上传文件链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("上传文件链接")
    private String uploadUrl;

    @Schema(description = "是否需要下载", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否需要下载")
    private Boolean needDownload;

    @Schema(description = "下载文件链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("下载文件链接")
    private String downloadUrl;

    @Schema(description = "目录总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "15495")
    @ExcelProperty("目录总数")
    private Integer totalCount;

    @Schema(description = "已匹配数", requiredMode = Schema.RequiredMode.REQUIRED, example = "14781")
    @ExcelProperty("已匹配数")
    private Integer matchedCount;

    @Schema(description = "未匹配数", requiredMode = Schema.RequiredMode.REQUIRED, example = "30548")
    @ExcelProperty("未匹配数")
    private Integer unmatchedCount;

    @Schema(description = "是否禁用，默认否", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "环境（1测试、2灰度、3上线）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("环境（1测试、2灰度、3上线）")
    private Integer env;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本药品集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailList;

    @Schema(description = "导入成功条数", example = "1")
    private Integer exportSuccessCount;

    @Schema(description = "导入失败条数", example = "1")
    private Integer exportFailCount;

    @Schema(description = "导入失败excel", example = "https://www.iocoder.cn")
    private String failExcelUrl;

    @Schema(description = "绑定租户数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantCount;
}