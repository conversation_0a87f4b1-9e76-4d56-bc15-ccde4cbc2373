package com.xyy.saas.inquiry.product.server.mq.consumer.mid;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum;
import com.xyy.saas.inquiry.product.server.config.mq.annotation.ExternalMQConsumer;
import com.xyy.saas.inquiry.product.server.config.mq.enums.ExternalMQClusterEnum;
import com.xyy.saas.inquiry.product.server.config.mq.handler.ExternalMessageHandler;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.transfer.ProductTransferRecordMapper;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidProductPresentResponseMsg;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_MID_STDLIB_SYNC_FAILED;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_STDLIB_NOT_EXISTS;

/**
 * 中台商品提报响应消息：
 * 1. 新品提报
 * 2. 三方外采上报
 */
@Slf4j
@Component
@ExternalMQConsumer(
    topic = "XYY_ME_PRODUCT_SAAS_PHARMACY_TOPIC",
    consumerGroup = "com_xyy_saas_inquiry_product_server_mq_consumer_mid_MidProductPresentResponseConsumer",
    cluster = ExternalMQClusterEnum.MIDDLE
)
@RefreshScope
public class MidProductPresentResponseConsumer implements ExternalMessageHandler {

    @Resource
    private ProductInfoService productInfoService;
    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductTransferRecordMapper productTransferRecordMapper;

    /**
     * 消息延迟次数，默认2次（10秒、30秒、1分钟、2分钟、、）
     */
    @Value("${saas.product.mq.present.response.re-consumer-times:2}")
    private Integer reConsumerTimes;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConsumeConcurrentlyStatus handleMessage(List<MessageExt> msgList) throws Exception {
        if (CollectionUtils.isEmpty(msgList)) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        for (MessageExt msg : msgList) {
            String body = new String(msg.getBody(), StandardCharsets.UTF_8);

            try {
                // 1. 解析消息
                MidProductPresentResponseMsg callbackMsg = JSON.parseObject(body, MidProductPresentResponseMsg.class);
                if (callbackMsg == null) {
                    log.error("【中台商品提报响应】解析消息失败, msgId: {}, body: {}", msg.getMsgId(), body);
                    continue;
                }

                // 2. 校验消息
                if (!Objects.equals((byte) 2, callbackMsg.getSendType())) {
                    continue;
                }

                // 3. 检查数据
                String productPref = callbackMsg.getOutsideCode();
                if (StringUtils.isBlank(productPref) || !productPref.startsWith(PrefUtil.PREF_KEY_PREFIX_PRODUCT_PREF)) {
                    // log.error("提报商品编码为空或格式错误, 跳过！ pref: {}", productPref);
                    continue;
                }
                log.info("【中台商品提报响应】收到消息, msgId: {}, body: {}", msg.getMsgId(), body);

                // 消息延迟消费（提报审批通过后，中台更新ES有延迟）
                if (msg.getReconsumeTimes() < reConsumerTimes) {
                    log.info("【中台商品提报响应】消息延迟消费, 等待中台ES更新！ pref: {}", productPref);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }

                // 3.1. 查询商品信息
                List<ProductInfoDto> productList = productInfoService.listProductInfoByPref(List.of(productPref));
                if (CollectionUtils.isEmpty(productList) || productList.getFirst() == null) {
                    log.error("【中台商品提报响应】未找到商品信息, 跳过！ pref: {}", productPref);
                    continue;
                }
                ProductInfoDto product = productList.getFirst();

                // 4. 处理消息
                boolean pass = handleMessage(product, callbackMsg);

                // 5. 查询商品流转记录（未完结状态）, 根据商品pref + type
                List<ProductTransferRecordDO> transferRecordDOList = getLatestTransferRecord(productPref);

                if (ObjectUtils.isNotEmpty(transferRecordDOList)) {
                    List<Long> trIdList = transferRecordDOList.stream().map(ProductTransferRecordDO::getId).toList();
                    log.warn("【中台商品提报响应】更新商品流转记录, pref: {}, syncType: {}, transferRecordIdList: {}", productPref, ProductTransferTypeEnum.REPORT_MID.desc, trIdList);
                    ProductTransferRecordDO updDo = new ProductTransferRecordDO();
                    // 5.1. 中台审核通过后，更新提报记录冗余的商品信息
                    if (pass) {
                        ProductTransferRecordSaveReqVO updateReqVO = new ProductTransferRecordSaveReqVO().of(product);
                        BeanUtils.copyProperties(updateReqVO, updDo);
                        // 查询商品信息
                        productInfoService.listProductInfoByPref(List.of(productPref)).stream().findFirst().ifPresent(productInfoDto -> {
                            updDo.setCommonName(productInfoDto.getCommonName())
                                .setBrandName(productInfoDto.getBrandName())
                                .setMnemonicCode(productInfoDto.getMnemonicCode())
                                .setBarcode(productInfoDto.getBarcode())
                                .setSpec(productInfoDto.getSpec())
                                .setManufacturer(productInfoDto.getManufacturer())
                                .setApprovalNumber(productInfoDto.getApprovalNumber());
                        });
                        updDo.setStatus(ProductTransferStatusEnum.SUCCESS.code);
                    } else {
                        updDo.setStatus(ProductTransferStatusEnum.MID_AUDIT_REJECT.code);
                    }
                    // 5.2. 更新商品流转记录（商品信息也要修改）
                    updDo.setResult(JSON.toJSONString(callbackMsg))
                        .setRemark(callbackMsg.getRemark());

                    // 批量更新
                    productTransferRecordMapper.update(updDo, new LambdaUpdateWrapper<ProductTransferRecordDO>().in(ProductTransferRecordDO::getId, trIdList));
                }

                log.info("【中台商品提报响应】处理消息成功, msgId: {}, pref: {}", msg.getMsgId(), productPref);
            } catch (Exception e) {
                log.error("【中台商品提报响应】处理消息异常, msgId: {}, body: {}, error: {}",
                    msg.getMsgId(), body, e.getMessage(), e);
                throw e; // 抛出异常触发重试
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理匹配成功提报响应消息,
     * 返回是否审核通过，false为审核驳回
     */
    private boolean handleMessage(ProductInfoDto product, MidProductPresentResponseMsg msg) {
        String productPref = product.getPref();
        long midStdlibId = NumberUtils.toLong(msg.getProductId());
        // 2. 更新商品中台标准库ID
        ProductInfoDto updateDto = ProductInfoDto.builder()
            .id(product.getId())
            .stdlibId(product.getStdlibId())
            .tenantId(product.getTenantId())
            .build();

        // 新品审核成功 or 三方外采匹配成功
        if (midStdlibId > 0) {
            log.info("【中台商品提报响应】绑定中台标准库ID, pref: {}, midStdlibId: {}", productPref, midStdlibId);
            updateDto.setMidStdlibId(midStdlibId);

            // 更新自建标品信息
            stdlibService.saveOrUpdateStdlibFromMid(List.of(midStdlibId), true).stream().findFirst()
                .map(stdlib -> {
                    // 更新门店商品信息（冗余标准库信息）
                    ProductConvert.INSTANCE.ProductStdlibDO2ProductInfoDto(stdlib, updateDto);
                    return updateDto.setStdlibId(product.getStdlibId());
                }).orElseThrow(() -> {
                    // 查不到中台信息，则抛异常，过会再消费
                    log.error("【中台商品提报响应】中台标准库数据同步失败，稍后再试。midStdlibId: {}", midStdlibId);
                    return exception(PRODUCT_MID_STDLIB_SYNC_FAILED);
                });

            productInfoService.saveOrUpdateProduct(updateDto, null);
            log.info("【中台商品提报响应】审核通过, 状态: {} -> {}, pref: {}, stdlibId: {}, midStdlibId: {}",
                ProductStatusEnum.getDescByCode(product.getStatus()),
                ProductStatusEnum.getDescByCode(updateDto.getStatus()),
                productPref, updateDto.getStdlibId(), midStdlibId);
        }
        // 审核驳回 | 审核不通过
        if (Objects.equals(2, msg.getStatusCode()) || Objects.equals(4, msg.getStatusCode())) {
            updateDto.setStatus(ProductStatusEnum.MID_AUDIT_REJECT.code);
            productInfoService.saveOrUpdateProduct(updateDto, ProductBizTypeEnum.EDIT_PRODUCT);
            log.info("【中台商品提报响应】审核驳回, 状态: {} -> {}, pref: {}, stdlibId: {}, midStdlibId: {}",
                ProductStatusEnum.getDescByCode(product.getStatus()),
                ProductStatusEnum.getDescByCode(updateDto.getStatus()),
                productPref, product.getStdlibId(), midStdlibId);
            return false;
        }
        return true;
    }

    private List<ProductTransferRecordDO> getLatestTransferRecord(String productPref) {
        // 先查全部的待审批的提报记录
        List<ProductTransferRecordDO> transferRecordDOList = productTransferRecordMapper.selectList(new LambdaUpdateWrapper<ProductTransferRecordDO>()
            .eq(ProductTransferRecordDO::getDisable, false)
            .eq(ProductTransferRecordDO::getProductPref, productPref)
            .eq(ProductTransferRecordDO::getType, ProductTransferTypeEnum.REPORT_MID.code)
            .eq(ProductTransferRecordDO::getStatus, ProductTransferStatusEnum.INIT.code));

        if (CollectionUtils.isNotEmpty(transferRecordDOList)) {
            return transferRecordDOList;
        }
        // 如果没有就查最近一次的提报记录
        return productTransferRecordMapper.selectList(new LambdaUpdateWrapper<ProductTransferRecordDO>()
            .eq(ProductTransferRecordDO::getDisable, false)
            .eq(ProductTransferRecordDO::getProductPref, productPref)
            .eq(ProductTransferRecordDO::getType, ProductTransferTypeEnum.REPORT_MID.code)
            .orderByDesc(ProductTransferRecordDO::getId)
            .last("LIMIT 1"));
    }
} 