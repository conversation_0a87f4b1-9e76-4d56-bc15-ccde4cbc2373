package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品基本信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductInfoMapper extends BaseMapperX<ProductInfoDO> {

    default List<ProductInfoDO> listByPref(List<String> prefList) {
        if (CollectionUtils.isEmpty(prefList)) {
            return List.of();
        }
        return selectList(new LambdaQueryWrapperX<ProductInfoDO>()
            .in(ProductInfoDO::getPref, prefList.stream().distinct().toList())
            .orderByDesc(ProductInfoDO::getCreateTime));
    }

    default List<ProductInfoDO> listByStdlibId(Long tenantId, List<Long> stdlibIdList) {
        if (CollectionUtils.isEmpty(stdlibIdList)) {
            return List.of();
        }
        return selectList(new LambdaQueryWrapperX<ProductInfoDO>()
            .eqIfPresent(ProductInfoDO::getTenantId, tenantId)
            .in(ProductInfoDO::getStdlibId, stdlibIdList.stream().distinct().toList())
            .orderByDesc(ProductInfoDO::getCreateTime));
    }

    default List<ProductInfoDO2> listByIdOrPref(List<Long> idList, List<String> prefList, TenantDto tenant, Boolean deleted) {
        if ((CollectionUtils.isEmpty(idList) && CollectionUtils.isEmpty(prefList))) {
            return List.of();
        }

        ProductInfoPageReqVO pageReqVO = new ProductInfoPageReqVO()
            .setIdList(idList)
            .setPrefList(prefList)
            .setDeleted(deleted);
        // 如果有传租户，则会查询指定租户下的数据
        if (tenant != null) {
            pageReqVO.setTenantId(tenant.getId())
                .setHeadTenantId(tenant.getHeadTenantId());
        }
        return selectPageByParam(new Page<>(1, PageParam.PAGE_SIZE_NONE), pageReqVO).getRecords();
    }

    /**
     * 获取展示编码列表
     * @param tenantId
     * @return
     */
    List<String> listShowPref(@Param("tenantId") Long tenantId);

    /**
     * 根据租户id、（展示编码 | 标准库id） 查询唯一索引是否存在
     * @param tenantId
     * @param showPref
     * @param stdlibId
     * @return
     */
    ProductInfoDO uniqueIndexExists(@Param("tenantId") Long tenantId, @Param("showPref") String showPref, @Param("stdlibId") Long stdlibId);

    /**
     * 分页查询商品信息
     * @param page
     * @param param
     * @return
     */
    Page<ProductInfoDO2> selectPageByParam(Page<ProductInfoDO> page, @Param("param") ProductInfoPageReqVO param);


    /**
     * 根据列表修改删除相关字段数据
     * @param idList
     * @param deleted
     * @param deleteType
     * @return
     */
    int batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deleted") boolean deleted, @Param("deleteType") Integer deleteType);

    /**
     * 根据pref列表物理删除数据
     * @param prefList
     * @return
     */
    int hardDeleteByPrefList(List<String> prefList);

    /**
     * 获取过期的回收站商品编码列表
     * 
     * @param expireTime 过期时间
     * @return 商品编码列表
     */
    List<String> listExpiredRecyclePref(@Param("expireTime") LocalDateTime expireTime);

}