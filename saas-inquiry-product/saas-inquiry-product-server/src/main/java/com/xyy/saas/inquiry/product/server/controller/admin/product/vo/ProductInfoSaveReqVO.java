package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import com.xyy.saas.inquiry.annotation.FieldCompare;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 商品基本信息新增/修改 Request VO")
@Data
public class ProductInfoSaveReqVO {

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21893")
    private Long id;

    @Schema(description = "商品外码（自动生成或填写，机构唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String showPref;

    @Schema(description = "中台标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    private Long midStdlibId;

    @Schema(description = "标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    private Long stdlibId;

    @Schema(description = "助记码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mnemonicCode;

    @Schema(description = "通用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "通用名不能为空")
    private String commonName;

    @Schema(description = "品牌名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    // @NotEmpty(message = "品牌名不能为空")
    private String brandName;

    @Schema(description = "规格/型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规格/型号不能为空")
    private String spec;

    @Schema(description = "条形码", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "条形码不能为空")
    private String barcode;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "生产厂家不能为空")
    private String manufacturer;

    @Schema(description = "批准文号（商品分类为医疗器械时，变为备案/注册证）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批准文号不能为空")
    private String approvalNumber;

    @Schema(description = "本位码", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "本位码不能为空")
    private String standardCode;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "23148")
    // @NotNull(message = "单位不能为空")
    private String unit;

    @Schema(description = "剂型", requiredMode = Schema.RequiredMode.REQUIRED, example = "21146")
    // @NotNull(message = "剂型不能为空")
    private String dosageForm;

    @Schema(description = "所属范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "6843")
    // @NotNull(message = "所属范围不能为空")
    private String businessScope;

    @Schema(description = "处方分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "8689")
    // @NotNull(message = "处方分类不能为空")
    private String presCategory;

    @Schema(description = "储存条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1588")
    // @NotNull(message = "储存条件不能为空")
    private String storageWay;

    @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "产地不能为空")
    private String origin;

    @Schema(description = "生产企业社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "生产企业社会信用代码不能为空")
    private String manufacturerUscc;

    @Schema(description = "有效期,例12个月,36个月", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "有效期不能为空")
    private String productValidity;

    @Schema(description = "批准文号有效期")
    private LocalDate approvalValidityPeriod;

    @Schema(description = "上市许可持有人", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "上市许可持有人不能为空")
    private String marketingAuthorityHolder;

    @Schema(description = "进项税率")
    // @NotNull(message = "进项税率不能为空")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    // @NotNull(message = "销项税率不能为空")
    private BigDecimal outputTaxRate;

    @Schema(description = "药品标识码", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "药品标识码不能为空")
    private String drugIdentCode;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    // @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "商品封面图", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品封面图不能为空")
    private List<String> coverImages;

    @Schema(description = "商品外包装图", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> outerPackageImages;

    @Schema(description = "商品说明书图", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> instructionImages;



    // 多属性标志
    @Schema(description = "多属性标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductFlag productFlag;

    // 使用信息（价格，医保匹配信息）
    @Schema(description = "使用信息（价格，医保匹配信息）", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private ProductUseInfoDto useInfo;

    // 资质信息
    @Schema(description = "资质信息", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private ProductQualificationInfoDto qualificationInfo;

}