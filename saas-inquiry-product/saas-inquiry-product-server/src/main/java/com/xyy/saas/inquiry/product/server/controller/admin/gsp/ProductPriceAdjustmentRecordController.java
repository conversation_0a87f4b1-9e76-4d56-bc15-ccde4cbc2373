package com.xyy.saas.inquiry.product.server.controller.admin.gsp;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 售价调整单")
@RestController
@RequestMapping("/product/price-adjustment")
@Validated
public class ProductPriceAdjustmentRecordController {

    @Resource
    private ProductPriceAdjustmentRecordService productPriceAdjustmentRecordService;

    @PostMapping("/save")
    @Operation(summary = "暂存/提交售价调整单")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:create')")
    public CommonResult<Long> submitPriceAdjustment(@Valid @RequestBody ProductPriceAdjustmentRecordSaveReqVO reqVO) {
        return success(productPriceAdjustmentRecordService.saveOrUpdatePriceAdjustment(reqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除售价调整单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:delete')")
    public CommonResult<Boolean> deleteProductPriceAdjustmentRecord(@RequestParam("id") Long id) {
        productPriceAdjustmentRecordService.deleteProductPriceAdjustmentRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得售价调整单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:query')")
    public CommonResult<ProductPriceAdjustmentRecordRespVO> getProductPriceAdjustmentRecord(@RequestParam("id") Long id) {
        ProductPriceAdjustmentRecordDO productPriceAdjustmentRecord = productPriceAdjustmentRecordService.getProductPriceAdjustmentRecord(id);
        return success(BeanUtils.toBean(productPriceAdjustmentRecord, ProductPriceAdjustmentRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得售价调整单分页")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:query')")
    public CommonResult<PageResult<ProductPriceAdjustmentRecordRespVO>> getProductPriceAdjustmentRecordPage(@Valid ProductPriceAdjustmentRecordPageReqVO pageReqVO) {
        PageResult<ProductPriceAdjustmentRecordDO> pageResult = productPriceAdjustmentRecordService.getProductPriceAdjustmentRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductPriceAdjustmentRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出售价调整单 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductPriceAdjustmentRecordExcel(@Valid ProductPriceAdjustmentRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductPriceAdjustmentRecordDO> list = productPriceAdjustmentRecordService.getProductPriceAdjustmentRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "售价调整单.xls", "数据", ProductPriceAdjustmentRecordRespVO.class,
                        BeanUtils.toBean(list, ProductPriceAdjustmentRecordRespVO.class));
    }

    // ==================== 子表（售价调整单明细） ====================

    @GetMapping("/product-price-adjustment-detail/page")
    @Operation(summary = "获得售价调整单明细分页")
    @Parameter(name = "recordId", description = "单据ID")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:query')")
    public CommonResult<PageResult<ProductPriceAdjustmentDetailDO>> getProductPriceAdjustmentDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("recordPref") String recordPref) {
        return success(productPriceAdjustmentRecordService.getProductPriceAdjustmentDetailPage(pageReqVO, recordPref));
    }

    @PostMapping("/product-price-adjustment-detail/create")
    @Operation(summary = "创建售价调整单明细")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:create')")
    public CommonResult<Long> createProductPriceAdjustmentDetail(@Valid @RequestBody ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail) {
        return success(productPriceAdjustmentRecordService.createProductPriceAdjustmentDetail(productPriceAdjustmentDetail));
    }

    @PutMapping("/product-price-adjustment-detail/update")
    @Operation(summary = "更新售价调整单明细")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:update')")
    public CommonResult<Boolean> updateProductPriceAdjustmentDetail(@Valid @RequestBody ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail) {
        productPriceAdjustmentRecordService.updateProductPriceAdjustmentDetail(productPriceAdjustmentDetail);
        return success(true);
    }

    @DeleteMapping("/product-price-adjustment-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除售价调整单明细")
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:delete')")
    public CommonResult<Boolean> deleteProductPriceAdjustmentDetail(@RequestParam("id") Long id) {
        productPriceAdjustmentRecordService.deleteProductPriceAdjustmentDetail(id);
        return success(true);
    }

	@GetMapping("/product-price-adjustment-detail/get")
	@Operation(summary = "获得售价调整单明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:price-adjustment:query')")
	public CommonResult<ProductPriceAdjustmentDetailDO> getProductPriceAdjustmentDetail(@RequestParam("id") Long id) {
	    return success(productPriceAdjustmentRecordService.getProductPriceAdjustmentDetail(id));
	}


}