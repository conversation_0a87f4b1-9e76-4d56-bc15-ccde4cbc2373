package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 商品基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductInfoPageReqVO extends PageParam {

    @Schema(description = "商品ID列表")
    private List<Long> idList;
    @Schema(description = "商品编码列表")
    private List<String> prefList;

    @Schema(description = "租户编码")
    private Long tenantId;

    @Schema(description = "租户编码（总部）")
    private Long headTenantId;

    @Schema(description = "标准库ID", example = "21163")
    private Long stdlibId;

    @Schema(description = "商品信息（商品编码，通用名，商品名，条形码，助记码）", example = "21163")
    private String mixedQuery;

    @Schema(description = "单位", example = "23148")
    private String unit;

    @Schema(description = "剂型", example = "21146")
    private String dosageForm;

    @Schema(description = "所属范围", example = "6843")
    private String businessScope;

    @Schema(description = "处方分类", example = "8689")
    private String presCategory;

    @Schema(description = "储存条件", example = "1588")
    private String storageWay;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "是否禁用", example = "2")
    private Boolean disable;

    @Schema(description = "删除类型", example = "1")
    private Integer deleteType;

    @Schema(description = "删除时间")
    private LocalDateTime[] deletedAt;

    @Schema(description = "是否删除")
    private Boolean deleted = false;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    // 多属性标志
    @Schema(description = "多属性标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductFlag productFlag;

}