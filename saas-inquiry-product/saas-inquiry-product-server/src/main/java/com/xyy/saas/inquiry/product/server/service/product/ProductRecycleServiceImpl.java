package com.xyy.saas.inquiry.product.server.service.product;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_DELETE_TYPE_UN_SUPPORT;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_STATUS_UN_SUPPORT_DELETE;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductDeleteTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationCancelReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecyclePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecycleSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductQualificationInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 商品回收站 Service 实现类
 */
@Slf4j
@Service
@Validated
public class ProductRecycleServiceImpl implements ProductRecycleService {

    @Resource
    private ProductInfoMapper productInfoMapper;
    @Resource
    private ProductUseInfoMapper productUseInfoMapper;
    @Resource
    private ProductQualificationInfoMapper productQualificationInfoMapper;
    @Resource
    private ProductQualityChangeRecordService productQualityChangeRecordService;
    @Resource
    private ProductPriceAdjustmentRecordService productPriceAdjustmentRecordService;

    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;

    @Resource
    private TenantApi tenantApi;

    @Override
    public void softDeleteProduct(@Valid ProductRecycleSaveReqVO saveReqVO) {
        ProductDeleteTypeEnum deleteTypeEnum = ProductDeleteTypeEnum.getByCode(saveReqVO.getDeleteType());
        if (deleteTypeEnum == null) {
            throw exception(PRODUCT_DELETE_TYPE_UN_SUPPORT);
        }

        batchUpdateDeleted(saveReqVO.getIdList(), true, deleteTypeEnum.code);
    }

    @Override
    public void restoreProduct(List<Long> idList) {
        batchUpdateDeleted(idList, false, null);
    }

    private void batchUpdateDeleted(List<Long> idList, boolean deleted, Integer deleteType) {
        List<ProductInfoDO2> productInfoDO2List = productInfoMapper.listByIdOrPref(idList, null, null, !deleted);
        if (CollectionUtils.isEmpty(productInfoDO2List)) {
            return;
        }
        // 判断商品状态，是否允许删除
        // for (ProductInfoDO2 productInfoDO2 : productInfoDO2List) {
        //     if (productInfoDO2.getStatus() != ProductStatusEnum.USING.code) {
        //         throw exception(PRODUCT_STATUS_UN_SUPPORT_DELETE);
        //     }
        // }
        List<Long> idList2 = productInfoDO2List.stream().map(ProductInfoDO::getId).toList();
        // 批量修改
        productInfoMapper.batchUpdateDeleted(idList2, deleted, deleteType);
    }

    @Override
    public void hardDeleteProduct(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        // 查询
        List<ProductInfoDO2> productInfoDO2List = productInfoMapper.listByIdOrPref(idList, null, null, true);
        if (CollectionUtils.isEmpty(productInfoDO2List)) {
            return;
        }
        List<String> prefList = productInfoDO2List.stream().map(ProductInfoDO::getPref).toList();
        // 硬删除
        handleHardDeleteProduct(prefList);

        // 根据 tenantId 分组
        productInfoDO2List.stream().collect(Collectors.groupingBy(ProductInfoDO::getTenantId))
            .forEach((tenantId, list) -> {
                // 删除审批流
                bpmBusinessRelationService.deleteProcessInstance(new BpmBusinessRelationCancelReqVO()
                    .setTenantId(tenantId)
                    // .setBusinessType(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code)
                    .setBusinessPrefList(list.stream().map(ProductInfoDO::getPref).toList())
                    .setReason("商品回收站永久删除"));
            });
    }

    private void handleHardDeleteProduct(List<String> prefList) {
        // 删除
        productInfoMapper.hardDeleteByPrefList(prefList);
        // 删除资质信息
        productQualificationInfoMapper.hardDeleteByProductPrefList(prefList);
        // 删除使用信息
        productUseInfoMapper.hardDeleteByProductPrefList(prefList);
    }

    @Override
    public PageResult<ProductInfoDO> getRecyclePage(@Valid ProductRecyclePageReqVO pageReqVO) {
        Long tenantId = pageReqVO.getTenantId();
        // 总部租户
        TenantDto tenantDto = tenantId == null ? tenantApi.getTenant() : tenantApi.getTenant(tenantId);
        Long headTenantId = tenantDto.getHeadTenantId();

        Page<ProductInfoDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());

        ProductInfoPageReqVO reqVO = BeanUtils.toBean(pageReqVO, ProductInfoPageReqVO.class);
        reqVO.setDeleted(true)
            .setTenantId(null)  // 不查询使用信息
            .setHeadTenantId(headTenantId);

        Page<ProductInfoDO2> productInfoDO2Page = productInfoMapper.selectPageByParam(page, reqVO);

        return new PageResult<>(BeanUtils.toBean(productInfoDO2Page.getRecords(), ProductInfoDO.class), productInfoDO2Page.getTotal());
    }

    @Override
    public int cleanExpiredRecycleData(Integer retainDays) {
        log.info("[cleanExpiredRecycleData] 清理商品回收站数据 retainDays: {}", retainDays);
        if (retainDays == null) {
            return 0;
        }
        // 1. 查询需要清理的商品编码列表
        LocalDateTime expireTime = LocalDateTime.now().minusDays(retainDays);
        List<String> expiredPrefList = productInfoMapper.listExpiredRecyclePref(expireTime);
        if (CollectionUtils.isEmpty(expiredPrefList)) {
            return 0;
        }

        // 2. 执行清理
        handleHardDeleteProduct(expiredPrefList);
        int size = expiredPrefList.size();
        log.info("[cleanExpiredRecycleData] 清理商品回收站数据，清理成功, size: {}", size);
        return size;
    }

} 