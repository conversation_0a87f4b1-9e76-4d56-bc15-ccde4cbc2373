package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 售价调整单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPriceAdjustmentDetailPageReqVO extends PageParam {

    @Schema(description = "单据编号", example = "16932")
    private String recordPref;

    @Schema(description = "适用门店", example = "2585")
    private Long applicableTenantId;

    @Schema(description = "商品编码", example = "22067")
    private String productPref;

    @Schema(description = "原零售价", example = "14358")
    private BigDecimal oldRetailPrice;

    @Schema(description = "原会员价", example = "27197")
    private BigDecimal oldMemberPrice;

    @Schema(description = "新零售价", example = "3727")
    private BigDecimal newRetailPrice;

    @Schema(description = "新会员价", example = "31530")
    private BigDecimal newMemberPrice;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}