package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecyclePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductRecycleRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.service.product.ProductRecycleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 商品回收站")
@RestController
@RequestMapping("/product/recycle")
@Validated
public class ProductRecycleController {

    @Resource
    private ProductRecycleService productRecycleService;

    @PutMapping("/restore")
    @Operation(summary = "恢复商品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:recycle:restore')")
    public CommonResult<Boolean> restoreProduct(@RequestParam("id") Long id) {
        productRecycleService.restoreProduct(List.of(id));
        return success(true);
    }

    @DeleteMapping("/remove")
    @Operation(summary = "永久删除商品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:recycle:remove')")
    public CommonResult<Boolean> removeProduct(@RequestParam("id") Long id) {
        productRecycleService.hardDeleteProduct(List.of(id));
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品回收站分页")
    @PreAuthorize("@ss.hasPermission('saas:product:recycle:query')")
    public CommonResult<PageResult<ProductRecycleRespVO>> getRecyclePage(@Valid ProductRecyclePageReqVO pageVO) {
        PageResult<ProductInfoDO> pageResult = productRecycleService.getRecyclePage(pageVO);
        return success(BeanUtils.toBean(pageResult, ProductRecycleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品回收站 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:recycle:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRecycleExcel(@Valid ProductRecyclePageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<ProductInfoDO> pageResult = productRecycleService.getRecyclePage(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "商品回收站.xls", "数据", ProductRecycleRespVO.class,
            BeanUtils.toBean(pageResult.getList(), ProductRecycleRespVO.class));
    }

} 