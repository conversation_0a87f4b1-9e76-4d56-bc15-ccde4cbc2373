package com.xyy.saas.inquiry.product.server.service.catalog;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_ENV_NOT_UPDATE;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_NAME_IS_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_RELATION_TENANT_COUNT_IS_NOT_NULL;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.CATALOG_UPLOAD_URL_IS_EMPTY;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.UPGRADE_CATALOG_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.UPGRADE_CATALOG_VERSION_CODE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import com.xyy.saas.inquiry.product.enums.CatalogEnvEnum;
import com.xyy.saas.inquiry.product.enums.ProductProjectCodeTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.convert.catalog.CatalogConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.CatalogMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.catalog.RegulatoryCatalogDetailMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 目录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CatalogServiceImpl implements CatalogService {

    @Resource
    private CatalogMapper catalogMapper;
    @Resource
    private RegulatoryCatalogDetailMapper regulatoryCatalogDetailMapper;
    @Resource
    private ProductStdlibMapper productStdlibMapper;
    @Resource
    private RegulatoryCatalogDetailService regulatoryCatalogDetailService;
    @Resource
    private TenantServicePackRelationApi tenantServicePackRelationApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CatalogRespVO createCatalog(CatalogSaveReqVO createReqVO) {

        LocalDateTime nowLocalDateTime = LocalDateTime.now();

        // 校验入参并返回入参
        Integer version = this.checkCreateCatalogAndReturnVersion(createReqVO, nowLocalDateTime);

        // 解析Excel
        CatalogRespVO catalogRespVO = regulatoryCatalogDetailService.analysisCatalogExcel(CatalogSaveReqVO.builder().uploadUrl(createReqVO.getUploadUrl()).build());

        if (CollUtil.isEmpty(catalogRespVO.getRegulatoryCatalogDetailList())) {
            return catalogRespVO;
        }

        Long catalogDOId;

        // 升级版本
        CatalogDO catalogDO;
        if (createReqVO.getId() != null && createReqVO.getId() > 0) {

            catalogDO = catalogMapper.selectById(createReqVO.getId());
            catalogDO.setVersionCode(nowLocalDateTime.format(DateTimeFormatter.ofPattern("yyMMddHH")));
            catalogDO.setUploadUrl(createReqVO.getUploadUrl());
            catalogDO.setRemark(createReqVO.getRemark());
            catalogDO.setDisable(createReqVO.getDisable());
            catalogDO.setEnv(createReqVO.getEnv());

            // 新增目录
        } else {
            catalogDO = BeanUtils.toBean(createReqVO, CatalogDO.class);
            catalogDO.setType(OrganTypeEnum.INTERNET_SUPERVISION.getCode());
            catalogDO.setProjectCodeType(ProductProjectCodeTypeEnum.CENTER_STANDARD_WAREHOUSE.getCode());
            catalogDO.setVersionCode(nowLocalDateTime.format(DateTimeFormatter.ofPattern("yyMMddHH")));
            String prefPrefix = PrefUtil.INTERNET_REGULATION_PREF + nowLocalDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            CatalogDO maxCatalogDO = catalogMapper.selectMaxByPrefLikeRight(prefPrefix);

            catalogDO.setPref(maxCatalogDO != null
                ? PrefUtil.INTERNET_REGULATION_PREF + (Convert.toLong(maxCatalogDO.getPref().replace(PrefUtil.INTERNET_REGULATION_PREF, "")) + 1)
                : prefPrefix + "0001");

        }

        catalogDO.setId(null);
        catalogDO.setVersion(version);
        catalogDO.setTotalCount(catalogRespVO.getRegulatoryCatalogDetailList().size());
        catalogDO.setMatchedCount(catalogRespVO.getRegulatoryCatalogDetailList().size());
        catalogDO.setUnmatchedCount(0);
        catalogDO.setCreator(null);
        catalogDO.setCreateTime(null);
        catalogDO.setUpdater(null);
        catalogDO.setUpdateTime(null);

        // 插入版本
        catalogMapper.insert(catalogDO);
        catalogDOId = catalogDO.getId();

        // 插入子表
        for (RegulatoryCatalogDetailDO item : catalogRespVO.getRegulatoryCatalogDetailList()) {
            item.setCatalogId(catalogDOId);
        }

        List<List<RegulatoryCatalogDetailDO>> partition = Lists.partition(catalogRespVO.getRegulatoryCatalogDetailList(), 500);
        for (List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOS : partition) {
            regulatoryCatalogDetailMapper.batchInsertByXml(regulatoryCatalogDetailDOS);
        }

        // 不需要展示给前端
        catalogRespVO.setRegulatoryCatalogDetailList(null);

        // 返回
        return catalogRespVO;
    }

    /**
     * 校验入参
     *
     * @param createReqVO
     */
    private Integer checkCreateCatalogAndReturnVersion(CatalogSaveReqVO createReqVO, LocalDateTime nowLocalDateTime) {

        if (StringUtils.isEmpty(createReqVO.getUploadUrl())) {
            throw exception(CATALOG_UPLOAD_URL_IS_EMPTY);
        }

        // 升级版本
        if (createReqVO.getId() != null && createReqVO.getId() > 0) {

            CatalogDO catalogDO = catalogMapper.selectById(createReqVO.getId());

            if (catalogDO == null) {
                throw exception(UPGRADE_CATALOG_NOT_EXISTS);
            }

            CatalogDO maxVersionCatalogDO = catalogMapper.selectMaxVersionByPref(catalogDO.getPref());

            if (maxVersionCatalogDO == null) {
                throw exception(UPGRADE_CATALOG_NOT_EXISTS);
            }

            String format = nowLocalDateTime.format(DateTimeFormatter.ofPattern("yyMMddHH"));

            if (format.equals(maxVersionCatalogDO.getVersionCode())) {
                throw exception(UPGRADE_CATALOG_VERSION_CODE_NOT_EXISTS);
            }

            return maxVersionCatalogDO.getVersion() + 1;

        }

        // 新增目录
        List<CatalogDO> catalogDOS = catalogMapper.selectList(new LambdaQueryWrapperX<CatalogDO>()
            .eq(CatalogDO::getName, createReqVO.getName())
            .eq(CatalogDO::getType, createReqVO.getType())
            .eq(CatalogDO::getDeleted, false));

        if (CollUtil.isNotEmpty(catalogDOS)) {
            throw exception(CATALOG_NAME_IS_EXISTS);
        }

        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCatalog(CatalogSaveReqVO updateReqVO) {

        CatalogDO catalogDO = catalogMapper.selectById(updateReqVO.getId());

        if (catalogDO == null) {
            throw exception(CATALOG_NOT_EXISTS);
        }

        if (CatalogEnvEnum.PROD.getCode().equals(catalogDO.getEnv()) &&
            (CatalogEnvEnum.GRAY.getCode().equals(updateReqVO.getEnv()) || CatalogEnvEnum.TEST.getCode().equals(updateReqVO.getEnv()))) {
            throw exception(CATALOG_ENV_NOT_UPDATE);
        }

        if (updateReqVO.getDisable()) {
            Map<Long, Long> tenantCountMap = tenantServicePackRelationApi.selectCountByCatalogIds(Lists.newArrayList(updateReqVO.getId()), null);
            if (tenantCountMap.getOrDefault(updateReqVO.getId(), 0L) > 0) {
                throw exception(CATALOG_RELATION_TENANT_COUNT_IS_NOT_NULL);
            }
        }

        // 更新
        CatalogDO updateObj = BeanUtils.toBean(updateReqVO, CatalogDO.class);
        CatalogDO.builder()
            .id(updateReqVO.getId())
            .remark(updateReqVO.getRemark())
            .disable(updateReqVO.getDisable())
            .env(updateReqVO.getEnv())
            .build();

        catalogMapper.updateById(updateObj);

        // 更新子表
        // updateRegulatoryCatalogDetailList(updateReqVO.getId(), updateReqVO.getRegulatoryCatalogDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCatalog(Long id) {

        CatalogDO catalogDO = catalogMapper.selectById(id);

        if (catalogDO == null) {
            throw exception(CATALOG_NOT_EXISTS);
        }

        if (CatalogEnvEnum.PROD.getCode().equals(catalogDO.getEnv())) {
            throw exception(CATALOG_ENV_NOT_UPDATE);
        }

        // 删除
        catalogMapper.deleteById(id);

        // 删除子表
        deleteRegulatoryCatalogDetailByCatalogId(id);
    }

    private void validateCatalogExists(Long id) {
        if (catalogMapper.selectById(id) == null) {
            throw exception(CATALOG_NOT_EXISTS);
        }
    }

    @Override
    public CatalogDO getCatalog(Long id) {
        return catalogMapper.selectById(id);
    }

    @Override
    public PageResult<CatalogRespVO> getCatalogPage(CatalogPageReqVO pageReqVO) {
        IPage<CatalogDO> iPage = catalogMapper.getCatalogPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);

        if (iPage == null || iPage.getTotal() == 0) {
            return PageResult.empty();
        }

        List<CatalogRespVO> catalogRespVOS = CatalogConvert.INSTANCE.convertDOList2VOList(iPage.getRecords());
        UserUtil.fillUserInfo(catalogRespVOS, adminUserApi::getUserNameMap);

        return new PageResult<>(catalogRespVOS, iPage.getTotal());
    }

    // ==================== 子表（监管目录明细） ====================

    private void createRegulatoryCatalogDetailList(Long catalogId, List<RegulatoryCatalogDetailDO> list) {
        list.forEach(o -> o.setCatalogId(catalogId));
        regulatoryCatalogDetailMapper.insertBatch(list);
    }

    private void updateRegulatoryCatalogDetailList(Long catalogId, List<RegulatoryCatalogDetailDO> list) {
        deleteRegulatoryCatalogDetailByCatalogId(catalogId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createRegulatoryCatalogDetailList(catalogId, list);
    }

    private void deleteRegulatoryCatalogDetailByCatalogId(Long catalogId) {
        regulatoryCatalogDetailMapper.deleteByCatalogId(catalogId);
    }

    @Override
    public PageResult<CatalogRespVO> getCatalogVersionPage(CatalogPageReqVO pageReqVO) {

        if (StringUtils.isNotBlank(pageReqVO.getCreator())) {
            List<AdminUserRespDTO> adminUserRespDTOList = adminUserApi.getUserListByLikeNickName(pageReqVO.getCreator());
            if (CollUtil.isEmpty(adminUserRespDTOList)) {
                return PageResult.empty();
            }
            pageReqVO.setCreatorList(adminUserRespDTOList.stream().map(item -> item.getId().toString()).toList());
        }

        PageResult<CatalogDO> pageResult = catalogMapper.selectPage(pageReqVO);

        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        PageResult<CatalogRespVO> result = BeanUtils.toBean(pageResult, CatalogRespVO.class);

        List<Long> catalogIdList = result.getList().stream().map(CatalogRespVO::getId).distinct().toList();

        Map<Long, Long> tenantCountMap = tenantServicePackRelationApi.selectCountByCatalogIds(catalogIdList, null);

        result.getList().forEach(item -> {
            item.setTenantCount(CollUtil.isNotEmpty(tenantCountMap) && tenantCountMap.containsKey(item.getId()) ? tenantCountMap.get(item.getId()) : 0);
        });

        // 填充用户信息
        UserUtil.fillUserInfo(result.getList(), adminUserApi::getUserNameMap);

        return result;
    }

    @Override
    public List<CatalogDO> getCatalog(List<Long> idList) {

        if (CollUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        return catalogMapper.selectList(new LambdaQueryWrapperX<CatalogDO>()
            .eq(CatalogDO::getDeleted, false)
            .in(CatalogDO::getId, idList));
    }

    @Override
    public PageResult<CatalogRelationTenantDto> pageRelationTenant(CatalogPageReqVO pageReqVO) {

        TenantServicePackRelationReqDto tenantServicePackRelationReqDto = TenantServicePackRelationReqDto.builder()
            .catalogId(pageReqVO.getId())
            .provinceCode(pageReqVO.getProvinceCode())
            .cityCode(pageReqVO.getCityCode())
            .areaCode(pageReqVO.getAreaCode())
            .tenantNameOrPref(pageReqVO.getTenantNameOrPref())
            .tenantContactMobile(pageReqVO.getTenantContactMobile())
            .build();

        tenantServicePackRelationReqDto.setPageNo(pageReqVO.getPageNo());
        tenantServicePackRelationReqDto.setPageSize(pageReqVO.getPageSize());

        Map<Long, CatalogDO> catalogDOMap = new HashMap<>();

        if (pageReqVO.getId() != null) {
            CatalogDO catalogDO = catalogMapper.selectById(pageReqVO.getId());

            if (catalogDO != null) {
                catalogDOMap.put(catalogDO.getId(), catalogDO);
            }
        }

        if (CollUtil.isNotEmpty(pageReqVO.getCatalogIdList())) {
            tenantServicePackRelationReqDto.setCatalogIdList(pageReqVO.getCatalogIdList());

        } else if (StringUtils.isNotEmpty(pageReqVO.getPref())) {
            List<CatalogDO> catalogDOS = catalogMapper.selectByPref(pageReqVO.getPref());

            if (CollUtil.isNotEmpty(catalogDOS)) {
                List<Long> allCatalogIdList = new ArrayList<>();
                for (CatalogDO catalogDO : catalogDOS) {
                    allCatalogIdList.add(catalogDO.getId());
                    catalogDOMap.put(catalogDO.getId(), catalogDO);
                }
                tenantServicePackRelationReqDto.setCatalogIdList(allCatalogIdList);
            }
        }

        PageResult<CatalogRelationTenantDto> pageResult = tenantServicePackRelationApi.getCatalogRelationTenantPage(tenantServicePackRelationReqDto);

        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        for (CatalogRelationTenantDto tenantDto : pageResult.getList()) {

            if (catalogDOMap.containsKey(tenantDto.getCatalogId())) {
                tenantDto.setCatalogName(catalogDOMap.get(tenantDto.getCatalogId()).getName());
                tenantDto.setCatalogVersionCode(catalogDOMap.get(tenantDto.getCatalogId()).getVersionCode());
            }
        }

        return pageResult;
    }
}