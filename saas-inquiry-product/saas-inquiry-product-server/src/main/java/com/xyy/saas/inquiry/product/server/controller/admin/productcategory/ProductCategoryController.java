package com.xyy.saas.inquiry.product.server.controller.admin.productcategory;

import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.service.productcategory.ProductCategoryService;

@Tag(name = "管理后台 - 商品六级分类")
@RestController
@RequestMapping("/product/category")
@Validated
public class ProductCategoryController {

    @Resource
    private ProductCategoryService productCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建商品六级分类")
    @PreAuthorize("@ss.hasPermission('saas:product:category:create')")
    public CommonResult<Long> createProductCategory(@Valid @RequestBody ProductCategorySaveReqVO createReqVO) {
        return success(productCategoryService.createProductCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品六级分类")
    @PreAuthorize("@ss.hasPermission('saas:product:category:update')")
    public CommonResult<Boolean> updateProductCategory(@Valid @RequestBody ProductCategorySaveReqVO updateReqVO) {
        productCategoryService.updateProductCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品六级分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:category:delete')")
    public CommonResult<Boolean> deleteProductCategory(@RequestParam("id") Long id) {
        productCategoryService.deleteProductCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品六级分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:category:query')")
    public CommonResult<ProductCategoryRespVO> getProductCategory(@RequestParam("id") Long id) {
        ProductCategoryDO productCategory = productCategoryService.getProductCategory(id);
        return success(BeanUtils.toBean(productCategory, ProductCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品六级分类列表")
    @PreAuthorize("@ss.hasPermission('saas:product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getProductCategoryList(@Valid ProductCategoryListReqVO listReqVO) {
        List<ProductCategoryDO> list = productCategoryService.getProductCategoryList(listReqVO);
        return success(BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品六级分类 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductCategoryExcel(@Valid ProductCategoryListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<ProductCategoryDO> list = productCategoryService.getProductCategoryList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "商品六级分类.xls", "数据", ProductCategoryRespVO.class,
                        BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }

    // 登陆后请求一次，然后前端缓存，序列化传输比较耗时
    @GetMapping("/tree")
    @Operation(summary = "获得商品分类树")
    @PreAuthorize("@ss.hasPermission('saas:product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getCategoryTree() {
        return success(productCategoryService.getProductCategoryTree());
    }

}