package com.xyy.saas.inquiry.product.server.service.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 监管目录 Service 接口
 *
 * <AUTHOR>
 */
public interface RegulatoryCatalogDetailService {

    /**
     * 获得监管目录明细列表
     *
     * @param catalogId 目录ID
     * @return 监管目录明细列表
     */
    List<RegulatoryCatalogDetailDO> getRegulatoryCatalogDetailListByCatalogId(Long catalogId);

    /**
     * 分页查询监管目录商品明细
     *
     * @param pageReqVO
     * @return
     */
    PageResult<RegulatoryCatalogDetailDO> getRegulatoryCatalogDetailPage(RegulatoryCatalogDetailPageReqVO pageReqVO);

    /**
     * 解析目录excel
     *
     * @param createReqVO
     * @return
     */
    CatalogRespVO analysisCatalogExcel(CatalogSaveReqVO createReqVO);

    /**
     * 修改状态
     *
     * @param pageReqVO
     * @return
     */
    Boolean updateStatus(@Valid RegulatoryCatalogDetailPageReqVO pageReqVO);

    /**
     * 导入目录excel
     *
     * @param pageReqVO
     * @return
     */
    CatalogRespVO importExcel(CatalogSaveReqVO pageReqVO);
}
