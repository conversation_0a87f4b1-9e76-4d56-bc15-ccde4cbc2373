package com.xyy.saas.inquiry.product.server.dal.mysql.transfer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Mapper
public interface ProductTransferRecordMapper extends BaseMapperX<ProductTransferRecordDO> {

    default PageResult<ProductTransferRecordDO> selectPage(ProductTransferRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductTransferRecordDO>()
            .eqIfPresent(ProductTransferRecordDO::getProductPref, reqVO.getProductPref())
            .eqIfPresent(ProductTransferRecordDO::getType, reqVO.getType())
            .eqIfPresent(ProductTransferRecordDO::getSourceTenantId, reqVO.getSourceTenantId())
            .eqIfPresent(ProductTransferRecordDO::getTargetTenantId, reqVO.getTargetTenantId())
            .eqIfPresent(ProductTransferRecordDO::getStatus, reqVO.getStatus())
            .eqIfPresent(ProductTransferRecordDO::getResult, reqVO.getResult())
            .eqIfPresent(ProductTransferRecordDO::getDisable, reqVO.getDisable())
            .eqIfPresent(ProductTransferRecordDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(ProductTransferRecordDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(ProductTransferRecordDO::getId));
    }

    /**
     * 根据商品pref禁用提报记录（保证只有一条提报记录）
     * @param recordDO
     * @return
     */
    default int disableByProductPref(ProductTransferRecordDO recordDO) {
        ProductTransferRecordDO setDisable = new ProductTransferRecordDO().setDisable(true);
        return update(setDisable, new LambdaQueryWrapperX<ProductTransferRecordDO>()
            .eq(ProductTransferRecordDO::getProductPref, recordDO.getProductPref())
            .eq(ProductTransferRecordDO::getType, recordDO.getType())
            .eq(ProductTransferRecordDO::getSourceTenantId, recordDO.getSourceTenantId())
            .eq(ProductTransferRecordDO::getDisable, false)
            .eqIfPresent(ProductTransferRecordDO::getTargetTenantId, recordDO.getTargetTenantId())
            .eqIfPresent(ProductTransferRecordDO::getStatus, recordDO.getStatus()));
    }

    /**
     * 根据id禁用提报记录（保证只有一条提报记录）
     * @param id
     * @return
     */
    default int disableById(Long id) {
        if (id == null) {
            return 0;
        }
        ProductTransferRecordDO setDisable = new ProductTransferRecordDO().setDisable(true);
        return update(setDisable, new LambdaQueryWrapperX<ProductTransferRecordDO>()
            .eq(ProductTransferRecordDO::getId, id));
    }

    List<ProductTransferRecordDO> listTransferRecord(@Param("type") int type, @Param("param") ProductPresentPageReqVO reqVO);

    IPage<ProductPresentRecordRespVO> getLatestPresentPage(Page<Object> objectPage, @Param("param") ProductPresentPageReqVO reqVO);

    List<Map<String, Object>> getLatestPresentGroupByStatus(@Param("param") ProductPresentPageReqVO reqVO);

}