package com.xyy.saas.inquiry.product.server.service.productcategory;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_EXITS_CHILDREN;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_NAME_DUPLICATE;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_ERROR;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_IS_CHILD;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_NOT_EXITS;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 商品六级分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCategoryServiceImpl implements ProductCategoryService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Resource
    @Lazy
    private ProductCategoryService selfProxy;

    @Override
    public void batchSaveOrUpdate(List<ProductCategoryDO> productCategories) {
        if (productCategories == null || productCategories.isEmpty()) {
            return;
        }
        // 获取所有dictId列表（去重）
        Map<Long, ProductCategoryDO> dictIdMap = productCategories.stream()
                .collect(Collectors.toMap(ProductCategoryDO::getDictId, Function.identity(), (a, b) -> b));

        // 查询所有已存在的分类
        List<ProductCategoryDO> existingCategories = productCategoryMapper.selectByDictIdList(new ArrayList<>(dictIdMap.keySet()));
        Map<Long, ProductCategoryDO> existingCategoryMap = existingCategories.stream()
                .collect(Collectors.toMap(ProductCategoryDO::getDictId, Function.identity(), (o1, o2) -> o1));

        // 分别收集需要新增和更新的记录
        List<ProductCategoryDO> toInsert = new ArrayList<>();
        List<ProductCategoryDO> toUpdate = new ArrayList<>();

        for (ProductCategoryDO category : dictIdMap.values()) {
            ProductCategoryDO existingCategory = existingCategoryMap.get(category.getDictId());
            if (existingCategory == null) {
                // 不存在则新增
                toInsert.add(category);
            } else if (category.checkIsChanged(existingCategory)) {
                // 存在则更新（判断是否有改动）
                category.setId(existingCategory.getId());
                toUpdate.add(category);
            }
        }

        // 批量新增和更新
        boolean success = false;
        if (!toInsert.isEmpty()) {
            success |= productCategoryMapper.insertBatch(toInsert) == Boolean.TRUE;
        }
        if (!toUpdate.isEmpty()) {
            success |= productCategoryMapper.updateBatch(toUpdate);
        }
        if (success) {
            selfProxy.clearProductCategoryTreeCache();
        }
    }

    @Override
    public Long createProductCategory(ProductCategorySaveReqVO createReqVO) {
        // 校验字典父id（中台）的有效性
        validateParentProductCategory(null, createReqVO.getParentDictId());
        // 校验分类名称的唯一性
        validateProductCategoryNameUnique(null, createReqVO.getParentDictId(), createReqVO.getName());

        // 插入
        ProductCategoryDO productCategory = BeanUtils.toBean(createReqVO, ProductCategoryDO.class);
        int cnt = productCategoryMapper.insert(productCategory);
        if (cnt > 0) {
            selfProxy.clearProductCategoryTreeCache();
        }
        // 返回
        return productCategory.getId();
    }

    @Override
    public int updateProductCategory(ProductCategorySaveReqVO updateReqVO) {
        // 校验存在
        ProductCategoryDO categoryDO = validateProductCategoryExists(updateReqVO.getId());
        // 校验字典父id（中台）的有效性
        validateParentProductCategory(categoryDO.getDictId(), updateReqVO.getParentDictId());
        // 校验分类名称的唯一性
        validateProductCategoryNameUnique(categoryDO.getDictId(), updateReqVO.getParentDictId(), updateReqVO.getName());

        // 更新
        ProductCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ProductCategoryDO.class);
        int cnt = productCategoryMapper.updateById(updateObj);
        if (cnt > 0) {
            selfProxy.clearProductCategoryTreeCache();
        }
        return cnt;
    }

    @Override
    public void deleteProductCategory(Long id) {
        // 校验存在
        ProductCategoryDO categoryDO = validateProductCategoryExists(id);
        // 校验是否有子商品六级分类
        if (productCategoryMapper.selectCountByParentDictId(categoryDO.getDictId()) > 0) {
            throw exception(PRODUCT_CATEGORY_EXITS_CHILDREN);
        }
        // 删除
        int cnt = productCategoryMapper.deleteById(id);
        if (cnt > 0) {
            selfProxy.clearProductCategoryTreeCache();
        }
    }

    private ProductCategoryDO validateProductCategoryExists(Long id) {
        return Optional.ofNullable(productCategoryMapper.selectById(id)).orElseThrow(() -> exception(PRODUCT_CATEGORY_NOT_EXISTS));
    }

    private void validateParentProductCategory(Long dictId, Long parentDictId) {
        if (parentDictId == null || ProductCategoryDO.PARENT_DICT_ID_ROOT.equals(parentDictId)) {
            return;
        }
        // 1. 不能设置自己为父商品六级分类
        if (Objects.equals(dictId, parentDictId)) {
            throw exception(PRODUCT_CATEGORY_PARENT_ERROR);
        }
        // 2. 父商品六级分类不存在
        ProductCategoryDO parentProductCategory = productCategoryMapper.selectByDictId(parentDictId);
        if (parentProductCategory == null) {
            throw exception(PRODUCT_CATEGORY_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父商品六级分类，如果父商品六级分类是自己的子商品六级分类，则报错，避免形成环路
        if (dictId == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentDictId = parentProductCategory.getParentDictId();
            if (Objects.equals(dictId, parentDictId)) {
                throw exception(PRODUCT_CATEGORY_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父商品六级分类
            if (parentDictId == null || ProductCategoryDO.PARENT_DICT_ID_ROOT.equals(parentDictId)) {
                break;
            }
            parentProductCategory = productCategoryMapper.selectByDictId(parentDictId);
            if (parentProductCategory == null) {
                break;
            }
        }
    }

    private void validateProductCategoryNameUnique(Long dictId, Long parentDictId, String name) {
        ProductCategoryDO productCategory = productCategoryMapper.selectByParentDictIdAndName(parentDictId, name);
        if (productCategory == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的商品六级分类
        if (dictId == null) {
            throw exception(PRODUCT_CATEGORY_NAME_DUPLICATE);
        }
        if (!Objects.equals(productCategory.getDictId(), dictId)) {
            throw exception(PRODUCT_CATEGORY_NAME_DUPLICATE);
        }
    }

    @Override
    public ProductCategoryDO getProductCategory(Long id) {
        return productCategoryMapper.selectById(id);
    }

    @Override
    public List<ProductCategoryDO> getProductCategoryList(ProductCategoryListReqVO listReqVO) {
        return productCategoryMapper.selectList(listReqVO);
    }


    @Override
    public void clearProductCategoryTreeCache() {
        // do nothing
        synchronized (ProductCategoryServiceImpl.class) {
            if (TREE_CACHE != null) {
                TREE_CACHE = null;
            }
        }
    }

    // 使用内存缓存（redis缓存反序列化耗时太长）
    private static volatile List<ProductCategoryRespVO> TREE_CACHE;

    /**
     * 获得商品六级分类树
     *
     * @return 商品六级分类树
     */
    @Override
    public List<ProductCategoryRespVO> getProductCategoryTree() {
        if (TREE_CACHE == null) {
            synchronized (ProductCategoryServiceImpl.class) {
                if (TREE_CACHE == null) {
                    // 1. 获取所有分类列表
                    List<ProductCategoryDO> list = productCategoryMapper.selectList(new ProductCategoryListReqVO());

                    TREE_CACHE = ProductCategoryRespVO.buildTree(BeanUtils.toBean(list, ProductCategoryRespVO.class));
                }
            }
        }
        return TREE_CACHE;
    }
}