package com.xyy.saas.inquiry.product.server.dal.dataobject.product;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibExtDto;
import com.xyy.saas.inquiry.product.server.annotation.DictFieldType;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureInfoVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureProResult;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import jakarta.annotation.Nonnull;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * 商品标准库 DO
 */
@TableName(value = "saas_product_stdlib", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductStdlibDO extends BaseDO implements Cloneable {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 中台标准库ID
     */
    private Long midStdlibId;

    /**
     * 中台标准库ID（重复）
     */
    private Long midStdlibIdBak;

    /**
     * 通用名
     */
    private String commonName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 要素字段hash值
     */
    private String keyPointHash;

    /**
     * 助记码
     */
    private String mnemonicCode;

    /**
     * 最小包装数量
     */
    private BigDecimal minPackageNum;

    /**
     * 商品大类
     */
    @DictFieldType(DictTypeConstants.PRODUCT_SPU_CATEGORY)
    private String spuCategory;

    /**
     * 单位
     */
    @DictFieldType(DictTypeConstants.PRODUCT_UNIT)
    private String unit;

    /**
     * 剂型
     */
    @DictFieldType(DictTypeConstants.PRODUCT_DOSAGE_FORM)
    private String dosageForm;

    /**
     * 所属范围
     */
    @DictFieldType(value = DictTypeConstants.PRODUCT_BUSINESS_SCOPE, multiValueGap = ",")
    private String businessScope;

    /**
     * 处方分类
     */
    @DictFieldType(DictTypeConstants.PRODUCT_PRES_CATEGORY)
    private String presCategory;

    /**
     * 储存条件
     */
    @DictFieldType(DictTypeConstants.PRODUCT_STORAGE_WAY)
    private String storageWay;

    /**
     * 产地
     */
    private String origin;

    /**
     * 生产企业社会信用代码
     */
    private String manufacturerUscc;

    /**
     * 有效期
     */
    private String productValidity;

    /**
     * 批准文号有效期
     */
    private LocalDate approvalValidityPeriod;

    /**
     * 上市许可持有人
     */
    private String marketingAuthorityHolder;

    /**
     * 药品标识码
     */
    private String drugIdentCode;

    /**
     * 用药方式
     */
    @DictFieldType(DictTypeConstants.DRUG_DIRECTIONS)
    private String usageMethod;
    /**
     * 用药频次
     */
    @DictFieldType(DictTypeConstants.DRUG_USE_FREQUENCY)
    private String usageFrequency;
    /**
     * 单次使用剂量
     */
    private String singleDosage;
    /**
     * 单次使用剂量单位
     */
    @DictFieldType(DictTypeConstants.DRUG_DOSE_UNIT)
    private String singleDosageUnit;

    /**
     * 商品六级分类
     */
    private String firstCategory;
    private String secondCategory;
    private String thirdCategory;
    private String fourthCategory;
    private String fiveCategory;
    private String sixCategory;

    /**
     * 扩展信息（比如分类信息）
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ProductStdlibExtDto ext;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 多属性标志
     */
    private Long multiFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 组装助记码、要素hash值等字段
     */
    public void calcMnemonicCodeAndKeyPointHash() {
        calcMnemonicCodeAndKeyPointHash(false);
    }
    public void calcMnemonicCodeAndKeyPointHash(boolean ignoreSixPoint) {
        // 忽略六要素相关字段
        if (ignoreSixPoint || isAllNullSixPoint()) {
            setNullSixPoint();
            return;
        }

        // null 转 空字符串
        this.commonName = StringUtils.defaultString(this.commonName);
        this.brandName = StringUtils.defaultString(this.brandName);
        this.barcode = StringUtils.defaultString(this.barcode);
        this.spec = StringUtils.defaultString(this.spec);
        this.approvalNumber = StringUtils.defaultString(this.approvalNumber);
        this.manufacturer = StringUtils.defaultString(this.manufacturer);
        if (StringUtils.isBlank(mnemonicCode)) {
            this.mnemonicCode = ProductUtil.getMnemonicCode(this.commonName, this.brandName);
        }
        this.keyPointHash = ProductUtil.calcKeyPointHash(this.commonName, this.brandName, this.barcode, this.spec, this.approvalNumber, this.manufacturer);
    }

    /**
     * 忽略六要素相关字段（设置为null）
     */
    public void setNullSixPoint() {
        // 保留原有六要素值（通用名+品牌名+条形码+规格+批准文号+生产厂家）
        this.commonName = null;
        this.brandName = null;
        this.barcode = null;
        this.spec = null;
        this.approvalNumber = null;
        this.manufacturer = null;
        this.keyPointHash = null;
    }
    public boolean isAllNullSixPoint() {
        // 判断是否有修改六要素信息（通用名+品牌名+条形码+规格+批准文号+生产厂家）
        return this.commonName == null
            && this.brandName == null
            && this.barcode == null
            && this.spec == null
            && this.approvalNumber == null
            && this.manufacturer == null
            // && this.keyPointHash == null
            ;
    }



    /**
     * 判断六要素相关字段是否一致
     * @param other           为null默认为不相等
     * @param ignoreNullValue 字段为null是否判定为相等
     * @return true-相等 false-不相等
     */
    public boolean isSixPointEquals(ProductStdlibDO other, boolean ignoreNullValue) {
        if (other == null) {
            return false;
        }
        // 比较六要素字段，如果other字段为null且nullIsTrue，则该字段视为无变更
        return ((ignoreNullValue && other.commonName == null) || Objects.equals(this.commonName, other.commonName)) &&
            ((ignoreNullValue && other.brandName == null) || Objects.equals(this.brandName, other.brandName)) &&
            ((ignoreNullValue && other.barcode == null) || Objects.equals(this.barcode, other.barcode)) &&
            ((ignoreNullValue && other.spec == null) || Objects.equals(this.spec, other.spec)) &&
            ((ignoreNullValue && other.approvalNumber == null) || Objects.equals(this.approvalNumber, other.approvalNumber)) &&
            ((ignoreNullValue && other.manufacturer == null) || Objects.equals(this.manufacturer, other.manufacturer));
    }

    /**
     * 判断2个对象的字段是否一致
     * @param other             为null默认为不相等
     * @param ignoreNullValue   是否忽略字段值为null
     * @param ignoreSixPoint    是否忽略比较六要素相关字段
     * @return true-有变更 false-无变更
     */
    public boolean isEquals(ProductStdlibDO other, boolean ignoreSixPoint, boolean ignoreNullValue) {
        if (other == null) {
            return false;
        }
        // 比较其他字段
        return (ignoreSixPoint || isSixPointEquals(other, ignoreNullValue)) &&
            ((ignoreNullValue && other.midStdlibId == null) || Objects.equals(this.midStdlibId, other.midStdlibId)) &&
            ((ignoreNullValue && other.mnemonicCode == null) || Objects.equals(this.mnemonicCode, other.mnemonicCode)) &&
            ((ignoreNullValue && other.minPackageNum == null) || Objects.equals(this.minPackageNum, other.minPackageNum)) &&
            // ((ignoreNullValue && other.coverImages == null) || Objects.equals(this.coverImages, other.coverImages)) &&
            // ((ignoreNullValue && other.outerPackageImages == null) || Objects.equals(this.outerPackageImages, other.outerPackageImages)) &&
            // ((ignoreNullValue && other.instructionImages == null) || Objects.equals(this.instructionImages, other.instructionImages)) &&
            ((ignoreNullValue && other.unit == null) || Objects.equals(this.unit, other.unit)) &&
            ((ignoreNullValue && other.dosageForm == null) || Objects.equals(this.dosageForm, other.dosageForm)) &&
            ((ignoreNullValue && other.businessScope == null) || Objects.equals(this.businessScope, other.businessScope)) &&
            ((ignoreNullValue && other.presCategory == null) || Objects.equals(this.presCategory, other.presCategory)) &&
            ((ignoreNullValue && other.storageWay == null) || Objects.equals(this.storageWay, other.storageWay)) &&
            ((ignoreNullValue && other.origin == null) || Objects.equals(this.origin, other.origin)) &&
            ((ignoreNullValue && other.manufacturerUscc == null) || Objects.equals(this.manufacturerUscc, other.manufacturerUscc)) &&
            ((ignoreNullValue && other.productValidity == null) || Objects.equals(this.productValidity, other.productValidity)) &&
            ((ignoreNullValue && other.approvalValidityPeriod == null) || Objects.equals(this.approvalValidityPeriod, other.approvalValidityPeriod)) &&
            ((ignoreNullValue && other.marketingAuthorityHolder == null) || Objects.equals(this.marketingAuthorityHolder, other.marketingAuthorityHolder)) &&
            ((ignoreNullValue && other.drugIdentCode == null) || Objects.equals(this.drugIdentCode, other.drugIdentCode)) &&
            ((ignoreNullValue && other.usageMethod == null) || Objects.equals(this.usageMethod, other.usageMethod)) &&
            ((ignoreNullValue && other.usageFrequency == null) || Objects.equals(this.usageFrequency, other.usageFrequency)) &&
            ((ignoreNullValue && other.singleDosage == null) || Objects.equals(this.singleDosage, other.singleDosage)) &&
            ((ignoreNullValue && other.singleDosageUnit == null) || Objects.equals(this.singleDosageUnit, other.singleDosageUnit)) &&
            ((ignoreNullValue && other.firstCategory == null) || Objects.equals(this.firstCategory, other.firstCategory)) &&
            ((ignoreNullValue && other.secondCategory == null) || Objects.equals(this.secondCategory, other.secondCategory)) &&
            ((ignoreNullValue && other.thirdCategory == null) || Objects.equals(this.thirdCategory, other.thirdCategory)) &&
            ((ignoreNullValue && other.fourthCategory == null) || Objects.equals(this.fourthCategory, other.fourthCategory)) &&
            ((ignoreNullValue && other.fiveCategory == null) || Objects.equals(this.fiveCategory, other.fiveCategory)) &&
            ((ignoreNullValue && other.sixCategory == null) || Objects.equals(this.sixCategory, other.sixCategory)) &&
            ((ignoreNullValue && other.ext == null) || Objects.equals(this.ext, other.ext)) &&
            ((ignoreNullValue && other.status == null) || Objects.equals(this.status, other.status)) &&
            ((ignoreNullValue && other.multiFlag == null) || Objects.equals(this.multiFlag, other.multiFlag)) &&
            ((ignoreNullValue && other.remark == null) || Objects.equals(this.remark, other.remark));
    }

    /**
     * 组装分类列表
     */
    public List<ProductCategoryDO> assembleCategoryList() {
        // 获取扩展信息
        ProductStdlibExtDto ext = this.getExt();
        if (ext == null) {
            return List.of();
        }

        List<ProductCategoryDO> categories = new ArrayList<>();

        // 一级分类
        Long firstCategoryId = ext.getFirstCategoryId();
        String firstCategory = this.getFirstCategory();
        if (firstCategoryId != null && org.springframework.util.StringUtils.hasText(firstCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(firstCategoryId)
                .setName(firstCategory)
                .setParentDictId(0L); // 一级分类父ID为0
            categories.add(category);
        }

        // 二级分类
        Long secondCategoryId = ext.getSecondCategoryId();
        String secondCategory = this.getSecondCategory();
        if (secondCategoryId != null && org.springframework.util.StringUtils.hasText(secondCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(secondCategoryId)
                .setName(secondCategory)
                .setParentDictId(firstCategoryId);
            categories.add(category);
        }

        // 三级分类
        Long thirdCategoryId = ext.getThirdCategoryId();
        String thirdCategory = this.getThirdCategory();
        if (thirdCategoryId != null && org.springframework.util.StringUtils.hasText(thirdCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(thirdCategoryId)
                .setName(thirdCategory)
                .setParentDictId(secondCategoryId);
            categories.add(category);
        }

        // 四级分类
        Long fourthCategoryId = ext.getFourthCategoryId();
        String fourthCategory = this.getFourthCategory();
        if (fourthCategoryId != null && org.springframework.util.StringUtils.hasText(fourthCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(fourthCategoryId)
                .setName(fourthCategory)
                .setParentDictId(thirdCategoryId);
            categories.add(category);
        }

        // 五级分类
        Long fiveCategoryId = ext.getFiveCategoryId();
        String fiveCategory = this.getFiveCategory();
        if (fiveCategoryId != null && org.springframework.util.StringUtils.hasText(fiveCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(fiveCategoryId)
                .setName(fiveCategory)
                .setParentDictId(fourthCategoryId);
            categories.add(category);
        }

        // 六级分类
        Long sixCategoryId = ext.getSixCategoryId();
        String sixCategory = this.getSixCategory();
        if (sixCategoryId != null && org.springframework.util.StringUtils.hasText(sixCategory)) {
            ProductCategoryDO category = new ProductCategoryDO()
                .setDictId(sixCategoryId)
                .setName(sixCategory)
                .setParentDictId(fiveCategoryId);
            categories.add(category);
        }
        return categories;
    }

    public ProductStdlibDO assembleExt(ProductStdlibDO old) {
        this.ext = Optional.ofNullable(this.ext).orElseGet(ProductStdlibExtDto::new);
        if (old != null && old.ext != null) {
            if (this.ext.getCoverImages() == null) {
                this.ext.setCoverImages(old.ext.getCoverImages());
            }
            if (this.ext.getOuterPackageImages() == null) {
                this.ext.setOuterPackageImages(old.ext.getOuterPackageImages());
            }
            if (this.ext.getInstructionImages() == null) {
                this.ext.setInstructionImages(old.ext.getInstructionImages());
            }
        }
        return this;
    }

    /**
     * 组装商品图片
     */
    public ProductStdlibDO assembleImages(MidPictureProResult midPictureProResult, ProductStdlibDO old) {
        this.ext = Optional.ofNullable(this.ext).orElseGet(ProductStdlibExtDto::new);
        // ext 数据迁移
        if (old != null) {
            this.ext.migrate(old.ext);
        }

        // 区分是否为null 和 空集合（中台删除图片）
        if (midPictureProResult == null) {
            return this;
        }

        List<MidPictureInfoVo> picList = midPictureProResult.getPictureInfoList();
        // 1 ->主图
        List<String> coverImages = getImagesByPictureOrdinal(picList, i -> Objects.equals(1, i));
        // 2-5 ->外包装
        List<String> outerPackageImages = getImagesByPictureOrdinal(picList, i -> List.of(2, 3, 4, 5).contains(i));
        // 其他 ->说明书
        List<String> instructionImages = getImagesByPictureOrdinal(picList, i -> !List.of(1, 2, 3, 4, 5).contains(i));


        // 封面图必填
        if (CollectionUtils.isNotEmpty(coverImages)) {
            this.ext.setCoverImages(coverImages);
        }
        // 其他图片选填
        this.ext.setOuterPackageImages(outerPackageImages)
            .setInstructionImages(instructionImages);
        return this;
    }

    private List<String> getImagesByPictureOrdinal(List<MidPictureInfoVo> pictureInfoList, @Nonnull Predicate<Object> pictureOrdinalFilter) {
        return pictureInfoList.stream()
            .filter(i -> pictureOrdinalFilter.test(i.getPictureOrdinal()) && StringUtils.isNotEmpty(i.getPictureUrl()))
            .map(MidPictureInfoVo::getPictureUrl).toList();
    }


    @Override
    public ProductStdlibDO clone() {
        try {
            ProductStdlibDO clone = (ProductStdlibDO) super.clone();
            // 复制此处的可变状态，这样此克隆就不能更改初始克隆的内部项
            clone.ext = this.ext.clone();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError("Clone Not Supported: {}", e);
        }
    }
} 