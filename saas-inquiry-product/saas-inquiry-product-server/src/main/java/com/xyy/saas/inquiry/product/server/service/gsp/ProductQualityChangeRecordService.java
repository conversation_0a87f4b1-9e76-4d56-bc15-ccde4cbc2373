package com.xyy.saas.inquiry.product.server.service.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO;
import jakarta.validation.Valid;

/**
 * 质量变更申请操作记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductQualityChangeRecordService {

    /**
     * 保存或更新质量变更申请
     *
     * @param reqVO 请求参数
     * @return 记录ID
     */
    Long saveOrUpdateQualityChange(ProductQualityChangeRecordSaveReqVO reqVO);

    /**
     * 保存质量变更申请操作记录（商品修改）
     * @param dto
     * @param origin
     */
    void saveQualityChangeRecord(ProductInfoDto dto, ProductInfoDto origin);

    /**
     * 删除质量变更申请操作记录
     *
     * @param id 编号
     */
    void deleteProductQualityChangeRecord(Long id);

    /**
     * 获得质量变更申请操作记录
     *
     * @param id 编号
     * @return 质量变更申请操作记录
     */
    ProductQualityChangeRecordDO getProductQualityChangeRecord(Long id);

    /**
     * 获得质量变更申请操作记录分页
     *
     * @param pageReqVO 分页查询
     * @return 质量变更申请操作记录分页
     */
    PageResult<ProductQualityChangeRecordDO> getProductQualityChangeRecordPage(ProductQualityChangeRecordPageReqVO pageReqVO);

    // ==================== 子表（质量变更申请明细记录） ====================

    /**
     * 获得质量变更申请明细记录分页
     *
     * @param pageReqVO 分页查询
     * @param recordPref 单据编号
     * @return 质量变更申请明细记录分页
     */
    PageResult<ProductQualityChangeDetailDO> getProductQualityChangeDetailPage(PageParam pageReqVO, String recordPref);

    /**
     * 创建质量变更申请明细记录
     *
     * @param productQualityChangeDetail 创建信息
     * @return 编号
     */
    Long createProductQualityChangeDetail(@Valid ProductQualityChangeDetailDO productQualityChangeDetail);

    /**
     * 更新质量变更申请明细记录
     *
     * @param productQualityChangeDetail 更新信息
     */
    void updateProductQualityChangeDetail(@Valid ProductQualityChangeDetailDO productQualityChangeDetail);

    /**
     * 删除质量变更申请明细记录
     *
     * @param id 编号
     */
    void deleteProductQualityChangeDetail(Long id);

	/**
	 * 获得质量变更申请明细记录
	 *
	 * @param id 编号
     * @return 质量变更申请明细记录
	 */
    ProductQualityChangeDetailDO getProductQualityChangeDetail(Long id);

}