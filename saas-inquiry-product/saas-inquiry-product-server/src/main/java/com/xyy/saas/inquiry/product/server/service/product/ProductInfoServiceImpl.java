package com.xyy.saas.inquiry.product.server.service.product;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.*;
import static com.xyy.saas.inquiry.util.PrefUtil.NAME_SUFFIX_PRODUCT_APPEND_UNBUNDLED;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.annotation.MethodArgumentTrim;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto.ProductQualificationInfo;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductQualificationInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.mq.producer.mid.MidStdlibInteractiveProducer;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 商品基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ProductInfoServiceImpl implements ProductInfoService {

    @Resource
    private Validator validator;

    @Resource
    private ProductInfoMapper productInfoMapper;
    @Resource
    private ProductQualificationInfoMapper productQualificationInfoMapper;
    @Resource
    private ProductUseInfoMapper productUseInfoMapper;

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductQualityChangeRecordService qualityChangeRecordService;
    @Resource
    private ProductPriceAdjustmentRecordService priceAdjustmentRecordService;

    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;
    @Resource
    private ProductTransferRecordService transferRecordService;

    @Resource
    private MidStdlibInteractiveProducer midStdlibInteractiveProducer;

    @Resource
    private TenantApi tenantApi;

    @Resource
    @Lazy
    private ProductInfoService selfProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateProduct(@MethodArgumentTrim @Nonnull ProductInfoDto dto, ProductBizTypeEnum bizType) {
        // 1. 组装租户信息
        assembleTenantInfo(dto);
        // 如果bizType不传，根据参数判断业务类型
        bizType = ProductBizTypeEnum.checkIfNullBizType(dto, bizType);

        // 2. 组装商品资质+使用信息（需要在处理标准库之前查询数据，否则原数据可能会变更）
        ProductInfoDto origin = getProductInfo(dto.getId(), dto.getTenant());
        if (dto.getId() != null && origin == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }

        // 3. 处理商品状态和属性标志
        bizType.handleProductStatusOrFlag(dto);

        // 4. 处理商品标准库
        if (!bizType.ignoreHandleStdlib()) {
            stdlibService.saveOrUpdateStdlib(dto, true);
        }

        // 4.1.校验商品标准库id是否重复
        validateProductStdlibUnique(dto, bizType.throwExceptionIfStdlibUnique());
        // 如果标准库id重复，改为修改数据，则origin需要重新查询
        origin = getProductInfo(dto.getId(), dto.getTenant());

        // 5. 保存或更新商品信息
        boolean isCreate = dto.getId() == null;
        ProductInfoDO productInfo = saveOrUpdateProductInfoWithGsp(dto, bizType, origin);

        // 5.1.批量插入资质信息
        saveOrUpdateQualificationInfo(dto);
        // 5.2.批量插入使用信息
        saveOrUpdateUseInfo(dto);

        // 6.创建审批流
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByAuditingStatus(dto.getStatus());
        if (businessTypeEnum != null) {
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            if (loginUserId == null) {
                throw exception(USER_NOT_EXISTS);
            } else {
                bpmBusinessRelationService.createProcessInstance(loginUserId, new BpmBusinessRelationDto()
                    .setBusinessPref(productInfo.getPref())
                    .setBusinessType(businessTypeEnum.code)
                    .setTenantId(dto.getTenantId())
                    .setHeadTenantId(dto.getHeadTenantId())
                    .setApplicant("" + loginUserId)
                );
            }
        }

        // 7. 新建标品 - 直接调用 （不调用匹配接口，直接上报中台）
        if ((isCreate && bizType == ProductBizTypeEnum.MID_STDLIB_ADD)) {
            stdlibService.reportProduct2MidStdlib(dto);
        }

        return productInfo.getId();
    }

    /**
     * 组装租户信息
     * @param dto
     */
    private void assembleTenantInfo(ProductInfoDto dto) {
        if (dto.getTenantId() == null) {
            dto.setTenant(tenantApi.getTenant());
        } else {
            dto.setTenant(tenantApi.getTenant(dto.getTenantId()));
        }
        if (dto.getHeadTenantId() == null) {
            dto.setHeadTenantId(dto.getTenantId());
        }
    }

    /**
     * 保存或更新商品信息, 并且保存gsp信息（售价调整，质量变更）
     * @param dto
     * @return
     */
    private @NotNull ProductInfoDO saveOrUpdateProductInfoWithGsp(@Nonnull ProductInfoDto dto, @Nonnull ProductBizTypeEnum bizTypeEnum, @Nullable ProductInfoDto origin) {
        if (ObjectUtil.isNull(origin)) {
            ProductInfoDO productInfo = assembleCreateProductInfo(dto, bizTypeEnum);
            productInfoMapper.insert(productInfo);
            dto.setId(productInfo.getId())
                .setPref(productInfo.getPref())
                .setShowPref(productInfo.getShowPref())
                .setMnemonicCode(productInfo.getMnemonicCode());
            return productInfo;
        }

        dto.setPref(origin.getPref())
            .setShowPref(origin.getShowPref())
            .setMnemonicCode(origin.getMnemonicCode());
        // 修改售价（零售价 ｜ 会员价），需要增加售价调整单（不走审批流）
        // 修改其他字段，需要增加质量变更记录（不走审批流）
        qualityChangeRecordService.saveQualityChangeRecord(dto, origin);
        priceAdjustmentRecordService.savePriceAdjustmentRecord(dto, origin, List.of(dto.getTenantId()));

        // 更新商品信息（顺序不能变）
        ProductInfoDO productInfo = assembleUpdateProductInfo(dto, origin);
        productInfoMapper.updateById(productInfo);
        return productInfo;
    }

    /**
     * 组装创建商品模型
     * @param dto
     * @return
     */
    private ProductInfoDO assembleCreateProductInfo(@Nonnull ProductInfoDto dto, @Nonnull ProductBizTypeEnum bizTypeEnum) {
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);
        // 设置租户id
        productInfo.setTenantId(ObjectUtil.defaultIfNull(dto.getHeadTenantId(), dto.getTenantId()));

        // 根据id 判断是否存在
        if (StringUtils.isBlank(dto.getShowPref())) {
            // 商品编码如果为空，则系统自动生成，这里查询会会查出全部的数据（包含逻辑删除的）
            Supplier<List<String>> initAllPrefSp = () -> productInfoMapper.listShowPref(dto.getHeadTenantId());

            // 拆零商品外码：源商品外码+CL
            String showPref = bizTypeEnum == ProductBizTypeEnum.UNBUNDLED_PRODUCT
                ? PrefUtil.getUnbundledProductShortPref(dto.getHeadTenantId(), dto.getSourceProductShowPref(), initAllPrefSp)
                : PrefUtil.getProductShortPref(dto.getHeadTenantId(), initAllPrefSp);

            productInfo.setShowPref(showPref);
        }
        // 校验 租户 + 商品编号是否唯一
        validateProductShowPrefUnique(productInfo);

        productInfo.setPref(PrefUtil.getProductPref());
        // 商品助记码如果为空，则系统自动生成
        productInfo.calcMnemonicCode();

        // 多属性转换为数值
        long multiFlag = ProductFlag.toFlag(null, dto.getProductFlag());
        productInfo.setMultiFlag(multiFlag);

        return productInfo;
    }

    /**
     * 组装更新商品模型
     * @param dto
     * @param origin
     * @return
     */
    private ProductInfoDO assembleUpdateProductInfo(ProductInfoDto dto, ProductInfoDto origin) {
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);

        // 租户 + 商品编号 不允许变更
        productInfo.setTenantId(origin.getTenantId());
        productInfo.setShowPref(origin.getShowPref());

        // 多属性转换为数值: 非新增场景：需要合并原有多属性
        long multiFlag = ProductFlag.toFlag(origin.getMultiFlag(), dto.getProductFlag());

        productInfo.setMultiFlag(multiFlag & origin.getMultiFlag());

        return productInfo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductInfo(Long id) {
        // 校验存在
        ProductInfoDO exist = validateProductInfoExists(id);
        // 状态为使用中
        if (!Objects.equals(ProductStatusEnum.USING.code, exist.getStatus())) {
            throw exception(PRODUCT_INFO_CAN_NOT_DELETE, exist.getShowPref());
        }

        // 删除
        productInfoMapper.deleteById(id);

        // 删除资质信息
        productQualificationInfoMapper.deleteByProductPref(exist.getPref());
        // 删除使用信息
        productUseInfoMapper.deleteByProductPref(exist.getPref());
    }

    private ProductInfoDO validateProductInfoExists(Long id) {
        ProductInfoDO productInfoDO = productInfoMapper.selectById(id);
        if (productInfoDO == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }
        return productInfoDO;
    }

    private ProductInfoDO validateSourceProductExists(String sourcePref) {
        return Optional.ofNullable(productInfoMapper.selectOne(ProductInfoDO::getPref, sourcePref))
            .orElseThrow(() -> exception(PRODUCT_INFO_NOT_EXISTS));
    }

    private void validateProductShowPrefUnique(ProductInfoDO productInfo) {
        if (productInfo == null) {
            return;
        }
        // 机构+外码唯一性校验（忽略当前id）
        ProductInfoDO exists = productInfoMapper.uniqueIndexExists(productInfo.getTenantId(), productInfo.getShowPref(), null);
        if (exists != null && !Objects.equals(productInfo.getId(), exists.getId())) {
            throw exception(PRODUCT_INFO_DUPLICATE_SHOW_PREF, productInfo.getShowPref(), exists.tips4Deleted());
        }
    }

    private void validateProductStdlibUnique(ProductInfoDto dto, boolean exceptionIfExists) {
        // 拆零品 stdlib id为空，则不校验
        if (dto == null || dto.getStdlibId() == null) {
            return;
        }
        // 机构+标准库id唯一性校验（忽略当前id）
        ProductInfoDO exists = productInfoMapper.uniqueIndexExists(dto.getHeadTenantId(), null, dto.getStdlibId());
        if (exists == null || Objects.equals(dto.getId(), exists.getId())) {
            return;
        }
        // 存在则抛异常
        if (exceptionIfExists) {
            throw exception(PRODUCT_INFO_DUPLICATE_STDLIB, exists.getShowPref(), exists.tips4Deleted());
        }
        // 否则由新增变成修改
        dto.setId(exists.getId());
    }

    @Override
    public ProductInfoDto getProductInfo(Long id) {
        return getProductInfo(id, null);
    }

    private ProductInfoDto getProductInfo(Long id, TenantDto tenant) {
        if (id == null) {
            return null;
        }
        tenant = tenant == null ? tenantApi.getTenant() : tenant;
        List<ProductInfoDO2> productInfoDO2List = productInfoMapper.listByIdOrPref(List.of(id), null, tenant, false);
        if (CollectionUtils.isEmpty(productInfoDO2List)) {
            return null;
        }
        List<ProductInfoDto> dtoList = convertListDoToDto(tenant.getId(), tenant.getHeadTenantId(), productInfoDO2List);
        return dtoList.getFirst();
    }

    @Override
    public List<ProductInfoDto> listProductInfoByPref(List<String> prefList) {
        if (CollectionUtils.isEmpty(prefList)) {
            return List.of();
        }
        Page<ProductInfoDO> page = new Page<>(1, prefList.size());
        ProductInfoPageReqVO pageReqVO = new ProductInfoPageReqVO().setPrefList(prefList);
        // 查询分页
        Page<ProductInfoDO2> selectedPage = productInfoMapper.selectPageByParam(page, pageReqVO);

        List<ProductInfoDto> result = new ArrayList<>();
        // 按照 tenantId 分组，然后组装数据
        selectedPage.getRecords().stream().collect(Collectors.groupingBy(ProductInfoDO2::getTenantId)).forEach((tenantId, list) -> {
            // 转换分页数据
            List<ProductInfoDto> dtoList = convertListDoToDto(tenantId, tenantId, list);
            result.addAll(dtoList);
        });

        return result;
    }

    @Override
    public PageResult<ProductInfoDto> getProductInfoPage(ProductInfoPageReqVO pageReqVO) {
        if (pageReqVO.getTenantId() == null) {
            TenantDto tenant = tenantApi.getTenant();
            pageReqVO.setTenantId(tenant.getId());
            pageReqVO.setHeadTenantId(tenant.getHeadTenantId());
        }
        if (pageReqVO.getHeadTenantId() == null) {
            pageReqVO.setHeadTenantId(pageReqVO.getTenantId());
        }
        Page<ProductInfoDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 查询分页
        Page<ProductInfoDO2> selectedPage = productInfoMapper.selectPageByParam(page, pageReqVO);
        // 转换分页数据
        List<ProductInfoDto> dtoList = convertListDoToDto(pageReqVO.getTenantId(), pageReqVO.getHeadTenantId(), selectedPage.getRecords());

        return new PageResult<>(dtoList, selectedPage.getTotal());
    }

    private @Nonnull List<ProductInfoDto> convertListDoToDto(Long tenantId, Long headTenantId, List<ProductInfoDO2> records) {
        List<String> prefList = records.stream().map(ProductInfoDO2::getPref).toList();

        // 如果有总部租户，则查询总部的商品使用信息，上面分页查询的是门店使用信息
        if (!Objects.equals(headTenantId, tenantId)) {
            Map<String, ProductUseInfoDO> headUseInfoMap = productUseInfoMapper.selectMapList(prefList, headTenantId, headTenantId);
            // 组装总部使用信息
            records.forEach(i -> {
                // 门店有使用信息， 不覆盖
                if (i.getUseInfo() != null && i.getUseInfo().getProductPref() != null) {
                    return;
                }
                ProductUseInfoDO headUseInfo = headUseInfoMap.get(i.getPref());
                if (headUseInfo != null) {
                    i.setUseInfo(headUseInfo);
                }
            });
        }

        // 查询资质信息
        Map<String, List<ProductQualificationInfo>> qualificationGroupByProductPref = productQualificationInfoMapper.selectList(prefList)
            .stream().map(i -> BeanUtil.toBean(i, ProductQualificationInfo.class))
            .collect(Collectors.groupingBy(ProductQualificationInfo::getProductPref));

        return records.stream().map(i -> {
            // 组装商品信息
            ProductInfoDto dto;
            if (i.getStdlib() == null) {
                dto = BeanUtil.toBean(i, ProductInfoDto.class);
            } else {
                // 组装标准库信息
                dto = BeanUtil.toBean(i.getStdlib(), ProductInfoDto.class);
                BeanUtils.copyProperties(i, dto);
            }

            // 多属性标志转换
            ProductFlag productFlag = ProductFlag.of(i.getMultiFlag());
            // 标准库-多属性标志转换（合并）
            productFlag = ProductFlag.ofStdlib(productFlag, Optional.ofNullable(i.getStdlib()).map(ProductStdlibDO::getMultiFlag).orElse(null));
            dto.setProductFlag(productFlag);

            // 组装使用信息
            dto.setUseInfo(BeanUtil.toBean(i.getUseInfo(), ProductUseInfoDto.class));
            // 组装资质信息
            List<ProductQualificationInfo> infoList = qualificationGroupByProductPref.get(i.getPref());
            dto.setQualificationInfo(ProductQualificationInfoDto.of(infoList));
            return dto;
        }).toList();
    }

    /**
     * 保存或更新资质信息
     * @param dto
     */
    private void saveOrUpdateQualificationInfo(ProductInfoDto dto) {
        if (dto == null || dto.getId() == null || dto.getQualificationInfo() == null) {
            return;
        }
        // 批量插入资质信息
        ProductQualificationInfoDto qualificationInfo = dto.getQualificationInfo();
        qualificationInfo.setProductPref(dto.getPref());
        List<ProductQualificationInfoDO> qualificationInfoDOList = BeanUtil.copyToList(qualificationInfo.toInfoList(), ProductQualificationInfoDO.class);
        if (qualificationInfoDOList.isEmpty()) {
            return;
        }
        productQualificationInfoMapper.insertOrUpdateOnDuplicate(qualificationInfoDOList);
    }

    /**
     * 保存或更新使用信息
     * @param dto
     */
    private void saveOrUpdateUseInfo(ProductInfoDto dto) {
        if (dto == null || dto.getId() == null || dto.getUseInfo() == null) {
            return;
        }
        // 批量插入使用信息
        ProductUseInfoDto useInfo = dto.getUseInfo();
        useInfo.setProductPref(dto.getPref());
        useInfo.setTenantId(dto.getTenantId());
        useInfo.setHeadTenantId(dto.getHeadTenantId());

        // 查询修改前的数据
        ProductUseInfoDO before = productUseInfoMapper.selectOne(new LambdaQueryWrapperX<ProductUseInfoDO>()
            .eq(ProductUseInfoDO::getProductPref, dto.getPref())
            .eq(ProductUseInfoDO::getTenantId, dto.getTenantId())
        );

        ProductUseInfoDO useInfoDO = BeanUtils.toBean(useInfo, ProductUseInfoDO.class);
        // 记录上一次数据（可恢复）
        useInfoDO.setRedoData(useInfoDO.compareBeforeToRedo(before));
        if (before != null) {
            useInfoDO.setId(before.getId());
        }

        productUseInfoMapper.insertOrUpdate(useInfoDO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveOrUpdateUnbundledProduct(ProductUnbundledBatchSaveReqVO batchSaveReqVO) {
        if (batchSaveReqVO == null || CollectionUtils.isEmpty(batchSaveReqVO.getProducts())) {
            return List.of();
        }
        return batchSaveReqVO.getProducts().stream().map(createReqVO -> {
            boolean isCreate = createReqVO.getId() == null;
            ValidationUtils.validate(validator, createReqVO, isCreate ? Add.class : Update.class);

            ProductInfoDto dto = BeanUtils.toBean(createReqVO, ProductInfoDto.class);;
            if (isCreate) {
                String sourceProductPref = createReqVO.getSourceProductPref();
                ProductInfoDO exists = validateSourceProductExists(sourceProductPref);

                dto = BeanUtils.toBean(exists, ProductInfoDto.class);
                dto.setId(null)
                    .setPref(null)
                    .setShowPref(null)
                    .setCommonName(exists.getCommonName() + NAME_SUFFIX_PRODUCT_APPEND_UNBUNDLED)
                    .setSourceProductPref(sourceProductPref)
                    .setSourceProductShowPref(exists.getShowPref());
            }

            // 拆零商品不关联标准库信息，只关联源商品信息
            dto.setStdlibId(null)
                .setMidStdlibId(null)
                .setUnbundledQuantity(createReqVO.getUnbundledQuantity())
                .setSpec(createReqVO.getUnbundledSpec())
                .setUnit(createReqVO.getUnbundledUnit())
                .setRemark(createReqVO.getRemark());

            // 插入拆零商品信息
            return saveOrUpdateProduct(dto, ProductBizTypeEnum.UNBUNDLED_PRODUCT);
        }).toList();
    }


    // region 保存商品提报
    /**
     * 保存商品提报
     *
     * @param reqVO
     * @return  返回门店商品id
     */
    @Override
    public Long saveProductPresent(ProductPresentSaveReqVO reqVO) {
        // 1. 转换请求参数
        ProductInfoDto dto = BeanUtils.toBean(reqVO, ProductInfoDto.class);
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        dto.setTenantId(tenantId);

        Long transferRecordId = reqVO.getId();
        // 2. 如果是编辑,检查审批状态
        if (transferRecordId != null) {
            ProductTransferRecordDO transferRecord = transferRecordService.getProductTransferRecord(transferRecordId);
            if (transferRecord == null || !Objects.equals(ProductTransferStatusEnum.MID_AUDIT_REJECT.code, transferRecord.getStatus())) {
                throw exception(PRODUCT_PRESENT_CANNOT_EDIT);
            }
            ProductInfoDO productInfo = productInfoMapper.selectOne(ProductInfoDO::getPref, transferRecord.getProductPref());
            // 审批驳回才能编辑
            if (productInfo == null || !Objects.equals(ProductStatusEnum.MID_AUDIT_REJECT.code, productInfo.getStatus())) {
                throw exception(PRODUCT_PRESENT_CANNOT_EDIT);
            }
            // 商品信息组装（这里不能覆盖租户id，否则提报记录可能会变成连锁总部的id）
            dto.setId(productInfo.getId());
        }

        // 3. 查询相同 条形码 的标准库商品（使用中）
        // 条形码传0，不根据条形码查询
        String barcode = reqVO.getBarcode();
        List<ProductStdlibDto> stdlibDtoList = stdlibService.searchStdlibByBarcode(barcode);
        if (CollectionUtils.isNotEmpty(stdlibDtoList)) {
            throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
        }

        // 4. 查询当前门店相同 条形码/六要素 的提报记录（待审批）
        ProductPresentPageReqVO queryReqVO = new ProductPresentPageReqVO()
            .setSourceTenantId(dto.getTenantId())
            .setBarcode(StringUtils.defaultString(barcode));
        if (barcode == null || barcode.equals("0")) {
            queryReqVO.setCommonName(StringUtils.defaultString(reqVO.getCommonName()))
                .setBrandName(StringUtils.defaultString(reqVO.getBrandName()))
                .setSpec(StringUtils.defaultString(reqVO.getSpec()))
                .setManufacturer(StringUtils.defaultString(reqVO.getManufacturer()))
                .setApprovalNumber(StringUtils.defaultString(reqVO.getApprovalNumber()));
        }
        List<ProductTransferRecordDO> transferRecordDOS = transferRecordService.listPresentTransferRecord(queryReqVO);
        if (CollectionUtils.isNotEmpty(transferRecordDOS)) {
            boolean existAuditing = transferRecordDOS.stream().anyMatch(i -> Objects.equals(i.getStatus(), ProductTransferStatusEnum.INIT.code));
            if (existAuditing) {
                throw exception(PRODUCT_PRESENT_DUPLICATED_AUDITING);
            }
            // boolean existAudited = transferRecordDOS.stream().anyMatch(i -> Objects.equals(i.getStatus(), ProductTransferStatusEnum.SUCCESS.code));
            // if (existAudited) {
            //     throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
            // }
        }

        // 5. 查询相同 六要素 的标准库商品（使用中）
        // 排除当前编辑记录，且中台审核驳回后还可以重新提报（提报中台会返回已驳回原因）
        ProductStdlibDO stdlibDO = stdlibService.uniqueQuery(dto);
        if (transferRecordId == null && stdlibDO != null && Objects.equals(stdlibDO.getStatus(), ProductStdlibStatusEnum.USING.code)) {
            throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
        }

        // 6. 保存商品信息（包含上报等操作）
        selfProxy.saveOrUpdateProduct(dto, ProductBizTypeEnum.INQUIRY_PRESENT);

        // 7. 提报商品中台
        ProductTransferRecordDO transferRecordDO = stdlibService.reportProduct2MidStdlib(dto);

        // 8. 重新编辑后新增提报记录id，原提报记录禁用
        if (transferRecordId != null && !Objects.equals(transferRecordId, transferRecordDO.getId())) {
            // 禁用原提报记录
            transferRecordService.disableProductTransferRecord(transferRecordId);
        }
        return transferRecordDO.getId();

    }

    // endregion
}