package com.xyy.saas.inquiry.product.server.controller.admin.bpm;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.convert.task.BpmProcessInstanceConvert;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmCategoryDO;
import cn.iocoder.yudao.module.bpm.service.definition.BpmCategoryService;
import cn.iocoder.yudao.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO2;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 审批流关联业务")
@RestController
@RequestMapping("/bpm/business-relation")
@Validated
public class BpmBusinessRelationController {

    @Resource
    private HistoryService historyService;

    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private BpmTaskService taskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private BpmCategoryService categoryService;

    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;

    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;


    // @PostMapping("/create")
    // @Operation(summary = "创建审批流关联业务")
    // @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:create')")
    // public CommonResult<Long> createBpmBusinessRelation(@Valid @RequestBody BpmBusinessRelationSaveReqVO createReqVO) {
    //     return success(bpmBusinessRelationService.createBpmBusinessRelation(createReqVO));
    // }
    //
    // @PutMapping("/update")
    // @Operation(summary = "更新审批流关联业务")
    // @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:update')")
    // public CommonResult<Boolean> updateBpmBusinessRelation(@Valid @RequestBody BpmBusinessRelationSaveReqVO updateReqVO) {
    //     bpmBusinessRelationService.updateBpmBusinessRelation(updateReqVO);
    //     return success(true);
    // }
    //
    // @DeleteMapping("/delete")
    // @Operation(summary = "删除审批流关联业务")
    // @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:delete')")
    // public CommonResult<Boolean> deleteBpmBusinessRelation(@RequestParam("id") Long id) {
    //     bpmBusinessRelationService.deleteBpmBusinessRelation(id);
    //     return success(true);
    // }

    @GetMapping("/get")
    @Operation(summary = "获得审批流关联业务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:query')")
    public CommonResult<BpmBusinessRelationRespVO> getBpmBusinessRelation(@RequestParam("id") Long id) {
        BpmBusinessRelationDO bpmBusinessRelation = bpmBusinessRelationService.getBpmBusinessRelation(id);
        return success(BeanUtils.toBean(bpmBusinessRelation, BpmBusinessRelationRespVO.class));
    }

    /**
     * 获得审批流关联业务分页
     *
     * @param pageReqVO 分页查询
     * @return 审批流关联业务分页
     */
    @GetMapping("/page")
    @Operation(summary = "获得审批流关联业务分页")
    @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:query')")
    public CommonResult<PageResult<BpmBusinessRelationRespVO2>> getBpmBusinessRelationPage(@Valid BpmBusinessRelationPageReqVO pageReqVO) {
        // 获得分页
        PageResult<BpmBusinessRelationDO> pageResult = bpmBusinessRelationService.getBpmBusinessRelationPage(pageReqVO);
        // return success(BeanUtils.toBean(pageResult, BpmBusinessRelationRespVO.class));

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty(pageResult.getTotal()));
        }

        // 查询历史流程实例（状态在流程变量中，需要带includeProcessVariables的查询）
        Set<String> processInstanceIdList = convertSet(pageResult.getList(), BpmBusinessRelationDO::getProcessInstanceId);
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
            .processInstanceIds(processInstanceIdList).includeProcessVariables().list();
        // List<HistoricProcessInstance> historicProcessInstances = processInstanceService.getHistoricProcessInstances(processInstanceIdList);

        // 拼接返回
        Map<String, List<Task>> taskMap = taskService.getTaskMapByProcessInstanceIds(List.copyOf(processInstanceIdList));
        Map<String, ProcessDefinition> processDefinitionMap = processDefinitionService.getProcessDefinitionMap(
            convertSet(historicProcessInstances, HistoricProcessInstance::getProcessDefinitionId));
        Map<String, BpmCategoryDO> categoryMap = categoryService.getCategoryMap(
            convertSet(processDefinitionMap.values(), ProcessDefinition::getCategory));
        // 发起人信息
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
            convertSet(historicProcessInstances, processInstance -> NumberUtils.parseLong(processInstance.getStartUserId())));
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(
            convertSet(userMap.values(), AdminUserRespDTO::getDeptId));

        PageResult<BpmProcessInstanceRespVO> bpmPageResult = BpmProcessInstanceConvert.INSTANCE.buildProcessInstancePage(new PageResult<>(historicProcessInstances, pageResult.getTotal()),
            processDefinitionMap, categoryMap, taskMap, userMap, deptMap);

        // 组装 业务数据 businessRelation
        Map<String, BpmBusinessRelationDO> businessRelationDOMap = pageResult.getList().stream().collect(Collectors.toMap(BpmBusinessRelationDO::getProcessInstanceId, Function.identity(), (old, curr) -> curr));

        List<BpmBusinessRelationRespVO2> bpmBusinessRelationRespVO2List = bpmPageResult.getList().stream().map(bpmProcessInstanceRespVO -> {
            BpmBusinessRelationDO bpmBusinessRelationDO = businessRelationDOMap.get(bpmProcessInstanceRespVO.getId());
            return BeanUtils.toBean(bpmProcessInstanceRespVO, BpmBusinessRelationRespVO2.class)
                .setBusinessRelation(BeanUtils.toBean(bpmBusinessRelationDO, BpmBusinessRelationRespVO.class));
        }).collect(Collectors.toList());

        return success(new PageResult<>(bpmBusinessRelationRespVO2List, bpmPageResult.getTotal()));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出审批流关联业务 Excel")
    @PreAuthorize("@ss.hasPermission('saas:bpm:business-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBpmBusinessRelationExcel(@Valid BpmBusinessRelationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BpmBusinessRelationDO> list = bpmBusinessRelationService.getBpmBusinessRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "审批流关联业务.xls", "数据", BpmBusinessRelationRespVO.class,
                        BeanUtils.toBean(list, BpmBusinessRelationRespVO.class));
    }

}