package com.xyy.saas.inquiry.product.server.service.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品基本信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductInfoService {

    /**
     * 保存（新增 or 修改）商品基本信息
     *
     * @param dto           创建信息
     * @param bizType       业务类型
     * @return 编号
     */
    Long saveOrUpdateProduct(ProductInfoDto dto, ProductBizTypeEnum bizType);

    /**
     * 删除商品基本信息
     *
     * @param id 编号
     */
    void deleteProductInfo(Long id);

    /**
     * 获得商品基本信息（带标准库信息，使用信息，资质信息）
     *
     * @param id 编号
     * @return 商品基本信息
     */
    ProductInfoDto getProductInfo(Long id);

    /**
     * 获得商品基本信息
     *
     * @param prefList 编号
     * @return 商品基本信息
     */
    List<ProductInfoDto> listProductInfoByPref(List<String> prefList);

    /**
     * 获得商品基本信息分页
     *
     * @param pageReqVO 分页查询
     * @return 商品基本信息分页
     */
    PageResult<ProductInfoDto> getProductInfoPage(ProductInfoPageReqVO pageReqVO);


    /**
     * 创建拆零商品
     *
     * @param batchSaveReqVO 创建信息
     * @return 编号
     */
    List<Long> saveOrUpdateUnbundledProduct(@Valid ProductUnbundledBatchSaveReqVO batchSaveReqVO);


    /**
     * 保存商品提报
     *
     * @param reqVO
     * @return  返回门店商品id
     */
    Long saveProductPresent(ProductPresentSaveReqVO reqVO);

}