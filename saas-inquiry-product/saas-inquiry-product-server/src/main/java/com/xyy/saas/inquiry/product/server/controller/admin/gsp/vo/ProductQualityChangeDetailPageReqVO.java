package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 质量变更申请明细记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductQualityChangeDetailPageReqVO extends PageParam {

    @Schema(description = "单据编号", example = "32264")
    private String recordPref;

    @Schema(description = "变更类型", example = "1")
    private Integer type;

    @Schema(description = "商品/供应商编码", example = "6253")
    private String itemPref;

    @Schema(description = "外码")
    private String itemCode;

    @Schema(description = "名称", example = "王五")
    private String itemName;

    @Schema(description = "变更内容（JSON格式）")
    private String content;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}