package com.xyy.saas.inquiry.product.server.config.mq;

import com.xyy.saas.inquiry.product.server.config.mq.annotation.ExternalMQConsumer;
import com.xyy.saas.inquiry.product.server.config.mq.enums.ExternalMQClusterEnum;
import com.xyy.saas.inquiry.product.server.config.mq.handler.ExternalMessageHandler;
import com.xyy.saas.inquiry.product.server.config.mq.properties.MultiExternalMQProperties;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class MultiExternalMQConsumerAutoConfiguration implements SmartInitializingSingleton {

    @Resource
    private MultiExternalMQProperties externalMQProperties;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void afterSingletonsInstantiated() {
        // 扫描并处理 ExternalMQConsumer 注解
        String[] consumerBeanNames = applicationContext.getBeanNamesForAnnotation(ExternalMQConsumer.class);
        for (String beanName : consumerBeanNames) {
            Object bean = applicationContext.getBean(beanName);
            Object originBean = AopUtils.isAopProxy(bean) ? AopProxyUtils.getSingletonTarget(bean) : bean;
            Class<?> beanClass = originBean == null ? null : originBean.getClass();
            if (beanClass == null) {
                continue;
            }

            // 检查是否实现了 ExternalMessageHandler 接口
            if (!ExternalMessageHandler.class.isAssignableFrom(beanClass)) {
                log.error("ExternalMQConsumer class {} must implement ExternalMessageHandler interface", beanClass.getName());
                continue;
            }

            ExternalMQConsumer annotation = beanClass.getAnnotation(ExternalMQConsumer.class);

            // 获取消费者实例
            ExternalMessageHandler messageHandler = (ExternalMessageHandler) bean;

            // 根据集群创建对应的 Consumer
            String nameServer = getNameServer(annotation.cluster());
            createConsumer(annotation.consumerGroup(), annotation.topic(), nameServer, messageHandler);
        }
    }

    private String getNameServer(ExternalMQClusterEnum cluster) {
        return switch (cluster) {
            case MIDDLE -> externalMQProperties.getMiddle().getNameServer();
            case SAAS -> externalMQProperties.getSaas().getNameServer();
        };
    }

    private void createConsumer(String group, String topic, String nameServer, ExternalMessageHandler messageHandler) {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(group);
        consumer.setNamesrvAddr(nameServer);
        try {
            consumer.subscribe(topic, "*");
            
            // 注册消息监听器
            consumer.registerMessageListener((MessageListenerConcurrently) (msgList, context) -> {
                try {
                    log.debug("Receive external messages, topic: {}, size: {}", topic, msgList.size());
                    return messageHandler.handleMessage(msgList);
                } catch (Exception e) {
                    log.error("Handle external messages failed, topic: {}, msgIdList: {}, error: {}",
                        topic, msgList.stream().map(MessageExt::getMsgId).toList(), e.getMessage(), e);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            });

            consumer.start();
            log.info("Create external RocketMQ consumer success, topic: {}, group: {}, nameServer: {}", 
                topic, group, nameServer);
        } catch (Exception e) {
            String errorMsg = String.format("Failed to create external RocketMQ consumer, topic: %s, group: %s, nameServer: %s, error: %s", 
                topic, group, nameServer, e.getMessage());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg, e);
        }
    }
}