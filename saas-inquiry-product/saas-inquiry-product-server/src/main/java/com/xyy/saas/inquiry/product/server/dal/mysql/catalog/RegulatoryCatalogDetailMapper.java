package com.xyy.saas.inquiry.product.server.dal.mysql.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 监管目录明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegulatoryCatalogDetailMapper extends BaseMapperX<RegulatoryCatalogDetailDO> {

    default List<RegulatoryCatalogDetailDO> selectListByCatalogId(Long catalogId) {
        return selectList(RegulatoryCatalogDetailDO::getCatalogId, catalogId);
    }

    default int deleteByCatalogId(Long catalogId) {
        return delete(RegulatoryCatalogDetailDO::getCatalogId, catalogId);
    }

    default PageResult<RegulatoryCatalogDetailDO> selectPage(RegulatoryCatalogDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RegulatoryCatalogDetailDO>()
            .eqIfPresent(RegulatoryCatalogDetailDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(RegulatoryCatalogDetailDO::getProjectCode, reqVO.getProjectCode())
            .likeIfPresent(RegulatoryCatalogDetailDO::getCommonName, reqVO.getCommonName())
            .likeIfPresent(RegulatoryCatalogDetailDO::getBrandName, reqVO.getBrandName())
            .eqIfPresent(RegulatoryCatalogDetailDO::getApprovalNumber, reqVO.getApprovalNumber())
            .likeIfPresent(RegulatoryCatalogDetailDO::getManufacturer, reqVO.getManufacturer())
            .eqIfPresent(RegulatoryCatalogDetailDO::getDisable, reqVO.getDisable())
            .orderByDesc(RegulatoryCatalogDetailDO::getId));
    }

    void batchInsertByXml(List<RegulatoryCatalogDetailDO> list);
}
