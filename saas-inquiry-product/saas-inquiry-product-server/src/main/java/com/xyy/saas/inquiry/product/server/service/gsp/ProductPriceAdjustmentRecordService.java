package com.xyy.saas.inquiry.product.server.service.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 售价调整单 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductPriceAdjustmentRecordService {

    /**
     * 保存或更新售价调整单
     *
     * @param reqVO 请求参数
     * @return 记录ID
     */
    Long saveOrUpdatePriceAdjustment(ProductPriceAdjustmentRecordSaveReqVO reqVO);

    /**
     * 保存售价调整单（商品修改）
     * @param dto
     * @param origin
     * @param applicableTenantIdList
     */
    void savePriceAdjustmentRecord(ProductInfoDto dto, ProductInfoDto origin, List<Long> applicableTenantIdList);

    /**
     * 删除售价调整单
     *
     * @param id 编号
     */
    void deleteProductPriceAdjustmentRecord(Long id);

    /**
     * 获得售价调整单
     *
     * @param id 编号
     * @return 售价调整单
     */
    ProductPriceAdjustmentRecordDO getProductPriceAdjustmentRecord(Long id);

    /**
     * 获得售价调整单分页
     *
     * @param pageReqVO 分页查询
     * @return 售价调整单分页
     */
    PageResult<ProductPriceAdjustmentRecordDO> getProductPriceAdjustmentRecordPage(ProductPriceAdjustmentRecordPageReqVO pageReqVO);

    // ==================== 子表（售价调整单明细） ====================

    /**
     * 获得售价调整单明细分页
     *
     * @param pageReqVO 分页查询
     * @param recordPref 单据编号
     * @return 售价调整单明细分页
     */
    PageResult<ProductPriceAdjustmentDetailDO> getProductPriceAdjustmentDetailPage(PageParam pageReqVO, String recordPref);

    /**
     * 创建售价调整单明细
     *
     * @param productPriceAdjustmentDetail 创建信息
     * @return 编号
     */
    Long createProductPriceAdjustmentDetail(@Valid ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail);

    /**
     * 更新售价调整单明细
     *
     * @param productPriceAdjustmentDetail 更新信息
     */
    void updateProductPriceAdjustmentDetail(@Valid ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail);

    /**
     * 删除售价调整单明细
     *
     * @param id 编号
     */
    void deleteProductPriceAdjustmentDetail(Long id);

	/**
	 * 获得售价调整单明细
	 *
	 * @param id 编号
     * @return 售价调整单明细
	 */
    ProductPriceAdjustmentDetailDO getProductPriceAdjustmentDetail(Long id);

}