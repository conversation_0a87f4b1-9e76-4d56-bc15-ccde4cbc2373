package com.xyy.saas.inquiry.product.server.service.gsp;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_PRICE_ADJUSTMENT_DETAIL_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.consts.ProductConstant.*;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentDetailSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductPriceAdjustmentDetailMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductPriceAdjustmentRecordMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 售价调整单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductPriceAdjustmentRecordServiceImpl implements ProductPriceAdjustmentRecordService {

    @Resource
    private ProductPriceAdjustmentRecordMapper productPriceAdjustmentRecordMapper;
    @Resource
    private ProductPriceAdjustmentDetailMapper productPriceAdjustmentDetailMapper;

    @Resource
    private ProductInfoMapper productInfoMapper;
    @Resource
    private ProductUseInfoMapper productUseInfoMapper;

    @Resource
    private TenantApi tenantApi;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdatePriceAdjustment(ProductPriceAdjustmentRecordSaveReqVO reqVO) {
        // 保存或更新主表 和 明细
        Long id =  reqVO.getId();
        ProductPriceAdjustmentRecordDO recordDO = id == null
            ? createProductPriceAdjustmentRecordAndDetail(reqVO)
            : updateProductPriceAdjustmentRecordAndDetail(reqVO);

        // 如果是提交，则发起工作流
        // if (Boolean.TRUE.equals(reqVO.getSubmit())) {
        //     BpmBusinessRelationDto businessDto = new BpmBusinessRelationDto()
        //         .setBusinessType(BpmBusinessTypeEnum.PRODUCT_PRICE_ADJUSTMENT_APPROVE.code)
        //         .setBusinessPref(recordDO.getPref());
        //     bpmBusinessRelationService.createProcessInstance(SecurityFrameworkUtils.getLoginUserId(), businessDto);
        // }

        return recordDO.getId();
    }



    /**
     * 新增主单和明细
     * @param createReqVO
     * @return
     */
    private ProductPriceAdjustmentRecordDO createProductPriceAdjustmentRecordAndDetail(ProductPriceAdjustmentRecordSaveReqVO createReqVO) {
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        // 插入
        ProductPriceAdjustmentRecordDO record = BeanUtils.toBean(createReqVO, ProductPriceAdjustmentRecordDO.class);
        record.setPref(PrefUtil.getProductPriceAdjustmentPref());
        record.setTenantId(tenantId);
        record.setCreateTime(LocalDateTime.now());
        record.setApplicableTenantIds(createReqVO.getApplicableTenantIdList());

        // 提交：记录状态为已提交
        if (createReqVO.getApprovalStatus() != null) {
            record.setApprovalStatus(createReqVO.getApprovalStatus());
        } else if (Boolean.TRUE.equals(createReqVO.getSubmit())) {
            record.setApprovalStatus(ApprovalStatus.RUNNING);
        } else {
            record.setApprovalStatus(ApprovalStatus.NOT_START);
        }
        // 插入主表
        productPriceAdjustmentRecordMapper.insert(record);
        // 保存明细
        saveAndUpdateDetail(record, createReqVO.getDetails());
        // 返回
        return record;
    }

    /**
     * 更新主单和明细
     * @param updateReqVO
     */
    private ProductPriceAdjustmentRecordDO updateProductPriceAdjustmentRecordAndDetail(ProductPriceAdjustmentRecordSaveReqVO updateReqVO) {
        // 校验存在
        ProductPriceAdjustmentRecordDO record = validateProductPriceAdjustmentRecordExists(updateReqVO.getId());
        // 提交：记录状态为已提交（防止重复提交）
        if (Boolean.TRUE.equals(updateReqVO.getSubmit()) && Objects.equals(ApprovalStatus.NOT_START, record.getApprovalStatus())) {
            record.setApprovalStatus(ApprovalStatus.RUNNING);
        }
        // 更新主表
        ProductPriceAdjustmentRecordDO updateObj = BeanUtils.toBean(updateReqVO, ProductPriceAdjustmentRecordDO.class);
        productPriceAdjustmentRecordMapper.updateById(updateObj);
        // 更新明细
        saveAndUpdateDetail(record, updateReqVO.getDetails());
        return updateObj;
    }

    /**
     * 保存或更新明细
     * @param record
     * @param details
     */
    private void saveAndUpdateDetail(ProductPriceAdjustmentRecordDO record, List<ProductPriceAdjustmentDetailSaveReqVO> details) {
        if (record == null || CollUtil.isEmpty(details)) {
            return;
        }
        // 总部租户id
        TenantDto tenant = tenantApi.getTenant(record.getTenantId());
        Long headTenantId = tenant.getHeadTenantId();

        List<ProductPriceAdjustmentDetailDO> insertList = new ArrayList<>();
        List<ProductPriceAdjustmentDetailDO> updateList = new ArrayList<>();

        List<Long> applicableTenantIds = record.getApplicableTenantIds();
        // 查询商品使用信息, 按照商品编码，租户id分组
        List<String> productPrefList = details.stream().map(ProductPriceAdjustmentDetailSaveReqVO::getProductPref).filter(Objects::nonNull).distinct().toList();
        Map<String, Map<Long, ProductUseInfoDO>> useInfoMap = productUseInfoMapper.selectList(applicableTenantIds, productPrefList).stream().collect(
                Collectors.groupingBy(ProductUseInfoDO::getProductPref,
                Collectors.toMap(ProductUseInfoDO::getTenantId, Function.identity(), (old, curr) -> curr)));

        // 商品外码映射
        Map<String, String> productInfoMap = productInfoMapper.selectList(ProductInfoDO::getPref, productPrefList).stream().collect(
                Collectors.toMap(ProductInfoDO::getPref, ProductInfoDO::getShowPref, (old, curr) -> curr));

        applicableTenantIds.forEach(tenantId -> {
            for (ProductPriceAdjustmentDetailSaveReqVO detail : details) {
                ProductPriceAdjustmentDetailDO detailDO = BeanUtils.toBean(detail, ProductPriceAdjustmentDetailDO.class);
                if (detail.getId() == null) {
                    insertList.add(detailDO);
                } else {
                    updateList.add(detailDO);
                }
            }

            // 组装主单信息 + 商品信息
            insertList.forEach(detail -> {
                detail.setRecordPref(record.getPref());
                detail.setTenantId(tenantId);
                // 不验证商品编码是否存在
                detail.setShowPref(productInfoMap.get(detail.getProductPref()));

                Map<Long, ProductUseInfoDO> map2 = useInfoMap.getOrDefault(detail.getProductPref(), Map.of());
                ProductUseInfoDO useInfoDO = ObjectUtils.defaultIfNull(map2.get(tenantId), map2.get(headTenantId)) ;
                // 适用门店（只记录门店之前的价格，不管总部价格）
                Optional.ofNullable(useInfoDO).ifPresent(useInfo -> {
                    detail.setOldRetailPrice(useInfo.getRetailPrice());
                    detail.setOldMemberPrice(useInfo.getMemberPrice());
                });
            });

        });
        if (CollUtil.isNotEmpty(insertList)) {
            productPriceAdjustmentDetailMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            productPriceAdjustmentDetailMapper.updateBatch(updateList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePriceAdjustmentRecord(ProductInfoDto dto, ProductInfoDto origin, List<Long> applicableTenantIdList) {
        if (dto == null || dto.getUseInfo() == null || origin == null || origin.getUseInfo() == null || CollectionUtils.isEmpty(applicableTenantIdList)) {
            return;
        }
        ProductUseInfoDto useInfo = dto.getUseInfo();
        if (useInfo.getRetailPrice() == null && useInfo.getMemberPrice() == null) {
            return;
        }
        if (!ProductUtil.isPriceChanged(dto, origin)) {
            return;
        }
        ProductPriceAdjustmentDetailSaveReqVO detail = new ProductPriceAdjustmentDetailSaveReqVO()
            .setProductPref(origin.getPref())
            .setNewRetailPrice(ObjectUtils.defaultIfNull(useInfo.getRetailPrice(), origin.getUseInfo().getRetailPrice()))
            .setNewMemberPrice(ObjectUtils.defaultIfNull(useInfo.getMemberPrice(), origin.getUseInfo().getMemberPrice()));

        ProductPriceAdjustmentRecordSaveReqVO reqVO = new ProductPriceAdjustmentRecordSaveReqVO()
            .setApprovalStatus(ApprovalStatus.UN_NEED_TODO)
            .setAdjustmentReason("系统生成")
            .setTenantId(dto.getTenantId())
            .setApplicableTenantIdList(applicableTenantIdList)
            .setDetails(List.of(detail));

        this.saveOrUpdatePriceAdjustment(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductPriceAdjustmentRecord(Long id) {
        // 校验存在
        ProductPriceAdjustmentRecordDO exist = validateProductPriceAdjustmentRecordExists(id);
        // 删除
        productPriceAdjustmentRecordMapper.deleteById(id);

        // 删除子表
        deleteProductPriceAdjustmentDetailByRecordPref(exist.getPref());

        // TODO 取消审批流
    }

    private ProductPriceAdjustmentRecordDO validateProductPriceAdjustmentRecordExists(Long id) {
        ProductPriceAdjustmentRecordDO record = productPriceAdjustmentRecordMapper.selectById(id);
        if (record == null) {
            throw exception(PRODUCT_PRICE_ADJUSTMENT_RECORD_NOT_EXISTS);
        }
        return record;
    }

    @Override
    public ProductPriceAdjustmentRecordDO getProductPriceAdjustmentRecord(Long id) {
        return productPriceAdjustmentRecordMapper.selectById(id);
    }

    @Override
    public PageResult<ProductPriceAdjustmentRecordDO> getProductPriceAdjustmentRecordPage(ProductPriceAdjustmentRecordPageReqVO pageReqVO) {
        return productPriceAdjustmentRecordMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（售价调整单明细） ====================

    @Override
    public PageResult<ProductPriceAdjustmentDetailDO> getProductPriceAdjustmentDetailPage(PageParam pageReqVO, String recordPref) {
        return productPriceAdjustmentDetailMapper.selectPage(pageReqVO, recordPref);
    }

    @Override
    public Long createProductPriceAdjustmentDetail(ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail) {
        productPriceAdjustmentDetailMapper.insert(productPriceAdjustmentDetail);
        return productPriceAdjustmentDetail.getId();
    }

    @Override
    public void updateProductPriceAdjustmentDetail(ProductPriceAdjustmentDetailDO productPriceAdjustmentDetail) {
        // 校验存在
        validateProductPriceAdjustmentDetailExists(productPriceAdjustmentDetail.getId());
        // 更新
        productPriceAdjustmentDetail.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        productPriceAdjustmentDetailMapper.updateById(productPriceAdjustmentDetail);
    }

    @Override
    public void deleteProductPriceAdjustmentDetail(Long id) {
        // 校验存在
        validateProductPriceAdjustmentDetailExists(id);
        // 删除
        productPriceAdjustmentDetailMapper.deleteById(id);
    }

    @Override
    public ProductPriceAdjustmentDetailDO getProductPriceAdjustmentDetail(Long id) {
        return productPriceAdjustmentDetailMapper.selectById(id);
    }

    private void validateProductPriceAdjustmentDetailExists(Long id) {
        if (productPriceAdjustmentDetailMapper.selectById(id) == null) {
            throw exception(PRODUCT_PRICE_ADJUSTMENT_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteProductPriceAdjustmentDetailByRecordPref(String recordPref) {
        productPriceAdjustmentDetailMapper.deleteByRecordPref(recordPref);
    }

}