package com.xyy.saas.inquiry.product.server.service.transfer;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_TRANSFER_RECORD_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.transfer.ProductTransferRecordMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 商品流转记录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ProductTransferRecordServiceImpl implements ProductTransferRecordService {

    @Resource
    private ProductTransferRecordMapper productTransferRecordMapper;
    @Resource
    private ProductInfoMapper productInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductTransferRecordDO createProductTransferRecord(ProductTransferRecordSaveReqVO createReqVO) {
        ProductTransferRecordDO disableReq = new ProductTransferRecordDO()
            .setProductPref(createReqVO.getProductPref())
            .setType(createReqVO.getType())
            .setSourceTenantId(createReqVO.getSourceTenantId());
        productTransferRecordMapper.disableByProductPref(disableReq);
        // 插入
        ProductTransferRecordDO productTransferRecord = BeanUtils.toBean(createReqVO, ProductTransferRecordDO.class);
        productTransferRecordMapper.insert(productTransferRecord);
        // 返回
        return productTransferRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductTransferRecord(ProductTransferRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateProductTransferRecordExists(updateReqVO.getId());
        // 更新
        ProductTransferRecordDO updateObj = BeanUtils.toBean(updateReqVO, ProductTransferRecordDO.class);
        productTransferRecordMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableProductTransferRecord(Long id) {
        productTransferRecordMapper.disableById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductTransferRecord(Long id) {
        // 校验存在
        // validateProductTransferRecordExists(id);
        // 删除
        productTransferRecordMapper.deleteById(id);
    }

    private void validateProductTransferRecordExists(Long id) {
        if (productTransferRecordMapper.selectById(id) == null) {
            throw exception(PRODUCT_TRANSFER_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public ProductTransferRecordDO getProductTransferRecord(Long id) {
        return productTransferRecordMapper.selectById(id);
    }

    @Override
    public PageResult<ProductTransferRecordDO> getProductTransferRecordPage(ProductTransferRecordPageReqVO pageReqVO) {
        return productTransferRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductTransferRecordDO> listPresentTransferRecord(ProductPresentPageReqVO reqVO) {
        return productTransferRecordMapper.listTransferRecord(ProductTransferTypeEnum.REPORT_MID.code, reqVO);
    }

    /**
     * 获取最新提报记录
     *
     * @param pageReqVO 分页查询 条件
     * @return
     */
    @Override
    public PageResult<ProductPresentRecordRespVO> getLatestPresentPage(ProductPresentPageReqVO pageReqVO) {
        // statusList 转换
        if (pageReqVO.getApproveStatus() != null) {
            pageReqVO.setStatusList(ProductTransferStatusEnum.getCodeListByApproveStatus(pageReqVO.getApproveStatus()));
        }

        IPage<ProductPresentRecordRespVO> page = productTransferRecordMapper.getLatestPresentPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }

        List<String> productPrefList = page.getRecords().stream().map(ProductPresentRecordRespVO::getProductPref).toList();
        Map<String, ProductInfoDO2> productMap = productInfoMapper.listByIdOrPref(null, productPrefList, null, null)
            .stream().collect(Collectors.toMap(ProductInfoDO2::getPref, Function.identity(), (a, b) -> b));

        List<ProductPresentRecordRespVO> respVOList = page.getRecords().stream()
            .map(i -> i.assembleFullInfo(productMap.get(i.getProductPref())))
            .toList();
        return new PageResult<>(respVOList, page.getTotal());
    }

    /**
     * 获取最新提报记录（移动端需要统计待审核、已通过、已驳回数量）
     *
     * @param pageReqVO 分页查询 条件
     * @return 商品流转记录分页
     */
    @Override
    public ProductPresentPageRespVO getLatestPresentPageWithStatistics(ProductPresentPageReqVO pageReqVO) {
        PageResult<ProductPresentRecordRespVO> page = getLatestPresentPage(pageReqVO);

        ProductPresentPageRespVO respVO = new ProductPresentPageRespVO().setPage(page);
        // 统计状态数量
        pageReqVO.setStatusList(null);
        List<Map<String, Object>> groupByStatus = productTransferRecordMapper.getLatestPresentGroupByStatus(pageReqVO);
        if (CollectionUtils.isNotEmpty(groupByStatus)) {
            // 遍历累加
            groupByStatus.forEach(i -> {
                Integer approveStatus = ProductTransferStatusEnum.getByCode((Integer) i.get("status")).map(x -> x.approveStatus).orElse(null);
                long cnt = Optional.ofNullable((Long) i.get("count")).orElse(0L);
                // 待审批
                if (Objects.equals(approveStatus, ProductTransferStatusEnum.INIT.approveStatus)) {
                    respVO.setApprovingCount(respVO.getApprovingCount() + cnt);
                }
                // 已通过
                else if (Objects.equals(approveStatus, ProductTransferStatusEnum.SUCCESS.approveStatus)) {
                    respVO.setPassedCount(respVO.getPassedCount() + cnt);
                }
                // 已驳回
                else if (Objects.equals(approveStatus, ProductTransferStatusEnum.MID_AUDIT_REJECT.approveStatus)) {
                    respVO.setRejectedCount(respVO.getRejectedCount() + cnt);
                }
            });
        }
        return respVO;
    }

    /**
     * 获取最新提报记录
     *
     * @param id
     * @return 商品流转记录分页
     */
    @Override
    public ProductPresentRecordRespVO getLatestPresentById(Long id) {
        ProductPresentPageReqVO reqVO = new ProductPresentPageReqVO().setIdList(List.of(id));
        PageResult<ProductPresentRecordRespVO> page = this.getLatestPresentPage(reqVO);
        return page == null || CollectionUtils.isEmpty(page.getList()) ? null : page.getList().getFirst();
    }
}