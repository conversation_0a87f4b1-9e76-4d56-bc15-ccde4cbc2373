package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 质量变更申请明细记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductQualityChangeDetailRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20566")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32264")
    @ExcelProperty("单据编号")
    private String recordPref;

    @Schema(description = "变更类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("变更类型")
    private Integer type;

    @Schema(description = "商品/供应商编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "6253")
    @ExcelProperty("商品/供应商编码")
    private String itemPref;

    @Schema(description = "外码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外码")
    private String itemCode;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("名称")
    private String itemName;

    @Schema(description = "变更内容（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更内容（JSON格式）")
    private String content;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}