package com.xyy.saas.inquiry.product.server.service.bpm;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationCancelReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;

/**
 * 审批流关联业务 Service 接口
 *
 * <AUTHOR>
 */
public interface BpmBusinessRelationService {

    /**
     * 创建申请
     *
     * @param userId 用户编号
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProcessInstance(Long userId, BpmBusinessRelationDto createReqVO);

    /**
     * 更新申请的状态
     *
     * @param bpmBusinessRelationDto
     */
    BpmBusinessRelationDO updateProcessInstanceStatus(BpmBusinessRelationDto bpmBusinessRelationDto);

    /**
     * 删除申请（关联审批流删除）
     */
    void deleteProcessInstance(BpmBusinessRelationCancelReqVO cancelReqVO);

    /**
     * 获得审批流关联业务
     *
     * @param id 编号
     * @return 审批流关联业务
     */
    BpmBusinessRelationDO getBpmBusinessRelation(Long id);

    /**
     * 获得审批流关联业务分页
     *
     * @param pageReqVO 分页查询
     * @return 审批流关联业务分页
     */
    PageResult<BpmBusinessRelationDO> getBpmBusinessRelationPage(BpmBusinessRelationPageReqVO pageReqVO);

}