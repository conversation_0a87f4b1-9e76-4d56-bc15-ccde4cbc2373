package com.xyy.saas.inquiry.product.server.dal.dataobject.catalog;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 目录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_catalog")
@KeySequence("saas_catalog_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 目录编码
     */
    private String pref;
    /**
     * 目录名称
     */
    private String name;
    /**
     * 业务类型（1:医保 3:互联网监管）
     */
    private Integer type;
    /**
     * 项目编码类型（11:医保项目编码 31:自建标准库ID 32:中台标准库ID）
     */
    private Integer projectCodeType;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 版本编码
     */
    private String versionCode;
    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 上传文件链接
     */
    private String uploadUrl;
    /**
     * 是否需要下载
     */
    private Boolean needDownload;
    /**
     * 下载文件链接
     */
    private String downloadUrl;
    /**
     * 目录总数
     */
    private Integer totalCount;
    /**
     * 已匹配数
     */
    private Integer matchedCount;
    /**
     * 未匹配数
     */
    private Integer unmatchedCount;
    /**
     * 是否禁用，默认否
     */
    private Boolean disable;
    /**
     * 环境（1测试、2灰度、3上线）
     */
    private Integer env;
    /**
     * 备注
     */
    private String remark;

}