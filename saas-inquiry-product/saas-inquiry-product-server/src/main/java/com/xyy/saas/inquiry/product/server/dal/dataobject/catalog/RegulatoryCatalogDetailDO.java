package com.xyy.saas.inquiry.product.server.dal.dataobject.catalog;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 监管目录明细 DO
 *
 * <AUTHOR>
 */
@TableName("saas_regulatory_catalog_detail")
@KeySequence("saas_regulatory_catalog_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegulatoryCatalogDetailDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 目录ID
     */
    private Long catalogId;
    /**
     * 项目编码（标准库ID）
     */
    private Long projectCode;
    /**
     * 通用名
     */
    private String commonName;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 规格/型号
     */
    private String spec;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 是否禁用，默认否
     */
    private Boolean disable;
    /**
     * 备注
     */
    private String remark;

}